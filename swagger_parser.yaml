swagger_parser:
  # You must provide the file path and/or url to the OpenApi schema.

  # Sets the OpenApi schema path directory for api definition.
  # schema_path: openapi.json

  # Sets the url of the OpenApi schema.
  schema_url: https://langda.cucumbers.fyi/munseo
 
  # Required. Sets output directory for generated files (Clients and DTOs).
  output_directory: lib/api

  # Optional. Set API name for folder and export file
  # If not specified, the file name is used.
  name: null

  # Optional. Sets the programming language.
  # Current available languages are: dart, kotlin.
  language: dart

  # Optional (dart only).
  # Current available serializers are: json_serializable, freezed, dart_mappable.
  json_serializer: json_serializable

  # Optional. Set default content-type for all requests.
  default_content_type: "application/json"

  # Optional (dart only).
  # Support @Extras annotation for interceptors.
  # If the value is 'true', then the annotation will be added to all requests.
  extras_parameter_by_default: false

  # Optional (dart only).
  # Support @DioOptions annotation for interceptors.
  # If the value is 'true', then the annotation will be added to all requests.
  dio_options_parameter_by_default: false

  # Optional (dart only). Set 'true' to generate root client
  # with interface and all clients instances.
  root_client: true

  # Optional (dart only). Set root client name.
  root_client_name: RestClient

  # Optional (dart only). Set 'true' to generate export file.
  export_file: true

  # Optional. Set to 'true' to put the all api in its folder.
  put_in_folder: false

  # Optional. Set 'true' to put all clients in clients folder.
  put_clients_in_folder: true

  # Optional. Set to 'true' to squash all clients in one client.
  merge_clients: false

  # Optional. Set postfix for Client class and file.
  client_postfix: Client

  # Optional. Set 'true' to use only the endpoint path for the method name.
  # Set 'false' to use operationId
  path_method_name: false

  # Optional (dart only). Set 'true' to include toJson() in enums. 
  # If set to false, serialization will use .name instead.
  enums_to_json: false

  # Optional. Set 'true' to set enum prefix from parent component.
  enums_parent_prefix: true

  # Optional (dart only). Set 'true' to maintain backwards compatibility 
  # when adding new values on the backend.
  unknown_enum_value: true

  # Optional. Set 'false' to not put a comment at the beginning of the generated files.
  mark_files_as_generated: true

  # Optional (dart only). Set 'true' to wrap all request return types with HttpResponse.
  original_http_response: false

  # Optional. Set regex replacement rules for the names of the generated classes/enums.
  # All rules are applied in order.
  # replacement_rules:
  #   # Example of rule
  #   - pattern: "[0-9]+"
  #     replacement: ""

  # Optional. Skip parameters with names.
  # skipped_parameters:
    # - 'X-Some-Token'