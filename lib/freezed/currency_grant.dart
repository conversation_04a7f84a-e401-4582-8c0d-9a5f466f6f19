// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:langda/api/models/achievement_grant_type.dart';
import 'package:langda/api/models/achievement_id.dart';
import 'package:langda/api/models/achievement_type.dart';
import 'package:langda/api/models/periodic_grant_period.dart';
import 'package:langda/api/models/periodic_grant_type.dart';
import 'package:langda/api/models/streak_grant_type.dart';
import 'package:langda/api/models/streak_milestone.dart';
import 'package:langda/api/models/subscription_type.dart';

part 'currency_grant.freezed.dart';
part 'currency_grant.g.dart';

@Freezed(unionKey: 'type')
sealed class CurrencyGrant with _$CurrencyGrant {
  @FreezedUnionValue('streak_milestone')
  const factory CurrencyGrant.streakMilestone({
    required int amount,
    required StreakGrantType type,
    required StreakMilestone milestone,
  }) = CurrencyGrantStreakMilestone;

  @FreezedUnionValue('achievement')
  const factory CurrencyGrant.achievement({
    required int amount,
    required AchievementGrantType type,
    required AchievementId achievementId,
    required AchievementType achievementType,
  }) = CurrencyGrantAchievement;

  @FreezedUnionValue('periodic_reward')
  const factory CurrencyGrant.periodicReward({
    required int amount,
    required PeriodicGrantType type,
    required PeriodicGrantPeriod period,
    required SubscriptionType subscriptionType,
  }) = CurrencyGrantPeriodicReward;

  factory CurrencyGrant.fromJson(Map<String, Object?> json) =>
      _$CurrencyGrantFromJson(json);
}
