// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'currency_grant.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CurrencyGrant _$CurrencyGrantFromJson(Map<String, dynamic> json) {
  switch (json['type']) {
    case 'streak_milestone':
      return CurrencyGrantStreakMilestone.fromJson(json);
    case 'achievement':
      return CurrencyGrantAchievement.fromJson(json);
    case 'periodic_reward':
      return CurrencyGrantPeriodicReward.fromJson(json);

    default:
      throw CheckedFromJsonException(json, 'type', 'CurrencyGrant',
          'Invalid union type "${json['type']}"!');
  }
}

/// @nodoc
mixin _$CurrencyGrant {
  int get amount => throw _privateConstructorUsedError;
  Enum get type => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            int amount, StreakGrantType type, StreakMilestone milestone)
        streakMilestone,
    required TResult Function(int amount, AchievementGrantType type,
            AchievementId achievementId, AchievementType achievementType)
        achievement,
    required TResult Function(int amount, PeriodicGrantType type,
            PeriodicGrantPeriod period, SubscriptionType subscriptionType)
        periodicReward,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            int amount, StreakGrantType type, StreakMilestone milestone)?
        streakMilestone,
    TResult? Function(int amount, AchievementGrantType type,
            AchievementId achievementId, AchievementType achievementType)?
        achievement,
    TResult? Function(int amount, PeriodicGrantType type,
            PeriodicGrantPeriod period, SubscriptionType subscriptionType)?
        periodicReward,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            int amount, StreakGrantType type, StreakMilestone milestone)?
        streakMilestone,
    TResult Function(int amount, AchievementGrantType type,
            AchievementId achievementId, AchievementType achievementType)?
        achievement,
    TResult Function(int amount, PeriodicGrantType type,
            PeriodicGrantPeriod period, SubscriptionType subscriptionType)?
        periodicReward,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CurrencyGrantStreakMilestone value)
        streakMilestone,
    required TResult Function(CurrencyGrantAchievement value) achievement,
    required TResult Function(CurrencyGrantPeriodicReward value) periodicReward,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CurrencyGrantStreakMilestone value)? streakMilestone,
    TResult? Function(CurrencyGrantAchievement value)? achievement,
    TResult? Function(CurrencyGrantPeriodicReward value)? periodicReward,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CurrencyGrantStreakMilestone value)? streakMilestone,
    TResult Function(CurrencyGrantAchievement value)? achievement,
    TResult Function(CurrencyGrantPeriodicReward value)? periodicReward,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Serializes this CurrencyGrant to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CurrencyGrant
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CurrencyGrantCopyWith<CurrencyGrant> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CurrencyGrantCopyWith<$Res> {
  factory $CurrencyGrantCopyWith(
          CurrencyGrant value, $Res Function(CurrencyGrant) then) =
      _$CurrencyGrantCopyWithImpl<$Res, CurrencyGrant>;
  @useResult
  $Res call({int amount});
}

/// @nodoc
class _$CurrencyGrantCopyWithImpl<$Res, $Val extends CurrencyGrant>
    implements $CurrencyGrantCopyWith<$Res> {
  _$CurrencyGrantCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CurrencyGrant
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
  }) {
    return _then(_value.copyWith(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CurrencyGrantStreakMilestoneImplCopyWith<$Res>
    implements $CurrencyGrantCopyWith<$Res> {
  factory _$$CurrencyGrantStreakMilestoneImplCopyWith(
          _$CurrencyGrantStreakMilestoneImpl value,
          $Res Function(_$CurrencyGrantStreakMilestoneImpl) then) =
      __$$CurrencyGrantStreakMilestoneImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int amount, StreakGrantType type, StreakMilestone milestone});
}

/// @nodoc
class __$$CurrencyGrantStreakMilestoneImplCopyWithImpl<$Res>
    extends _$CurrencyGrantCopyWithImpl<$Res,
        _$CurrencyGrantStreakMilestoneImpl>
    implements _$$CurrencyGrantStreakMilestoneImplCopyWith<$Res> {
  __$$CurrencyGrantStreakMilestoneImplCopyWithImpl(
      _$CurrencyGrantStreakMilestoneImpl _value,
      $Res Function(_$CurrencyGrantStreakMilestoneImpl) _then)
      : super(_value, _then);

  /// Create a copy of CurrencyGrant
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? type = null,
    Object? milestone = null,
  }) {
    return _then(_$CurrencyGrantStreakMilestoneImpl(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as StreakGrantType,
      milestone: null == milestone
          ? _value.milestone
          : milestone // ignore: cast_nullable_to_non_nullable
              as StreakMilestone,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CurrencyGrantStreakMilestoneImpl
    implements CurrencyGrantStreakMilestone {
  const _$CurrencyGrantStreakMilestoneImpl(
      {required this.amount, required this.type, required this.milestone});

  factory _$CurrencyGrantStreakMilestoneImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CurrencyGrantStreakMilestoneImplFromJson(json);

  @override
  final int amount;
  @override
  final StreakGrantType type;
  @override
  final StreakMilestone milestone;

  @override
  String toString() {
    return 'CurrencyGrant.streakMilestone(amount: $amount, type: $type, milestone: $milestone)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CurrencyGrantStreakMilestoneImpl &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.milestone, milestone) ||
                other.milestone == milestone));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, amount, type, milestone);

  /// Create a copy of CurrencyGrant
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CurrencyGrantStreakMilestoneImplCopyWith<
          _$CurrencyGrantStreakMilestoneImpl>
      get copyWith => __$$CurrencyGrantStreakMilestoneImplCopyWithImpl<
          _$CurrencyGrantStreakMilestoneImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            int amount, StreakGrantType type, StreakMilestone milestone)
        streakMilestone,
    required TResult Function(int amount, AchievementGrantType type,
            AchievementId achievementId, AchievementType achievementType)
        achievement,
    required TResult Function(int amount, PeriodicGrantType type,
            PeriodicGrantPeriod period, SubscriptionType subscriptionType)
        periodicReward,
  }) {
    return streakMilestone(amount, type, milestone);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            int amount, StreakGrantType type, StreakMilestone milestone)?
        streakMilestone,
    TResult? Function(int amount, AchievementGrantType type,
            AchievementId achievementId, AchievementType achievementType)?
        achievement,
    TResult? Function(int amount, PeriodicGrantType type,
            PeriodicGrantPeriod period, SubscriptionType subscriptionType)?
        periodicReward,
  }) {
    return streakMilestone?.call(amount, type, milestone);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            int amount, StreakGrantType type, StreakMilestone milestone)?
        streakMilestone,
    TResult Function(int amount, AchievementGrantType type,
            AchievementId achievementId, AchievementType achievementType)?
        achievement,
    TResult Function(int amount, PeriodicGrantType type,
            PeriodicGrantPeriod period, SubscriptionType subscriptionType)?
        periodicReward,
    required TResult orElse(),
  }) {
    if (streakMilestone != null) {
      return streakMilestone(amount, type, milestone);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CurrencyGrantStreakMilestone value)
        streakMilestone,
    required TResult Function(CurrencyGrantAchievement value) achievement,
    required TResult Function(CurrencyGrantPeriodicReward value) periodicReward,
  }) {
    return streakMilestone(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CurrencyGrantStreakMilestone value)? streakMilestone,
    TResult? Function(CurrencyGrantAchievement value)? achievement,
    TResult? Function(CurrencyGrantPeriodicReward value)? periodicReward,
  }) {
    return streakMilestone?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CurrencyGrantStreakMilestone value)? streakMilestone,
    TResult Function(CurrencyGrantAchievement value)? achievement,
    TResult Function(CurrencyGrantPeriodicReward value)? periodicReward,
    required TResult orElse(),
  }) {
    if (streakMilestone != null) {
      return streakMilestone(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$CurrencyGrantStreakMilestoneImplToJson(
      this,
    );
  }
}

abstract class CurrencyGrantStreakMilestone implements CurrencyGrant {
  const factory CurrencyGrantStreakMilestone(
          {required final int amount,
          required final StreakGrantType type,
          required final StreakMilestone milestone}) =
      _$CurrencyGrantStreakMilestoneImpl;

  factory CurrencyGrantStreakMilestone.fromJson(Map<String, dynamic> json) =
      _$CurrencyGrantStreakMilestoneImpl.fromJson;

  @override
  int get amount;
  @override
  StreakGrantType get type;
  StreakMilestone get milestone;

  /// Create a copy of CurrencyGrant
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CurrencyGrantStreakMilestoneImplCopyWith<
          _$CurrencyGrantStreakMilestoneImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CurrencyGrantAchievementImplCopyWith<$Res>
    implements $CurrencyGrantCopyWith<$Res> {
  factory _$$CurrencyGrantAchievementImplCopyWith(
          _$CurrencyGrantAchievementImpl value,
          $Res Function(_$CurrencyGrantAchievementImpl) then) =
      __$$CurrencyGrantAchievementImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int amount,
      AchievementGrantType type,
      AchievementId achievementId,
      AchievementType achievementType});
}

/// @nodoc
class __$$CurrencyGrantAchievementImplCopyWithImpl<$Res>
    extends _$CurrencyGrantCopyWithImpl<$Res, _$CurrencyGrantAchievementImpl>
    implements _$$CurrencyGrantAchievementImplCopyWith<$Res> {
  __$$CurrencyGrantAchievementImplCopyWithImpl(
      _$CurrencyGrantAchievementImpl _value,
      $Res Function(_$CurrencyGrantAchievementImpl) _then)
      : super(_value, _then);

  /// Create a copy of CurrencyGrant
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? type = null,
    Object? achievementId = null,
    Object? achievementType = null,
  }) {
    return _then(_$CurrencyGrantAchievementImpl(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as AchievementGrantType,
      achievementId: null == achievementId
          ? _value.achievementId
          : achievementId // ignore: cast_nullable_to_non_nullable
              as AchievementId,
      achievementType: null == achievementType
          ? _value.achievementType
          : achievementType // ignore: cast_nullable_to_non_nullable
              as AchievementType,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CurrencyGrantAchievementImpl implements CurrencyGrantAchievement {
  const _$CurrencyGrantAchievementImpl(
      {required this.amount,
      required this.type,
      required this.achievementId,
      required this.achievementType});

  factory _$CurrencyGrantAchievementImpl.fromJson(Map<String, dynamic> json) =>
      _$$CurrencyGrantAchievementImplFromJson(json);

  @override
  final int amount;
  @override
  final AchievementGrantType type;
  @override
  final AchievementId achievementId;
  @override
  final AchievementType achievementType;

  @override
  String toString() {
    return 'CurrencyGrant.achievement(amount: $amount, type: $type, achievementId: $achievementId, achievementType: $achievementType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CurrencyGrantAchievementImpl &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.achievementId, achievementId) ||
                other.achievementId == achievementId) &&
            (identical(other.achievementType, achievementType) ||
                other.achievementType == achievementType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, amount, type, achievementId, achievementType);

  /// Create a copy of CurrencyGrant
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CurrencyGrantAchievementImplCopyWith<_$CurrencyGrantAchievementImpl>
      get copyWith => __$$CurrencyGrantAchievementImplCopyWithImpl<
          _$CurrencyGrantAchievementImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            int amount, StreakGrantType type, StreakMilestone milestone)
        streakMilestone,
    required TResult Function(int amount, AchievementGrantType type,
            AchievementId achievementId, AchievementType achievementType)
        achievement,
    required TResult Function(int amount, PeriodicGrantType type,
            PeriodicGrantPeriod period, SubscriptionType subscriptionType)
        periodicReward,
  }) {
    return achievement(amount, type, achievementId, achievementType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            int amount, StreakGrantType type, StreakMilestone milestone)?
        streakMilestone,
    TResult? Function(int amount, AchievementGrantType type,
            AchievementId achievementId, AchievementType achievementType)?
        achievement,
    TResult? Function(int amount, PeriodicGrantType type,
            PeriodicGrantPeriod period, SubscriptionType subscriptionType)?
        periodicReward,
  }) {
    return achievement?.call(amount, type, achievementId, achievementType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            int amount, StreakGrantType type, StreakMilestone milestone)?
        streakMilestone,
    TResult Function(int amount, AchievementGrantType type,
            AchievementId achievementId, AchievementType achievementType)?
        achievement,
    TResult Function(int amount, PeriodicGrantType type,
            PeriodicGrantPeriod period, SubscriptionType subscriptionType)?
        periodicReward,
    required TResult orElse(),
  }) {
    if (achievement != null) {
      return achievement(amount, type, achievementId, achievementType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CurrencyGrantStreakMilestone value)
        streakMilestone,
    required TResult Function(CurrencyGrantAchievement value) achievement,
    required TResult Function(CurrencyGrantPeriodicReward value) periodicReward,
  }) {
    return achievement(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CurrencyGrantStreakMilestone value)? streakMilestone,
    TResult? Function(CurrencyGrantAchievement value)? achievement,
    TResult? Function(CurrencyGrantPeriodicReward value)? periodicReward,
  }) {
    return achievement?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CurrencyGrantStreakMilestone value)? streakMilestone,
    TResult Function(CurrencyGrantAchievement value)? achievement,
    TResult Function(CurrencyGrantPeriodicReward value)? periodicReward,
    required TResult orElse(),
  }) {
    if (achievement != null) {
      return achievement(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$CurrencyGrantAchievementImplToJson(
      this,
    );
  }
}

abstract class CurrencyGrantAchievement implements CurrencyGrant {
  const factory CurrencyGrantAchievement(
          {required final int amount,
          required final AchievementGrantType type,
          required final AchievementId achievementId,
          required final AchievementType achievementType}) =
      _$CurrencyGrantAchievementImpl;

  factory CurrencyGrantAchievement.fromJson(Map<String, dynamic> json) =
      _$CurrencyGrantAchievementImpl.fromJson;

  @override
  int get amount;
  @override
  AchievementGrantType get type;
  AchievementId get achievementId;
  AchievementType get achievementType;

  /// Create a copy of CurrencyGrant
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CurrencyGrantAchievementImplCopyWith<_$CurrencyGrantAchievementImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CurrencyGrantPeriodicRewardImplCopyWith<$Res>
    implements $CurrencyGrantCopyWith<$Res> {
  factory _$$CurrencyGrantPeriodicRewardImplCopyWith(
          _$CurrencyGrantPeriodicRewardImpl value,
          $Res Function(_$CurrencyGrantPeriodicRewardImpl) then) =
      __$$CurrencyGrantPeriodicRewardImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int amount,
      PeriodicGrantType type,
      PeriodicGrantPeriod period,
      SubscriptionType subscriptionType});
}

/// @nodoc
class __$$CurrencyGrantPeriodicRewardImplCopyWithImpl<$Res>
    extends _$CurrencyGrantCopyWithImpl<$Res, _$CurrencyGrantPeriodicRewardImpl>
    implements _$$CurrencyGrantPeriodicRewardImplCopyWith<$Res> {
  __$$CurrencyGrantPeriodicRewardImplCopyWithImpl(
      _$CurrencyGrantPeriodicRewardImpl _value,
      $Res Function(_$CurrencyGrantPeriodicRewardImpl) _then)
      : super(_value, _then);

  /// Create a copy of CurrencyGrant
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? type = null,
    Object? period = null,
    Object? subscriptionType = null,
  }) {
    return _then(_$CurrencyGrantPeriodicRewardImpl(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as PeriodicGrantType,
      period: null == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as PeriodicGrantPeriod,
      subscriptionType: null == subscriptionType
          ? _value.subscriptionType
          : subscriptionType // ignore: cast_nullable_to_non_nullable
              as SubscriptionType,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CurrencyGrantPeriodicRewardImpl implements CurrencyGrantPeriodicReward {
  const _$CurrencyGrantPeriodicRewardImpl(
      {required this.amount,
      required this.type,
      required this.period,
      required this.subscriptionType});

  factory _$CurrencyGrantPeriodicRewardImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CurrencyGrantPeriodicRewardImplFromJson(json);

  @override
  final int amount;
  @override
  final PeriodicGrantType type;
  @override
  final PeriodicGrantPeriod period;
  @override
  final SubscriptionType subscriptionType;

  @override
  String toString() {
    return 'CurrencyGrant.periodicReward(amount: $amount, type: $type, period: $period, subscriptionType: $subscriptionType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CurrencyGrantPeriodicRewardImpl &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.period, period) || other.period == period) &&
            (identical(other.subscriptionType, subscriptionType) ||
                other.subscriptionType == subscriptionType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, amount, type, period, subscriptionType);

  /// Create a copy of CurrencyGrant
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CurrencyGrantPeriodicRewardImplCopyWith<_$CurrencyGrantPeriodicRewardImpl>
      get copyWith => __$$CurrencyGrantPeriodicRewardImplCopyWithImpl<
          _$CurrencyGrantPeriodicRewardImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            int amount, StreakGrantType type, StreakMilestone milestone)
        streakMilestone,
    required TResult Function(int amount, AchievementGrantType type,
            AchievementId achievementId, AchievementType achievementType)
        achievement,
    required TResult Function(int amount, PeriodicGrantType type,
            PeriodicGrantPeriod period, SubscriptionType subscriptionType)
        periodicReward,
  }) {
    return periodicReward(amount, type, period, subscriptionType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            int amount, StreakGrantType type, StreakMilestone milestone)?
        streakMilestone,
    TResult? Function(int amount, AchievementGrantType type,
            AchievementId achievementId, AchievementType achievementType)?
        achievement,
    TResult? Function(int amount, PeriodicGrantType type,
            PeriodicGrantPeriod period, SubscriptionType subscriptionType)?
        periodicReward,
  }) {
    return periodicReward?.call(amount, type, period, subscriptionType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            int amount, StreakGrantType type, StreakMilestone milestone)?
        streakMilestone,
    TResult Function(int amount, AchievementGrantType type,
            AchievementId achievementId, AchievementType achievementType)?
        achievement,
    TResult Function(int amount, PeriodicGrantType type,
            PeriodicGrantPeriod period, SubscriptionType subscriptionType)?
        periodicReward,
    required TResult orElse(),
  }) {
    if (periodicReward != null) {
      return periodicReward(amount, type, period, subscriptionType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CurrencyGrantStreakMilestone value)
        streakMilestone,
    required TResult Function(CurrencyGrantAchievement value) achievement,
    required TResult Function(CurrencyGrantPeriodicReward value) periodicReward,
  }) {
    return periodicReward(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CurrencyGrantStreakMilestone value)? streakMilestone,
    TResult? Function(CurrencyGrantAchievement value)? achievement,
    TResult? Function(CurrencyGrantPeriodicReward value)? periodicReward,
  }) {
    return periodicReward?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CurrencyGrantStreakMilestone value)? streakMilestone,
    TResult Function(CurrencyGrantAchievement value)? achievement,
    TResult Function(CurrencyGrantPeriodicReward value)? periodicReward,
    required TResult orElse(),
  }) {
    if (periodicReward != null) {
      return periodicReward(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$CurrencyGrantPeriodicRewardImplToJson(
      this,
    );
  }
}

abstract class CurrencyGrantPeriodicReward implements CurrencyGrant {
  const factory CurrencyGrantPeriodicReward(
          {required final int amount,
          required final PeriodicGrantType type,
          required final PeriodicGrantPeriod period,
          required final SubscriptionType subscriptionType}) =
      _$CurrencyGrantPeriodicRewardImpl;

  factory CurrencyGrantPeriodicReward.fromJson(Map<String, dynamic> json) =
      _$CurrencyGrantPeriodicRewardImpl.fromJson;

  @override
  int get amount;
  @override
  PeriodicGrantType get type;
  PeriodicGrantPeriod get period;
  SubscriptionType get subscriptionType;

  /// Create a copy of CurrencyGrant
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CurrencyGrantPeriodicRewardImplCopyWith<_$CurrencyGrantPeriodicRewardImpl>
      get copyWith => throw _privateConstructorUsedError;
}
