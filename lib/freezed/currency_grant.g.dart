// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'currency_grant.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CurrencyGrantStreakMilestoneImpl _$$CurrencyGrantStreakMilestoneImplFromJson(
        Map<String, dynamic> json) =>
    _$CurrencyGrantStreakMilestoneImpl(
      amount: (json['amount'] as num).toInt(),
      type: StreakGrantType.from<PERSON>son(json['type'] as String),
      milestone: StreakMilestone.from<PERSON>son(json['milestone'] as String),
    );

Map<String, dynamic> _$$CurrencyGrantStreakMilestoneImplToJson(
        _$CurrencyGrantStreakMilestoneImpl instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'type': _$StreakGrantTypeEnumMap[instance.type]!,
      'milestone': _$StreakMilestoneEnumMap[instance.milestone]!,
    };

const _$StreakGrantTypeEnumMap = {
  StreakGrantType.streakMilestone: 'streak_milestone',
  StreakGrantType.$unknown: r'$unknown',
};

const _$StreakMilestoneEnumMap = {
  StreakMilestone.value5Day: '5_day',
  StreakMilestone.value10Day: '10_day',
  StreakMilestone.value30Day: '30_day',
  StreakMilestone.$unknown: r'$unknown',
};

_$CurrencyGrantAchievementImpl _$$CurrencyGrantAchievementImplFromJson(
        Map<String, dynamic> json) =>
    _$CurrencyGrantAchievementImpl(
      amount: (json['amount'] as num).toInt(),
      type: AchievementGrantType.fromJson(json['type'] as String),
      achievementId: AchievementId.fromJson(json['achievementId'] as String),
      achievementType:
          AchievementType.fromJson(json['achievementType'] as String),
    );

Map<String, dynamic> _$$CurrencyGrantAchievementImplToJson(
        _$CurrencyGrantAchievementImpl instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'type': _$AchievementGrantTypeEnumMap[instance.type]!,
      'achievementId': _$AchievementIdEnumMap[instance.achievementId]!,
      'achievementType': _$AchievementTypeEnumMap[instance.achievementType]!,
    };

const _$AchievementGrantTypeEnumMap = {
  AchievementGrantType.achievement: 'achievement',
  AchievementGrantType.$unknown: r'$unknown',
};

const _$AchievementIdEnumMap = {
  AchievementId.diaryComplete: 'diary_complete',
  AchievementId.perfectScore: 'perfect_score',
  AchievementId.$unknown: r'$unknown',
};

const _$AchievementTypeEnumMap = {
  AchievementType.oneTime: 'one_time',
  AchievementType.repeatable: 'repeatable',
  AchievementType.$unknown: r'$unknown',
};

_$CurrencyGrantPeriodicRewardImpl _$$CurrencyGrantPeriodicRewardImplFromJson(
        Map<String, dynamic> json) =>
    _$CurrencyGrantPeriodicRewardImpl(
      amount: (json['amount'] as num).toInt(),
      type: PeriodicGrantType.fromJson(json['type'] as String),
      period: PeriodicGrantPeriod.fromJson(json['period'] as String),
      subscriptionType:
          SubscriptionType.fromJson(json['subscriptionType'] as String),
    );

Map<String, dynamic> _$$CurrencyGrantPeriodicRewardImplToJson(
        _$CurrencyGrantPeriodicRewardImpl instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'type': _$PeriodicGrantTypeEnumMap[instance.type]!,
      'period': _$PeriodicGrantPeriodEnumMap[instance.period]!,
      'subscriptionType': _$SubscriptionTypeEnumMap[instance.subscriptionType]!,
    };

const _$PeriodicGrantTypeEnumMap = {
  PeriodicGrantType.periodicReward: 'periodic_reward',
  PeriodicGrantType.$unknown: r'$unknown',
};

const _$PeriodicGrantPeriodEnumMap = {
  PeriodicGrantPeriod.initial: 'initial',
  PeriodicGrantPeriod.daily: 'daily',
  PeriodicGrantPeriod.monthly: 'monthly',
  PeriodicGrantPeriod.$unknown: r'$unknown',
};

const _$SubscriptionTypeEnumMap = {
  SubscriptionType.trial: 'trial',
  SubscriptionType.basic: 'basic',
  SubscriptionType.$unknown: r'$unknown',
};
