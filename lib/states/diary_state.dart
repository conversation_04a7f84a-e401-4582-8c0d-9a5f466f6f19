enum DiarySlotState {
  none,
  processing,
  success,
  faile,
  error,
  empty,
}

class DiarySlot {
  DiarySlotState state = DiarySlotState.none;
  String entryId = '';
  String content = '';
  String? error;
  String nickname = '';
  int? gems;

  DiarySlot({
    this.state = DiarySlotState.none,
    this.entryId = '',
    this.content = '',
    this.error = '',
    this.nickname = '',
    this.gems,
  });
}
