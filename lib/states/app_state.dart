import 'dart:convert';
import 'dart:core';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:langda/utils/my_logger.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LDAppState extends ChangeNotifier {
  static LDAppState _instance = LDAppState._internal();

  factory LDAppState() => _instance;

  LDAppState._internal();

  late SharedPreferences prefs;

  Future initializeLDAppState() async {
    try {
      prefs = await SharedPreferences.getInstance();

      final PackageInfo packageInfo = await PackageInfo.fromPlatform();
      appVersion = packageInfo.version;
      appBuildNumber = int.parse(packageInfo.buildNumber);

      _bufferContent = prefs.getString('bufferContent') ?? '';

      try {
        final String? result = prefs.getString('pageToolTipsRecord');
        if (result != null) {
          _pageToolTipsRecord = json.decode(result);
        } else {
          _pageToolTipsRecord = {};
        }
      } catch (e) {
        print('pageToolTipsRecord error: $e');
      }
    } catch (e) {
      myLog(
        'initializeLDAppState error: $e',
        LogLevel.error,
      );
    }
  }

  // * app info
  String _appVersion = '1.0.0';
  String get appVersion => _appVersion;
  set appVersion(String value) {
    _appVersion = value;
  }

  int _appBuild = 1;

  static var instance;
  int get appBuildNumber => _appBuild;
  set appBuildNumber(int value) {
    _appBuild = value;
  }

  Map<String, dynamic> _pageToolTipsRecord = {};
  Map<String, dynamic> get pageToolTipsRecord => _pageToolTipsRecord;

  Future<void> setTooltipShown(String pageName) async {
    try {
      _pageToolTipsRecord[pageName] = 'done';
      final String encoded = json.encode(_pageToolTipsRecord);
      await prefs.setString('pageToolTipsRecord', encoded);
      notifyListeners();
    } catch (e) {
      myLog('Error saving tooltip state: $e', LogLevel.error);
      // Reset state on error
      _pageToolTipsRecord.remove(pageName);
      notifyListeners();
    }
  }

  bool isPageToolTipShown(String pageName) {
    if (!_pageToolTipsRecord.containsKey(pageName)) {
      _pageToolTipsRecord[pageName] = 'todo';
      notifyListeners();
    } else if (_pageToolTipsRecord[pageName] == 'done') {
      return true;
    }

    return false;
  }

  // * locale
  Locale getLocale() {
    final String platformLocale = Platform.localeName;
    final String languageCode =
        prefs.getString('languageCode') ?? platformLocale.split('_').first;
    final String countryCode =
        prefs.getString('countryCode') ?? platformLocale.split('_').last;
    return Locale(
      languageCode,
      countryCode,
    );
  }

  Future<void> setLocale(String languageCode, String countryCode) async {
    await Future.wait([
      prefs.setString('languageCode', languageCode),
      prefs.setString('countryCode', countryCode),
    ]);
  }

  // * streak review tracking
  bool hasShownStreakReview() {
    return prefs.getBool('hasShownStreakReview') ?? false;
  }

  Future<void> setStreakReviewShown() async {
    await prefs.setBool('hasShownStreakReview', true);
  }

  //* tempNote
  String _bufferContent = '';
  String get bufferContent => _bufferContent;
  set bufferNoteContent(String value) {
    _bufferContent = value;
    prefs.setString('bufferContent', value);
  }
}
