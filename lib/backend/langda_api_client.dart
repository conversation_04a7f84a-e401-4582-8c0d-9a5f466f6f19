import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../api/rest_client.dart';
import '../utils/my_logger.dart';
import 'repository/clients/config.dart';

class LangdaApiClient {
  static final RestClient client = _createClient();

  static RestClient _createClient() {
    final dio = Dio()
      ..interceptors.add(
        InterceptorsWrapper(
          onRequest: _transformRequest,
          onError: _handleError,
        ),
      );

    return RestClient(dio, baseUrl: apiServerBaseUrl);
  }

  static dynamic _removeNullProperties(dynamic data) {
    // Remove null properties from JSON objects
    if (data is Map) {
      final Map<String, dynamic> result = {};
      data.forEach((key, value) {
        if (value != null) {
          result[key] = _removeNullProperties(value);
        }
      });
      return result;
    } else if (data is List) {
      return data.map((e) => _removeNullProperties(e)).toList();
    }
    return data;
  }

  static void _transformRequest(
      RequestOptions options, RequestInterceptorHandler handler) {
    // Add headers
    final authHeader = apiServerHeaders['Authorization'];
    if (authHeader == null || authHeader.isEmpty) {
      myLog('apiServerHeaders[\'Authorization\'] is empty', LogLevel.error);
      handler.reject(
        DioException(
          requestOptions: options,
          error: 'Missing Authorization header',
        ),
      );
      return;
    }
    options.headers['Authorization'] = authHeader;

    final session = Supabase.instance.client.auth.currentSession;
    if (session != null) {
      options.headers['Authorization-Supabase'] =
          'Bearer ${session.accessToken}';
    }

    // Remove null values from request body for PUT, POST, PATCH, DELETE requests
    if (['PUT', 'POST', 'PATCH', 'DELETE'].contains(options.method) &&
        options.data is Map) {
      options.data = _removeNullProperties(options.data);
    }

    handler.next(options);
  }

  static void _handleError(
      DioException error, ErrorInterceptorHandler handler) {
    myLog('API Error: ${error.message}', LogLevel.error);

    if (error.response?.data != null) {
      try {
        if (error.response!.data is Map) {
          myLog('API Error Response: ${error.response!.data}', LogLevel.error);
        } else if (error.response!.data is String) {
          final Map<String, dynamic> jsonData =
              jsonDecode(error.response!.data);
          myLog('API Error Response: $jsonData', LogLevel.error);
        }
      } catch (e) {
        myLog(
            'API Error Raw Response: ${error.response!.data}', LogLevel.error);
      }
    }

    handler.next(error);
  }
}
