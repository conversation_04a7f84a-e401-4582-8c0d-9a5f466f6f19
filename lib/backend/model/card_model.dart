import 'dart:ui';

import 'package:diff_match_patch/diff_match_patch.dart';
import 'package:langda/api/export.dart';
import 'package:langda/models/generated_classes.dart';

import '../../presentation/pages/mobile/constants.dart';
import '../../presentation/pages/mobile/home_page/card_page/component/card_tag.dart';

class CardModel {
  final String correctionId;
  final DateTime createdAt;
  final List<CardTag> categories;
  final Phrases asIs;
  final Phrases toBe;
  late final String explanation;
  final bool isSaved;
  final bool isVoted;
  final Corrections correction;
  final String? deepExplanation;
  final CardHeaderWrapperModel asIsCardHeaderWrapper;
  final CardHeaderWrapperModel toBeCardHeaderWrapper;
  final ExplanationLanguagePreference? explanationLanguagePreference;

  CardModel({
    required this.correctionId,
    required this.createdAt,
    required this.categories,
    required this.asIs,
    required this.toBe,
    required this.correction,
    required this.isSaved,
    required this.isVoted,
    this.deepExplanation,
    this.explanationLanguagePreference,
  })  : asIsCardHeaderWrapper = _createAsIsCardHeaderWrapper(asIs, toBe),
        toBeCardHeaderWrapper = _createToBeCardHeaderWrapper(asIs, toBe),
        explanation = (() {
          if (explanationLanguagePreference ==
              ExplanationLanguagePreference.diaryLanguage) {
            return correction.explanationDiaryLanguage;
          } else {
            return correction.explanationUserLanguage;
          }
        })();

  static CardHeaderWrapperModel _createAsIsCardHeaderWrapper(
      Phrases asIs, Phrases toBe) {
    final dmp = DiffMatchPatch();
    return CardHeaderWrapperModel(
      diffs: dmp.diff(toBe.content, asIs.content),
      stringList: asIs.content.split(''),
      color: LDColors.mainRed,
    );
  }

  static CardHeaderWrapperModel _createToBeCardHeaderWrapper(
      Phrases asIs, Phrases toBe) {
    final dmp = DiffMatchPatch();
    return CardHeaderWrapperModel(
      diffs: dmp.diff(asIs.content, toBe.content),
      stringList: toBe.content.split(''),
      color: LDColors.mainBlue,
    );
  }

  // String _getExplanation(Corrections correction,
  //     ExplanationLanguagePreference explanationLanguagePreference) {
  //   if (explanationLanguagePreference ==
  //       ExplanationLanguagePreference.nativeLanguage) {
  //     return correction.explanationUserLanguage;
  //   } else {
  //     return correction.explanationDiaryLanguage;
  //   }
  // }

  CardModel copyWith({
    String? correctionId,
    DateTime? createdAt,
    List<CardTag>? categories,
    Phrases? asIs,
    Phrases? toBe,
    Corrections? correction,
    bool? isSaved,
    bool? isVoted,
    String? deepExplanation,
  }) {
    return CardModel(
      correctionId: correctionId ?? this.correctionId,
      createdAt: createdAt ?? this.createdAt,
      categories: categories ?? this.categories,
      asIs: asIs ?? this.asIs,
      toBe: toBe ?? this.toBe,
      correction: correction ?? this.correction,
      isSaved: isSaved ?? this.isSaved,
      isVoted: isVoted ?? this.isVoted,
      deepExplanation: deepExplanation ?? this.deepExplanation,
      explanationLanguagePreference: explanationLanguagePreference,
    );
  }
}

class CardHeaderWrapperModel {
  final List<Diff> diffs;
  final List<String> stringList;
  final Color color;

  CardHeaderWrapperModel({
    required this.diffs,
    required this.stringList,
    required this.color,
  });
}

class PhraseData {
  final List<String> phraseIds;
  final List<String> contents;

  PhraseData({required this.phraseIds, required this.contents});
}

// Currently unused
class CardDeckModel {
  final String entryId;
  final List<CardModel> cards;

  CardDeckModel({
    required this.entryId,
    required this.cards,
  });
}

class PhraseCorrectionModel {
  PhraseCorrectionModel({
    required this.correctionId,
    required this.asIs,
    required this.toBe,
    required this.explanation,
    required this.categories,
    this.isSaved,
    this.isVoted,
  });
  String correctionId;
  String asIs;
  String toBe;
  Map<String, String?> explanation;
  List<String> categories;
  bool? isSaved = false;
  bool? isVoted = false;

  factory PhraseCorrectionModel.fromJson(Map<String, dynamic> json) =>
      PhraseCorrectionModel(
        correctionId: json["correctionId"],
        asIs: json["as_is"],
        toBe: json["to_be"],
        explanation: Map.from(json["explanation"])
            .map((k, v) => MapEntry<String, String?>(k, v)),
        categories: List<String>.from(json["categories"]),
        isSaved: json["is_saved"],
        isVoted: json["is_voted"],
      );

  Map<String, dynamic> toJson() => {
        "correctionIdList": correctionId,
        "as_is": asIs,
        "to_be": toBe,
        "explanation": Map.from(explanation)
            .map((k, v) => MapEntry<String, dynamic>(k, v)),
        "categories": List<dynamic>.from(categories.map((x) => x)),
        "is_saved": isSaved,
        "is_voted": isVoted,
      };
}
