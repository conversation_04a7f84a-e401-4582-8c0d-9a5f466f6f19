import 'package:flutter/material.dart';

import '../../models/generated_classes.dart';

class LdCharacter {
  final String character;
  final String? correctionId;
  LdCharacter(
    this.character,
    this.correctionId,
  );

  factory LdCharacter.fromJson(Map<String, dynamic> json) {
    return Ld<PERSON>haracter(
      json['character'],
      json['correction_id'],
    );
  }

  toJson() => {
        'character': character,
        'correction_id': correctionId,
      };
}

class DiaryDetailPhraseModel {
  DiaryDetailPhraseModel({
    required this.originalVersionId,
    this.correctionVersionId,
    required this.originalSentence,
    this.correctedSentence,
    this.correctionIds,
  });

  String originalVersionId;
  String? correctionVersionId;
  List<List<LdCharacter>> originalSentence;
  List<List<LdCharacter>>? correctedSentence;
  List<String>? correctionIds;
}

class DiaryListModelStruct {
  DiaryListModelStruct({
    required this.id,
    required this.date,
    required this.weather,
    required this.originalVersion,
    required this.correctedVersion,
  });
  String id;
  DateTime? date;
  String? weather;
  DiaryVersions? originalVersion;
  DiaryVersions? correctedVersion;

  factory DiaryListModelStruct.fromJson(Map<String, dynamic> json) =>
      DiaryListModelStruct(
        id: json["id"],
        date: DateTime.parse(json["entry_date"]),
        weather: json["weather"],
        originalVersion: json["original_version"],
        correctedVersion: json["corrected_version"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "entry_date": date?.toIso8601String(),
        "weather": weather,
        "original_version": originalVersion,
        "corrected_version": correctedVersion,
      };
}

int getStreakCount(List<DateTime> dateList, DateTime _noteDate) {
  int streak = 0;

  dateList.sort((a, b) => b.compareTo(a));

  final mostRecentNoteDate = dateList.first;

  if (DateUtils.isSameDay(mostRecentNoteDate, _noteDate)) {
    streak += 1;
  } else if (mostRecentNoteDate.difference(_noteDate).inDays == 0) {
    streak += 1;
  }

  for (var i = 1; i < dateList.length; i++) {
    final prevNoteDate = dateList[i - 1];
    final currentNoteDate = dateList[i];

    if (DateUtils.isSameDay(
        prevNoteDate.subtract(Duration(days: 1)), currentNoteDate)) {
      streak += 1;
    } else {
      break;
    }
  }

  return streak;
}

int getEmojiUnicodeLength(String emoji) {
  int count = String.fromCharCodes(Runes(emoji)).length;
  return count;
}

class PhraseData {
  final List<String> phraseIds;
  final List<String> contents;

  PhraseData({required this.phraseIds, required this.contents});
}
