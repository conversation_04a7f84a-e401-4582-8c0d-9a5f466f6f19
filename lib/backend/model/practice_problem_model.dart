class PracticeProblem {
  final String id;
  final String sentence;
  final int blankPosition; // Index of the word to be blanked out
  final String correctAnswer;
  final List<String> options;
  final String? explanation;
  final String? category;

  const PracticeProblem({
    required this.id,
    required this.sentence,
    required this.blankPosition,
    required this.correctAnswer,
    required this.options,
    this.explanation,
    this.category,
  });

  /// Returns the sentence with the word at blankPosition replaced with a blank
  String get sentenceWithBlank {
    final words = sentence.split(' ');
    if (blankPosition >= 0 && blankPosition < words.length) {
      words[blankPosition] = '______';
      return words.join(' ');
    }
    return sentence;
  }

  /// Returns the word that should be blanked out
  String get blankWord {
    final words = sentence.split(' ');
    if (blankPosition >= 0 && blankPosition < words.length) {
      return words[blankPosition];
    }
    return '';
  }

  /// Checks if the provided answer is correct
  bool isCorrectAnswer(String answer) {
    return answer.toLowerCase().trim() == correctAnswer.toLowerCase().trim();
  }

  factory PracticeProblem.fromJson(Map<String, dynamic> json) {
    return PracticeProblem(
      id: json['id'] as String,
      sentence: json['sentence'] as String,
      blankPosition: json['blankPosition'] as int,
      correctAnswer: json['correctAnswer'] as String,
      options: List<String>.from(json['options'] as List),
      explanation: json['explanation'] as String?,
      category: json['category'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sentence': sentence,
      'blankPosition': blankPosition,
      'correctAnswer': correctAnswer,
      'options': options,
      'explanation': explanation,
      'category': category,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PracticeProblem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PracticeProblem(id: $id, sentence: $sentence, blankPosition: $blankPosition, correctAnswer: $correctAnswer)';
  }
}

/// Represents the user's progress through practice problems
class PracticeProgress {
  final int totalProblems;
  final int completedProblems;
  final int correctAnswers;
  final List<String> completedProblemIds;

  const PracticeProgress({
    required this.totalProblems,
    required this.completedProblems,
    required this.correctAnswers,
    required this.completedProblemIds,
  });

  /// Calculate accuracy percentage
  double get accuracyPercentage {
    if (completedProblems == 0) return 0.0;
    return (correctAnswers / completedProblems) * 100;
  }

  /// Check if all problems are completed
  bool get isComplete => completedProblems >= totalProblems;

  /// Get progress as a fraction (0.0 to 1.0)
  double get progressFraction {
    if (totalProblems == 0) return 0.0;
    return completedProblems / totalProblems;
  }

  /// Create a copy with updated values
  PracticeProgress copyWith({
    int? totalProblems,
    int? completedProblems,
    int? correctAnswers,
    List<String>? completedProblemIds,
  }) {
    return PracticeProgress(
      totalProblems: totalProblems ?? this.totalProblems,
      completedProblems: completedProblems ?? this.completedProblems,
      correctAnswers: correctAnswers ?? this.correctAnswers,
      completedProblemIds: completedProblemIds ?? this.completedProblemIds,
    );
  }

  factory PracticeProgress.initial(int totalProblems) {
    return PracticeProgress(
      totalProblems: totalProblems,
      completedProblems: 0,
      correctAnswers: 0,
      completedProblemIds: [],
    );
  }

  @override
  String toString() {
    return 'PracticeProgress(completed: $completedProblems/$totalProblems, accuracy: ${accuracyPercentage.toStringAsFixed(1)}%)';
  }
}
