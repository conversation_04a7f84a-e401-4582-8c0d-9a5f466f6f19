class DeckModelStructure {
  String id; // Deck ID (UUID)
  String name; // 덱 이름
  String description; // 덱 설명
  int wordCount; // 단어 수
  int wordCountToday; // 오늘의 단어 수
  DateTime createdAt; // 생성일
  DateTime updatedAt; // 업데이트일
  DateTime? deletedAt; // 삭제일
  DeckModelStructure({
    required this.id,
    required this.name,
    required this.description,
    required this.wordCount,
    required this.wordCountToday,
    required this.createdAt,
    required this.updatedAt,
    required this.deletedAt,
  });
}
