import 'package:langda/api/models/explanation_language_preference.dart';
import 'package:purchases_flutter/models/customer_info_wrapper.dart';

enum SubscriptionStatus {
  none,
  trial,
  subscribed_annual,
  subscribed_monthly,
  expired,
}

class UserModelStructure {
  String id;
  String nickname;
  String email;
  SubscriptionStatus status;
  DateTime? trialStartsAt;
  DateTime? trialEndsAt;
  DateTime createdAt;
  DateTime? updatedAt;
  String? learningMotivation;
  String userLanguage;
  String diaryLanguage;
  int currencyBalance;
  String referralCode = '';
  CustomerInfo? customerInfo;
  ExplanationLanguagePreference explanationLanguagePreference;

  UserModelStructure({
    required this.id,
    required this.nickname,
    required this.email,
    required this.status,
    required this.trialStartsAt,
    required this.trialEndsAt,
    required this.createdAt,
    required this.updatedAt,
    required this.learningMotivation,
    required this.userLanguage,
    required this.diaryLanguage,
    required this.currencyBalance,
    this.referralCode = '',
    this.customerInfo,
    this.explanationLanguagePreference =
        ExplanationLanguagePreference.nativeLanguage,
  });

  DateTime get latestPurchaseDate {
    if (customerInfo == null) {
      return DateTime.now();
    }
    final latestPurchase = customerInfo!.allPurchaseDates.values.last;
    if (latestPurchase == null) {
      return DateTime.now();
    }
    return DateTime.parse(latestPurchase);
  }

  DateTime get expirationDate {
    if (customerInfo == null) {
      return DateTime.now();
    }
    final expirationDate = customerInfo!.latestExpirationDate;
    if (expirationDate == null) {
      return DateTime.now();
    }
    return DateTime.parse(expirationDate);
  }

  DateTime get nextBillingDate {
    if (customerInfo == null) {
      return DateTime.now();
    }
    return expirationDate.add(Duration(days: 1));
  }

  factory UserModelStructure.fromJson(Map<String, dynamic> json) {
    return UserModelStructure(
      id: json['id'],
      nickname: json['name'],
      email: json['email'],
      status: json['status'],
      trialStartsAt: json['trialStartsAt'],
      trialEndsAt: json['trialEndsAt'],
      createdAt: json['createdAt'],
      updatedAt: json['updatedAt'],
      learningMotivation: json['learningMotivation'],
      userLanguage: json['userLanguage'],
      diaryLanguage: json['diaryLanguage'],
      currencyBalance: json['currencyBalance'],
      referralCode: json['referralCode'] ?? '',
      customerInfo: json['customerInfo'],
      explanationLanguagePreference:
          json['explanationLanguagePreference'] != null
              ? ExplanationLanguagePreference.fromJson(
                  json['explanationLanguagePreference'])
              : ExplanationLanguagePreference.nativeLanguage,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': nickname,
      'email': email,
      'status': status,
      'trialStartsAt': trialStartsAt,
      'trialEndsAt': trialEndsAt,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'learningMotivation': learningMotivation,
      'userLanguage': userLanguage,
      'diaryLanguage': diaryLanguage,
      'currencyBalance': currencyBalance,
      'referralCode': referralCode,
      'customerInfo': customerInfo,
    };
  }

  UserModelStructure copyWith({
    String? id,
    String? nickname,
    String? email,
    SubscriptionStatus? status,
    DateTime? trialStartsAt,
    DateTime? trialEndsAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? userLanguage,
    String? diaryLanguage,
    int? currencyBalance,
    String? learningMotivation,
    CustomerInfo? customerInfo,
    ExplanationLanguagePreference? explanationLanguagePreference,
  }) {
    return UserModelStructure(
      id: id ?? this.id,
      nickname: nickname ?? this.nickname,
      email: email ?? this.email,
      status: status ?? this.status,
      trialStartsAt: trialStartsAt ?? this.trialStartsAt,
      trialEndsAt: trialEndsAt ?? this.trialEndsAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      userLanguage: userLanguage ?? this.userLanguage,
      diaryLanguage: diaryLanguage ?? this.diaryLanguage,
      currencyBalance: currencyBalance ?? this.currencyBalance,
      customerInfo: customerInfo ?? this.customerInfo,
      referralCode: referralCode,
      learningMotivation: '',
      explanationLanguagePreference:
          explanationLanguagePreference ?? this.explanationLanguagePreference,
    );
  }
}
