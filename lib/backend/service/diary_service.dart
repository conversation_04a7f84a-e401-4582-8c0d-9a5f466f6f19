import 'package:flutter/widgets.dart';
import 'package:langda/backend/repository/clients/api/api_diary_repository.dart';
import 'package:langda/backend/repository/clients/supabase/supabase_diary_repository.dart';
import 'package:langda/models/generated_classes.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../utils/my_logger.dart';
import '../model/diary_model.dart';
import '../utils/diary_utils.dart';

class DiaryService {
  final SupabaseNoteRepository _supabaseNoteRepository;
  final ApiNoteRepository _noteApiRepository;

  static DiaryService _instance = DiaryService._(
    SupabaseNoteRepository(Supabase.instance.client),
    ApiNoteRepository(),
  );
  factory DiaryService() => _instance;

  // For testing only
  static void setTestInstance(DiaryService service) {
    _instance = service;
  }

  static void resetInstance() {
    _instance = DiaryService._(
      SupabaseNoteRepository(Supabase.instance.client),
      ApiNoteRepository(),
    );
  }

  // For testing only
  static DiaryService get testInstance => _instance;

  DiaryService._(this._supabaseNoteRepository, this._noteApiRepository);

  Future<DiaryListModelStruct> fetchDiaryById(String entryId) async {
    final entry = await _supabaseNoteRepository.getEntryById(entryId);

    final versions = await _fetchVersions(entry.id);
    return DiaryListModelStruct(
      id: entry.id,
      weather: entry.weather,
      date: entry.entryDate,
      originalVersion: versions.keys.first,
      correctedVersion: versions.values.first,
    );
  }

  Future<List<String>> fetchEntryIds() async {
    return await _supabaseNoteRepository.getEntryIds();
  }

  Future<Map<String, int>> fetchNumberOfDiary(DateTime date) async {
    final result = await _supabaseNoteRepository.getDiaryDateList();
    List<DateTime> dateList = [];

    if (result.isEmpty) {
      return {
        'streak': 0,
        'total': 0,
      };
    }
    List<String> dateStringList =
        result.map((e) => e.createdAt.toString()).toList();
    dateList = dateStringList.map((e) => DateTime.parse(e)).toList();

    final streak = calculateStreak(dateList, date);

    return {
      'streak': streak,
      'total': dateList.length,
    };
  }

  Future<List<DiaryListModelStruct>> fetchDiaryList({
    int? year,
    int? month,
  }) async {
    List<DiaryListModelStruct> noteList = [];
    final entries = await _supabaseNoteRepository.getEntries(year, month);
    if (entries.isEmpty) {
      return [];
    }
    final versions =
        await Future.wait(entries.map((e) => _fetchVersions(e.id)).toList());
    for (var entry in entries) {
      final note = DiaryListModelStruct(
        id: entry.id,
        weather: entry.weather,
        date: entry.entryDate,
        originalVersion: null,
        correctedVersion: null,
      );
      noteList.add(note);
    }

    for (int i = 0; i < noteList.length; i++) {
      final note = noteList[i];
      final version = versions[i];
      if (version.isNotEmpty &&
          note.id == version.keys.first.diaryEntryId &&
          note.id == version.values.first.diaryEntryId) {
        note.originalVersion = version.keys.first;
        note.correctedVersion = version.values.first;
      }

      noteList[i] = note;
    }

    return noteList;
  }

  Future<List<Map<DiaryVersionChunks, DiaryVersionChunks?>>> fetchChunks(
      String originalVersionId, String? correctedVersionId) async {
    List<Map<DiaryVersionChunks, DiaryVersionChunks?>> chunks = [];
    final originalChunks =
        await _supabaseNoteRepository.getDiaryVersionChunks(originalVersionId);

    final List<DiaryVersionChunks>? correctedChunks = correctedVersionId == null
        ? null
        : await _supabaseNoteRepository
            .getDiaryVersionChunks(correctedVersionId);

    for (int i = 0; i < originalChunks.length; i++) {
      final DiaryVersionChunks originalChunk = originalChunks[i];
      final DiaryVersionChunks? correctedChunk = correctedChunks?.firstWhere(
        (element) => element.sequenceNumber == originalChunk.sequenceNumber,
      );

      chunks.add({
        originalChunk: correctedChunk,
      });
    }

    return chunks;
  }

  Future<List<List<List<LdCharacter>>>> getParagraphs(
      String versionId, String content, bool isOriginal) async {
    myLog(
      'getParagraphs called: isOriginal: $isOriginal',
    );
    List<List<List<LdCharacter>>> paragraphs = [];

    List<String> contentCharacterList = content.characters.toList();

    final results = await Future.wait([
      _supabaseNoteRepository.getPhrases(versionId, isOriginal),
      _supabaseNoteRepository.getDiaryVersionChunks(versionId),
    ]);

    final phrases = results[0] as List<Map<String, dynamic>>;

    final Map<String, Map<int, int>> phraseMap = Map.fromEntries(
      phrases.map(
        (e) {
          return MapEntry(
            e['corrections'][0]['id'],
            {
              e['start_index']: e['end_index'],
            },
          );
        },
      ),
    );

    final versionChunks = results[1] as List<DiaryVersionChunks>;

    final Map<int, List<String>> chunkMap = Map.fromEntries(
      versionChunks.map((e) =>
          MapEntry(int.parse(e.sequenceNumber.toString()), e.sentences!)),
    );
    List<String> chunks = [];
    List<List<LdCharacter>> tempSentences = [];
    List<LdCharacter> tempWords = [];
    int contentIndex = 0;
    int emojiLength = 0;

    for (var j = 0; j < chunkMap.length; j++) {
      chunks = chunkMap.entries
          .firstWhere((element) => element.key == j)
          .value
          .map((e) => e.toString().trimRight().trimLeft())
          .toList();
      for (var k = 0; k < chunks.length; k++) {
        List<String> chunkChracterList = chunks[k].characters.toList();

        for (var i = 0; i < chunkChracterList.length; i++) {
          final chracter = contentCharacterList[contentIndex];

          if (chunkChracterList[i].toLowerCase() == chracter.toLowerCase() ||
              chunkChracterList[i].toUpperCase() == chracter.toUpperCase() ||
              chunkChracterList[i] == chracter) {
            // chunk의 글자와 content의 글자가 같은 경우
            // phrase의 시작 인덱스와 끝 인덱스를 찾아서 비교

            MapEntry<String, Map<int, int>>? phrase = null;

            if (isEmoji(chracter)) {
              emojiLength += getEmojiUnicodeLength(chracter) - 1;
            }

            if (phraseMap.entries.isNotEmpty) {
              phrase = phraseMap.entries.first;

              int startIndex = phrase.value.keys.first - emojiLength;
              int endIndex = phrase.value.values.first - emojiLength;
              if (contentIndex >= startIndex) {
                if (contentIndex <= endIndex) {
                  // correction이 있는 경우
                  tempWords.add(LdCharacter(chracter, phrase.key));
                } else {
                  if (contentIndex >= endIndex) {
                    phraseMap.remove(phrase.key);
                  }

                  if (startIndex > contentIndex) {
                    tempWords.add(LdCharacter(chracter, null));
                  } else {
                    i--;
                    contentIndex--;
                  }
                }
              } else {
                tempWords.add(LdCharacter(chracter, null));
              }
            } else {
              tempWords.add(LdCharacter(chracter, null));
            }
          } else {
            i -= 1;
          }
          contentIndex++;
        }

        tempSentences.add(tempWords);
        tempWords = [];
      }

      paragraphs.add(tempSentences);
      contentIndex++;
      tempWords = [];
      tempSentences = [];
    }

    return paragraphs;
  }

  Future<Map<DiaryVersions, DiaryVersions>> _fetchVersions(
      String entryId) async {
    final versions = await Future.wait([
      _supabaseNoteRepository.getOriginalVersion(entryId),
      _supabaseNoteRepository.getCorrectedVersion(entryId),
    ]);
    final DiaryVersions originalVersion = versions[0];
    final DiaryVersions correctedVersion = versions[1];

    return {
      originalVersion: correctedVersion,
    };
  }

  Future<String> getCurrentEntryDate(String userId) async {
    return await _noteApiRepository.getCurrentEntryDate(userId);
  }

  Future<bool> hasEntryForEntryDate(String entryDate) async {
    final entry = await _supabaseNoteRepository.getEntryByDate(entryDate);
    return entry != null;
  }
}
