import 'package:langda/backend/repository/clients/supabase/supabase_common_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class CommonService {
  final SupabaseCommonRepository _supabaseCommonRepository;

  static CommonService? _instance;

  static CommonService get instance {
    _instance ??= CommonService._internal();
    return _instance!;
  }

  CommonService._internal()
      : _supabaseCommonRepository =
            SupabaseCommonRepository(Supabase.instance.client);

  Future<void> insertQuestion(
    String title,
    String content,
  ) async {
    await _supabaseCommonRepository.insertQuestion(
        title, content, Supabase.instance.client.auth.currentUser!.id);
  }
}
