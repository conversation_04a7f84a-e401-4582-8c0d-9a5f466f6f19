import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:langda/api/export.dart';
import 'package:langda/backend/model/user_model.dart';
import 'package:langda/queries/auth_query.dart';
import 'package:langda/utils/my_logger.dart';

class CurrencyService {
  static CurrencyService? _instance;
  static CurrencyService get instance {
    _instance ??= CurrencyService._internal();
    return _instance!;
  }

  CurrencyService._internal();

  /// Updates the user's currency balance optimistically in the cache
  ///
  /// Takes a [BaseCurrencyResponse] which contains the updated currency balance
  /// and updates the fetchUserInfoQuery cache with the new balance.
  /// This follows the optimistic update pattern similar to _updateCardInAllCardsQueries
  void updateCurrencyBalance(BaseCurrencyResponse response) {
    final String key = userInfoKey();
    myLog("Updating query for userInfoKey: $key");
    CachedQuery.instance.updateQuery(
      updateFn: (dynamic old) {
        myLog("Optimistically updating user currency balance");
        final UserModelStructure? oldUserInfo = old as UserModelStructure?;
        if (oldUserInfo == null) {
          myLog("[updateCurrencyBalance] oldUserInfo is null");
          return old;
        }
        // Update the user model with the new currency balance
        return oldUserInfo.copyWith(
          currencyBalance: response.updatedCurrencyBalance.toInt(),
          updatedAt: DateTime.now(),
        );
      },
      filterFn: (unencodedKey, queryKey) => queryKey == key,
    );
  }
}
