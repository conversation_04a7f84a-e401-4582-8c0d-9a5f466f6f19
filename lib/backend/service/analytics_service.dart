import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:langda/queries/card_query.dart';
import 'package:langda/queries/diary_query.dart';

class AnalyticsService {
  /// Get diary writing streak data for the last 30 days
  static Future<List<FlSpot>> getDiaryStreakData() async {
    final now = DateTime.now();
    final List<FlSpot> spots = [];

    try {
      // Get diary count data
      final diaryCountResult = await fetchNumberOfDiaryQuery(now).result;
      final totalDiaries = diaryCountResult.data?['total'] ?? 0;
      final currentStreak = diaryCountResult.data?['streak'] ?? 0;

      // Generate sample data for the last 30 days
      // In a real implementation, you'd fetch actual daily diary counts
      for (int i = 29; i >= 0; i--) {
        final date = now.subtract(Duration(days: i));
        // Simulate diary writing pattern based on current streak
        final hasEntry = i < currentStreak ? 1.0 : (i % 3 == 0 ? 1.0 : 0.0);
        spots.add(FlSpot(29 - i.toDouble(), hasEntry));
      }
    } catch (e) {
      // Return empty data on error
      for (int i = 0; i < 30; i++) {
        spots.add(FlSpot(i.toDouble(), 0.0));
      }
    }

    return spots;
  }

  /// Get weekly diary count for the last 8 weeks
  static Future<List<BarChartGroupData>> getWeeklyDiaryData() async {
    final now = DateTime.now();
    final List<BarChartGroupData> barGroups = [];

    try {
      // Get current diary stats
      final diaryCountResult = await fetchNumberOfDiaryQuery(now).result;
      final totalDiaries = diaryCountResult.data?['total'] ?? 0;

      // Generate sample weekly data
      for (int i = 0; i < 8; i++) {
        final weekStart =
            now.subtract(Duration(days: (7 * (7 - i)) + now.weekday - 1));
        // Simulate weekly diary counts
        final weeklyCount = (totalDiaries / 8 + (i % 3) * 2).round().toDouble();

        barGroups.add(
          BarChartGroupData(
            x: i,
            barRods: [
              BarChartRodData(
                toY: weeklyCount,
                color: const Color(0xFFD6ED17),
                width: 16,
                borderRadius: BorderRadius.circular(4),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // Return empty data on error
      for (int i = 0; i < 8; i++) {
        barGroups.add(
          BarChartGroupData(
            x: i,
            barRods: [
              BarChartRodData(
                toY: 0,
                color: const Color(0xFFD6ED17),
                width: 16,
                borderRadius: BorderRadius.circular(4),
              ),
            ],
          ),
        );
      }
    }

    return barGroups;
  }

  /// Get card performance data (accuracy by category)
  static Future<List<PieChartSectionData>> getCardPerformanceData() async {
    try {
      // Fetch all cards
      final cardsResult = await fetchAllCardsQuery().result;
      final cards = cardsResult.data ?? [];

      if (cards.isEmpty) {
        return _getEmptyPieData();
      }

      // Group cards by category/tag and calculate performance
      final Map<String, int> categoryCount = {};
      final Map<String, int> categoryCorrect = {};

      for (final card in cards) {
        final category = card.categories.isNotEmpty
            ? card.categories.first.tag.name
            : 'General';
        categoryCount[category] = (categoryCount[category] ?? 0) + 1;
        // Simulate correct answers (in real app, you'd track this)
        categoryCorrect[category] = (categoryCorrect[category] ?? 0) +
            (card.isSaved ? 1 : 0); // Use saved as proxy for "mastered"
      }

      final List<PieChartSectionData> sections = [];
      final colors = [
        const Color(0xFFD6ED17),
        const Color(0xFF606060),
        const Color(0xFF4CAF50),
        const Color(0xFF2196F3),
        const Color(0xFFFF9800),
      ];

      int colorIndex = 0;
      categoryCount.forEach((category, count) {
        final percentage = (count / cards.length) * 100;
        sections.add(
          PieChartSectionData(
            color: colors[colorIndex % colors.length],
            value: percentage,
            title: '${percentage.toStringAsFixed(1)}%',
            radius: 60,
            titleStyle: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        );
        colorIndex++;
      });

      return sections;
    } catch (e) {
      return _getEmptyPieData();
    }
  }

  /// Get practice accuracy over time
  static Future<List<FlSpot>> getPracticeAccuracyData() async {
    final List<FlSpot> spots = [];

    // Generate sample practice accuracy data for the last 10 sessions
    for (int i = 0; i < 10; i++) {
      // Simulate improving accuracy over time
      final accuracy = 60 + (i * 3) + (i % 2 == 0 ? 5 : -2);
      spots.add(FlSpot(i.toDouble(), accuracy.clamp(0, 100).toDouble()));
    }

    return spots;
  }

  /// Get current statistics summary
  static Future<Map<String, dynamic>> getStatsSummary() async {
    try {
      final now = DateTime.now();

      // Get diary stats
      final diaryCountResult = await fetchNumberOfDiaryQuery(now).result;
      final totalDiaries = diaryCountResult.data?['total'] ?? 0;
      final currentStreak = diaryCountResult.data?['streak'] ?? 0;

      // Get card stats
      final cardsResult = await fetchAllCardsQuery().result;
      final totalCards = cardsResult.data?.length ?? 0;
      final bookmarkedCards =
          cardsResult.data?.where((card) => card.isSaved).length ?? 0;

      return {
        'totalDiaries': totalDiaries,
        'currentStreak': currentStreak,
        'totalCards': totalCards,
        'masteredCards': bookmarkedCards,
        'practiceAccuracy': 78.5, // Sample data
        'weeklyGoal': 7,
        'weeklyProgress': currentStreak.clamp(0, 7),
      };
    } catch (e) {
      return {
        'totalDiaries': 0,
        'currentStreak': 0,
        'totalCards': 0,
        'masteredCards': 0,
        'practiceAccuracy': 0.0,
        'weeklyGoal': 7,
        'weeklyProgress': 0,
      };
    }
  }

  static List<PieChartSectionData> _getEmptyPieData() {
    return [
      PieChartSectionData(
        color: Colors.grey.shade300,
        value: 100,
        title: 'No Data',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.grey,
        ),
      ),
    ];
  }
}
