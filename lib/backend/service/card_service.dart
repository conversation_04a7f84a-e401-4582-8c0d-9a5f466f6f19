import 'package:langda/models/generated_classes.dart';
import 'package:langda/presentation/pages/mobile/home_page/card_page/component/card_tag.dart';
import 'package:langda/utils/supabase_auth_helper.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../api/models/explanation_language_preference.dart';
import '../model/card_model.dart';
import '../repository/clients/api/api_correction_repository.dart';
import '../repository/clients/supabase/supabase_correction_repository.dart';

class CardService {
  final SupabaseCorrectionRepository _supabaseCorrectionRepository;
  final ApiCorrectionRepository _correctionApiRepository;

  static CardService _instance = CardService._(
      SupabaseCorrectionRepository(client: Supabase.instance.client),
      ApiCorrectionRepository());
  factory CardService() => _instance;

  CardService._(
      this._supabaseCorrectionRepository, this._correctionApiRepository);

  Future<CardModel> getCard(String correctionId,
      ExplanationLanguagePreference explanationLanguagePreference) async {
    // Correction
    final correction =
        await _supabaseCorrectionRepository.getCorrection(correctionId);

    // Phrases
    final originalPhrase = _supabaseCorrectionRepository
        .getPhrasesWithId(correction.originalPhraseId!);
    final correctedPhrase = _supabaseCorrectionRepository
        .getPhrasesWithId(correction.correctedPhraseId!);
    // Categories of correction
    final categories =
        _supabaseCorrectionRepository.getCorrectionCategories(correctionId);

    final datas =
        await Future.wait([originalPhrase, correctedPhrase, categories]);

    final Phrases originalPhraseData = datas[0] as Phrases;
    final Phrases correctedPhraseData = datas[1] as Phrases;
    final List<CorrectionCategories> categoriesData =
        datas[2] as List<CorrectionCategories>;

    final card = CardModel(
      correctionId: correction.id,
      createdAt: correction.createdAt,
      asIs: originalPhraseData,
      toBe: correctedPhraseData,
      correction: correction,
      isSaved: correction.isSaved,
      isVoted: correction.likes != 0,
      categories: categoriesData
          .map((category) => CardTag.fromId(tagId: category.id))
          .toList(),
      deepExplanation: correction.deepExplanationUserLanguage,
      explanationLanguagePreference: explanationLanguagePreference,
    );
    return card;
  }

  Future<List<CardModel>> getAllCards(
      {List<Tag>? tags,
      required bool bookmarkedOnly,
      required ExplanationLanguagePreference
          explanationLanguagePreference}) async {
    final List<CorrectionWithLinks> allCorrections =
        await _supabaseCorrectionRepository
            .getCorrectionsWithPhrasesAndCategories(
                tags: tags, bookmarkedOnly: bookmarkedOnly);
    // Sort on 2 conditions: 1. correction.createdAt (descending) 2. Then by originalPhrase.start_index (ascending)
    allCorrections.sort((a, b) {
      if (a.createdAt.isAfter(b.createdAt)) {
        return -1;
      } else if (a.createdAt.isBefore(b.createdAt)) {
        return 1;
      } else {
        return a.originalPhrase.startIndex
            .compareTo(b.originalPhrase.startIndex);
      }
    });

    return allCorrections.map((correction) {
      return CardModel(
        correctionId: correction.id,
        createdAt: correction.createdAt,
        asIs: correction.originalPhrase,
        toBe: correction.correctedPhrase,
        correction: correction,
        isSaved: correction.isSaved,
        isVoted: correction.likes != 0,
        categories: correction.categories
            .map((category) => CardTag.fromId(tagId: category.id))
            .toList(),
        deepExplanation: correction.deepExplanationUserLanguage,
        explanationLanguagePreference: explanationLanguagePreference,
      );
    }).toList();
  }

  Future<List<Corrections>> getSavedCorrections() async {
    return await _supabaseCorrectionRepository.getSavedCorrections();
  }

  Future<void> updateCorrectionSavedness(
      String correctionId, bool newState) async {
    return await _supabaseCorrectionRepository.updateCorrectionSavedness(
      correctionId,
      newState,
    );
  }

  Future<void> voteCorrection(String correctionId, VoteType voteType) async {
    final userId = currentUserId();
    if (userId == null) {
      throw Exception('User is not logged in');
    }
    return await _correctionApiRepository.updateVoteCard(
        correctionId, voteType, userId);
  }

  Future<List<String>> getCorrectionIds(String? entryId) async {
    return await _supabaseCorrectionRepository
        .getCorrectionIdsWithEntryId(entryId);
  }
}

class CorrectionException implements Exception {
  CorrectionException(this.message);
  final String message;
  @override
  String toString() => message;
}
