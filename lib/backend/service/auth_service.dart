import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:langda/utils/revenucat_helper.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../models/generated_classes.dart';
import '../model/user_model.dart';
import '../repository/clients/api/api_auth_repository.dart';
import '../repository/clients/supabase/supabase_auth_repository.dart';

class AuthService {
  final SupabaseAuthRepository _authRepository;
  final ApiAuthRepository _apiAuthRepository = ApiAuthRepository();

  static AuthService _instance = AuthService._(
    SupabaseAuthRepository(Supabase.instance.client),
  );
  factory AuthService() => _instance;

  AuthService._(this._authRepository);

  Future<void> createInitialProfile(String nickname, String selectedPurpose,
      {String? referralCode,
      String? explanationLanguage,
      String? nativeLanguage}) async {
    if (selectedPurpose.isEmpty || selectedPurpose == 'none') {
      throw ArgumentError(
          'Learning motivation is required for initial profile creation');
    }

    final motivation = LEARNING_MOTIVATION.values.firstWhere((element) =>
        element.toString().split('.').last.toLowerCase() ==
        selectedPurpose.replaceAll('-', '').trim());

    await _apiAuthRepository.createProfile(
      nickname,
      motivation,
      referralCode: referralCode,
      explanationLanguage: explanationLanguage,
      nativeLanguage: nativeLanguage,
    );
    await getUser();
  }

  Future<void> updateUser(String? nickname, String? fcmToken) async {
    await _authRepository.updateUser(nickname, fcmToken);
  }

  Future<UserModelStructure?> getUser() async {
    final currentUser = _authRepository.client.auth.currentUser;
    if (currentUser == null) return null;

    late Profiles? profile;

    try {
      profile = await _authRepository.getProfiles();
      if (profile == null) {
        return null;
      } else {
        if (profile.fcmToken == null || profile.fcmToken == '') {
          final fcmToken = await FirebaseMessaging.instance.getToken();
          if (fcmToken != null) {
            await _authRepository.updateUser(null, fcmToken);
          }
        }
      }
    } catch (e) {
      print('Profile fetch error: $e');

      // Check if it's an AuthApiException
      if (e.toString().contains('AuthApiException')) {
        // Rethrow to be handled by the navigation redirect
        throw e;
      }

      // For other errors, return a default user model
      return UserModelStructure(
        id: currentUser.id,
        nickname: '',
        email: currentUser.email ?? '',
        status: SubscriptionStatus.none,
        trialStartsAt: null,
        trialEndsAt: null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        learningMotivation: null,
        userLanguage: 'ko',
        diaryLanguage: 'en',
        customerInfo: null,
        referralCode: '',
        currencyBalance: 0,
      );
    }

    final String userId = currentUser.id;
    final String? email = currentUser.email;
    SubscriptionStatus state = SubscriptionStatus.none;

    state = await determineSubscriptionStatus();

    final trialEndsAt = profile?.trialEndsAt;
    if (trialEndsAt != null && trialEndsAt.isAfter(DateTime(1999, 1, 1))) {
      if (trialEndsAt.isAfter(DateTime.now())) {
        state = SubscriptionStatus.trial;
      }
    }

    print('User state: $state');

    return UserModelStructure(
      id: userId,
      nickname: profile.nickname,
      email: email ?? '',
      status: state,
      trialStartsAt:
          trialEndsAt != null ? trialEndsAt.subtract(Duration(days: 14)) : null,
      trialEndsAt: trialEndsAt,
      createdAt: profile.createdAt,
      updatedAt: profile.updatedAt,
      learningMotivation: profile.learningMotivation.toString().toLowerCase(),
      userLanguage: 'ko',
      diaryLanguage: profile.explanationLanguage.toString().toLowerCase(),
      customerInfo: null,
      referralCode: profile.referralCode!,
      currencyBalance: profile.currencyBalance,
    );
  }

  Future<SubscriptionStatus> determineSubscriptionStatus() async {
    final purchaserInfo = await userSubscribInfo();
    if (purchaserInfo == null) {
      return SubscriptionStatus.none;
    }

    if (purchaserInfo['status'] == 'subscribed_annual') {
      return SubscriptionStatus.subscribed_annual;
    } else if (purchaserInfo['status'] == 'subscribed_monthly') {
      return SubscriptionStatus.subscribed_monthly;
    } else if (purchaserInfo['status'] == 'expired') {
      return SubscriptionStatus.expired;
    } else {
      Sentry.captureMessage(
        'Unexpected subscription status',
        level: SentryLevel.warning,
        params: [purchaserInfo['status']],
      );
      return SubscriptionStatus.none;
    }
  }

  bool isLoggedIn() {
    return _authRepository.client.auth.currentUser?.id != null;
  }

  Future<void> deleteUser() async {
    await _apiAuthRepository.deleteUser();
  }
}
