import '../../../../api/models/correction_feedback_request_body.dart';
import '../../../../api/models/feedback_type.dart';
import '../../../../utils/my_logger.dart';
import '../../../langda_api_client.dart';

class ApiCorrectionRepository {
  Future<void> updateVoteCard(
      String correctionId, VoteType type, String userId) async {
    try {
      final body = CorrectionFeedbackRequestBody(
        type: type == VoteType.downvote
            ? FeedbackType.downvote
            : FeedbackType.upvote,
        userId: userId,
      );

      myLog('voteCorrection: ${body.toJson()}');
      await LangdaApiClient.client.corrections.postCorrectionsIdFeedback(
        id: correctionId,
        body: body,
      );
    } catch (e) {
      myLog('Error updating vote card: $e', LogLevel.error);
      throw e;
    }
  }
}

enum VoteType {
  upvote,
  downvote,
}
