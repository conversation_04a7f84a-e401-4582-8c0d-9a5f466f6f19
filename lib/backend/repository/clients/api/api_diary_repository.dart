import '../../../../api/models/diary_entry_response.dart';
import '../../../../api/models/diary_insertion_request_body.dart';
import '../../../../utils/my_logger.dart';
import '../../../langda_api_client.dart';

class ApiNoteRepository {
  Future<DiaryEntryResponse> sendDiaryDataToServer(
      Map<String, String> obj) async {
    try {
      final body = DiaryInsertionRequestBody(
        userId: obj['user_id']!,
        content: obj['content']!,
        weather: obj['weather']!,
        date: obj['date']!,
      );

      return await LangdaApiClient.client.diary.postInsert(body: body);
    } catch (e) {
      myLog(
        'SendNoteDateToServer error: $e',
        LogLevel.error,
      );
      throw e;
    }
  }

  Future<String> getCurrentEntryDate(String userId) async {
    try {
      final response =
          await LangdaApiClient.client.diary.getEntryDate(userId: userId);
      return response;
    } catch (e) {
      myLog('Error getCurrentEntryDate: $e', LogLevel.error);
      return '';
    }
  }

  Future<String> validateEntryDate(String userId, DateTime date) async {
    try {
      String entryDate = await getCurrentEntryDate(userId);

      final formattedToday =
          '${date.year}-${date.month.toString().length == 1 ? '0' + date.month.toString() : date.month.toString()}-${date.day.toString().length == 1 ? '0' + date.day.toString() : date.day}';

      if (entryDate == formattedToday) {
        return entryDate;
      }
    } catch (e) {
      myLog('Error checkIsAllowedDate: $e', LogLevel.error);
    }

    return '';
  }
}
