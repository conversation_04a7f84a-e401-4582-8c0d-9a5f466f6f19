import 'dart:convert';

import 'package:http/http.dart' as http;

import '../../../../api/models/create_user_profile_request.dart';
import '../../../../api/models/learning_motivation.dart';
import '../../../../models/generated_classes.dart';
import '../../../../utils/my_logger.dart';
import '../../../langda_api_client.dart';
import '../config.dart';

class ApiAuthRepository {
  /// Creates a new user profile. This should only be called once during initial setup.
  Future<void> createProfile(String nickname, LEARNING_MOTIVATION motivation,
      {String? referralCode,
      String? explanationLanguage,
      String? nativeLanguage}) async {
    try {
      myLog('createProfile: Sending request');
      final request = CreateUserProfileRequest(
        nickname: nickname,
        learningMotivation: motivation.toApiEnum(),
        referralCode: referralCode,
        explanationLanguage: explanationLanguage,
        nativeLanguage: nativeLanguage,
      );

      await LangdaApiClient.client.userManagement
          .postUsersProfile(body: request);
      myLog('createProfile: Success');
    } catch (e) {
      myLog('createProfile error: $e', LogLevel.error);
      throw e;
    }
  }

  Future<bool> validateEmail(String email) async {
    try {
      final Map<String, dynamic> data = {'email': email};
      final jsonBody = jsonEncode(data);

      myLog('checkEmail: $jsonBody');
      if (groonWorkerHeaders['Authorization'] == '') {
        myLog('groonWorkerHeaders[\'Authorization\'] is empty', LogLevel.error);
        return false;
      }

      final response = await http.post(
        Uri.parse(groonWorkerBaseUrl),
        headers: groonWorkerHeaders,
        body: jsonBody,
      );
      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        final bool allowed = json['allowed'];
        return allowed;
      } else {
        myLog('Error checkEmail response: ${response.statusCode}',
            LogLevel.error);
        return false;
      }
    } catch (e) {
      myLog('Error checkEmail: $e', LogLevel.error);
      throw e;
    }
  }

  Future<void> deleteUser() async {
    try {
      myLog('deleteUser: Sending request');
      await LangdaApiClient.client.userManagement.deleteUsersMe();
      myLog('deleteUser: Success');
    } catch (e) {
      myLog('deleteUser error: $e', LogLevel.error);
      throw e;
    }
  }
}

// Convert Postgres DB Type (supabase_flutter) to API Type (swagger_parser)
extension LearningMotivationConverter on LEARNING_MOTIVATION {
  LearningMotivation toApiEnum() {
    switch (this) {
      case LEARNING_MOTIVATION.social:
        return LearningMotivation.social;
      case LEARNING_MOTIVATION.study_abroad:
        return LearningMotivation.studyAbroad;
      case LEARNING_MOTIVATION.emigration:
        return LearningMotivation.emigration;
      case LEARNING_MOTIVATION.career:
        return LearningMotivation.career;
      case LEARNING_MOTIVATION.fun:
        return LearningMotivation.fun;
      case LEARNING_MOTIVATION.other:
        return LearningMotivation.other;
    }
  }
}
