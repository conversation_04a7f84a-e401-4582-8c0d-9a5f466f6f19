import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../utils/my_logger.dart';
import '../../../utils/common_utils.dart';

class SupabaseCommonRepository {
  SupabaseCommonRepository(this.client);
  final SupabaseClient client;

  Future<void> insertQuestion(
    String title,
    String content,
    String userId,
  ) async {
    try {
      final deviceInfo = await getDeviceInfo();
      myLog('insertQuestion called');
      await client.from('user_questions').insert([
        {
          'user_id': userId,
          'title': title,
          'body': content,
          'device_info': deviceInfo,
        },
      ]);
    } catch (e) {
      myLog('insertQuestion error: $e', LogLevel.error);
      throw e;
    }
  }
}
