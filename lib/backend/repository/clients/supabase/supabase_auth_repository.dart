import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../models/generated_classes.dart';
import '../../../../utils/my_logger.dart';

class SupabaseAuthRepository {
  SupabaseAuthRepository(this.client);
  final SupabaseClient client;

  String get _userId => client.auth.currentUser!.id;

  Future<bool> updateUser(String? nickname, String? fcmToken) async {
    try {
      Map<String, dynamic> updates = Profiles.update(
        nickname: nickname,
        fcmToken: fcmToken,
        updatedAt: DateTime.now(),
      );
      await client.from('profiles').update(updates).eq('user_id', _userId);

      return true;
    } catch (e) {
      myLog('setUserNickname error: $e', LogLevel.error);

      throw e;
    }
  }

  Future<Profiles?> getProfiles() async {
    try {
      final profileRows = await client
          .from('profiles')
          .select('*')
          .withConverter(Profiles.converter);
      if (profileRows.length > 0) {
        return profileRows.first;
      } else {
        return null;
      }
    } catch (e) {
      myLog(
        'getUserLDMetaData error: $e',
        LogLevel.error,
      );

      throw e;
    }
  }
}
