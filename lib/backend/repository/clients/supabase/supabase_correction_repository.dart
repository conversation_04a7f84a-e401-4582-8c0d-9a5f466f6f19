import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../models/generated_classes.dart';
import '../../../../presentation/pages/mobile/home_page/card_page/component/card_tag.dart';
import '../../../../utils/my_logger.dart';

class SupabaseCorrectionRepository {
  final SupabaseClient client;
  SupabaseCorrectionRepository({
    required this.client,
  });

  Future<Corrections> getCorrection(String correctionId) async {
    return await client
        .from('corrections')
        .select('*')
        .eq('id', correctionId)
        .single()
        .withConverter(Corrections.converterSingle);
  }

  Future<Phrases> getPhrasesWithId(String phrasesId) async {
    return await client
        .from('phrases')
        .select('*')
        .eq('id', phrasesId)
        .single()
        .withConverter(Phrases.converterSingle);
  }

  Future<List<CorrectionCategories>> getCorrectionCategories(
      String correctionId) async {
    final response =
        await client.from('corrections_correction_categories').select('''
        correction_categories (
          id,
          name_ko,
          name_en
        )
      ''').eq('correction_id', correctionId);

    return List<Map<String, dynamic>>.from(response)
        .map((item) =>
            CorrectionCategories.fromJson(item['correction_categories']))
        .toList();
  }

  Future<List<Map<String, dynamic>>> getCorrectionsWithPhraseIds(
      List<String> phraseIds) async {
    return await client.from('corrections').select('''
      *,
      corrections_correction_categories (
        category_id, 
        correction_id, 
        correction_categories (
          id,
          name_ko
        )
      ) where ((correction_id = id) = correction_categories.id)
    ''').inFilter('corrected_phrase_id', phraseIds);
  }

  Future<List<CorrectionWithLinks>> getCorrectionsWithPhrasesAndCategories(
      {List<Tag>? tags, required bool bookmarkedOnly}) async {
    final userId = client.auth.currentUser?.id;
    if (userId == null) {
      return [];
    }
    var query = client.from('corrections').select('''
    *,
    original_phrase:original_phrase_id (
      *
    ),
    corrected_phrase:corrected_phrase_id (
      *
    ),
    correction_categories!inner(
      *
    )
    ''').eq('user_id', userId);
    if (bookmarkedOnly) {
      query = query.eq('is_saved', true);
    }
    if (tags != null && tags.isNotEmpty) {
      query = query.inFilter(
          'correction_categories.id', tags.map((tag) => tag.id).toList());
    }

    var data;
    try {
      data = await query.order('created_at',
          ascending: false); // Remember to do any remaining ordering manually
    } on PostgrestException catch (error) {
      myLog(
          'Error getting corrections: ${error.code} ${error.hint} ${error.message} ${error.details}');
      throw error;
    }

    final corrections = List<CorrectionWithLinks>.from(data.map((item) {
      final correction = Corrections.fromJson(item);
      final originalPhrase = Phrases.fromJson(item['original_phrase']);
      final correctedPhrase = Phrases.fromJson(item['corrected_phrase']);
      final categories = List<CorrectionCategories>.from(
          item['correction_categories']
              .map((category) => CorrectionCategories.fromJson(category)));
      return CorrectionWithLinks(
        correction: correction,
        originalPhrase: originalPhrase,
        correctedPhrase: correctedPhrase,
        categories: categories,
      );
    }));
    return corrections;
  }

  Future<List<String>> getCorrectionIdsWithEntryId(String? entryId) async {
    try {
      if (entryId != null) {
        final result = await client.from('corrections').select('''
            id,
            corrected_phrase:corrected_phrase_id(
              diary_version:diary_version_id(
                diary_entry_id
              )
            )
          ''').eq('corrected_phrase.diary_version.diary_entry_id', entryId);

        return (result as List)
            .map((correction) => correction['id'] as String)
            .toList();
      } else {
        final result = await client
            .from('corrections')
            .select('id')
            .eq('user_id', client.auth.currentUser!.id);

        return (result as List)
            .map((correction) => correction['id'] as String)
            .toList();
      }
    } catch (e) {
      print('Error getting correction ids: $e');
      return [];
    }
  }

  Future<Map<String, String>> getEntryId(String versionId) async {
    return await client
        .from('diary_versions')
        .select('diary_entry_id, id')
        .eq('id', versionId)
        .limit(1)
        .single()
        .then((value) => {
              'diary_entry_id': value['diary_entry_id'] as String,
              'diary_version_id': value['id'] as String,
            });
  }

  Future<void> updateCorrectionSavedness(
      String correctionId, bool isSaved) async {
    await client
        .from('corrections')
        .update({'is_saved': isSaved}).eq('id', correctionId);
  }

  Future<List<Corrections>> getSavedCorrections() async {
    return await client
        .from('corrections')
        .select('*')
        .eq('user_id', client.auth.currentUser!.id)
        .eq('is_saved', true)
        .order('created_at', ascending: false)
        .withConverter(Corrections.converter);
  }
}

class CorrectionWithLinks extends Corrections {
  final Phrases originalPhrase;
  final Phrases correctedPhrase;
  final List<CorrectionCategories> categories;

  CorrectionWithLinks({
    required Corrections correction,
    required this.originalPhrase,
    required this.correctedPhrase,
    required this.categories,
  }) : super(
          id: correction.id,
          userId: correction.userId,
          originalPhraseId: correction.originalPhraseId,
          correctedPhraseId: correction.correctedPhraseId,
          explanationUserLanguage: correction.explanationUserLanguage,
          isSaved: correction.isSaved,
          createdAt: correction.createdAt,
          updatedAt: correction.updatedAt,
          likes: correction.likes,
          explanationDiaryLanguage: correction.explanationDiaryLanguage,
          deepExplanationDiaryLanguage: correction.deepExplanationDiaryLanguage,
          deepExplanationUserLanguage: correction.deepExplanationUserLanguage,
        );
}
