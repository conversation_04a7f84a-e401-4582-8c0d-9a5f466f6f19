import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../models/generated_classes.dart';

class SupabaseNoteRepository {
  SupabaseNoteRepository(this.client);
  final SupabaseClient client;

  String get _userId => client.auth.currentUser!.id;

  Future<DiaryVersions> getOriginalVersion(String entryId) async {
    return await client
        .from('diary_versions')
        .select('*')
        .eq('diary_entry_id', entryId)
        .eq('version_type', 'original')
        .single()
        .withConverter(DiaryVersions.converterSingle);
  }

  Future<DiaryVersions> getCorrectedVersion(String entryId) async {
    return await client
        .from('diary_versions')
        .select('*')
        .eq('diary_entry_id', entryId)
        .eq('version_type', 'corrected')
        .single()
        .withConverter(DiaryVersions.converterSingle);
  }

  Future<DiaryEntries> getEntryById(String entryId) async {
    return await client
        .from('diary_entries')
        .select('*')
        .eq('user_id', _userId)
        .eq('id', entryId)
        .single()
        .withConverter(DiaryEntries.converterSingle);
  }

  Future<List<DiaryEntries>> getEntries(int? year, int? month) async {
    return await client
        .from('diary_entries')
        .select('*')
        .eq('user_id', _userId)
        .order('created_at', ascending: false)
        .withConverter(DiaryEntries.converter);
  }

  Future<int> getDiaryNumber() async {
    final response = await client
        .from('diary_entries')
        .select('count(*)')
        .eq('user_id', _userId)
        .single();

    return response['count'] as int;
  }

  Future<List<DiaryEntries>> getDiaryDateList() async {
    return await client
        .from('diary_entries')
        .select('created_at')
        .eq('user_id', _userId)
        .order('created_at', ascending: false)
        .withConverter(DiaryEntries.converter);
  }

  Future<List<String>> getEntryIds() async {
    final result = await client
        .from('diary_entries')
        .select('id')
        .eq('user_id', _userId)
        .order('created_at', ascending: false);

    return result.map((e) => e['id'] as String).toList();
  }

  Future<List<DiaryVersionChunks>> getDiaryVersionChunks(
      String versionId) async {
    return await client
        .from('diary_version_chunks')
        .select('*')
        .eq('diary_version_id', versionId)
        .order('sequence_number', ascending: false)
        .withConverter(DiaryVersionChunks.converter);
  }

  Future<List<Map<String, dynamic>>> getPhrases(
      String versionId, bool isOriginal) async {
    final fkey = isOriginal
        ? 'corrections_original_phrase_id_fkey'
        : 'corrections_corrected_phrase_id_fkey';

    final response = await client
        .from('phrases')
        .select('*, corrections!$fkey(id)')
        .eq('diary_version_id', versionId)
        .order('start_index', ascending: true);
    print(response);
    return response;
  }

  Future<DiaryEntries?> getEntryByDate(String entryDate) async {
    final response = await client
        .from('diary_entries')
        .select('*')
        .eq('user_id', _userId)
        .eq('entry_date', entryDate)
        .withConverter(DiaryEntries.converter);

    return response.isEmpty ? null : response.first;
  }
}
