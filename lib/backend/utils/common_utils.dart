import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';

import '../../utils/my_logger.dart';

final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
Future<String> getDeviceInfo() async {
  late String _deviceInfo;
  try {
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfoPlugin.androidInfo;
      _deviceInfo = androidInfo.toString();
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfoPlugin.iosInfo;
      _deviceInfo = iosInfo.toString();
    }
  } catch (e) {
    myLog('getDeviceInfo error: $e', LogLevel.error);
    throw e;
  }

  return _deviceInfo;
}

extension Tap<T> on List<T> {
  List<T> tap(Function(List<T>) block) {
    block(this);
    return this;
  }
}