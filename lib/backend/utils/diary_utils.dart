import 'package:flutter/material.dart';

bool isEmoji(String str) {
  final emojiRegex =
      RegExp(r'(\u00a9|\u00ae|[\u2000-\u3300]|[\uD83C-\uDBFF\uDC00-\uDFFF])');
  return emojiRegex.hasMatch(str);
}

int calculateStreak(List<DateTime> dateList, DateTime _noteDate) {
  int streak = 0;

  dateList.sort((a, b) => b.compareTo(a));

  final mostRecentNoteDate = dateList.first;

  if (DateUtils.isSameDay(mostRecentNoteDate, _noteDate)) {
    streak += 1;
  } else if (mostRecentNoteDate.difference(_noteDate) == Duration(days: 1)) {
    streak += 1;
  } else {
    return streak;
  }

  for (var i = 1; i < dateList.length; i++) {
    final prevNoteDate = dateList[i - 1];
    final currentNoteDate = dateList[i];

    if (DateUtils.isSameDay(
        prevNoteDate.subtract(Duration(days: 1)), currentNoteDate)) {
      streak += 1;
    } else {
      break;
    }
  }

  return streak;
}
