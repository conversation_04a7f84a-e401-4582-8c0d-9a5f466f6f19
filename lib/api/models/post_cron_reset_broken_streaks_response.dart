// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'post_cron_reset_broken_streaks_response.g.dart';

@JsonSerializable()
class PostCronResetBrokenStreaksResponse {
  const PostCronResetBrokenStreaksResponse({
    required this.success,
  });
  
  factory PostCronResetBrokenStreaksResponse.fromJson(Map<String, Object?> json) => _$PostCronResetBrokenStreaksResponseFromJson(json);
  
  final bool success;

  Map<String, Object?> toJson() => _$PostCronResetBrokenStreaksResponseToJson(this);
}
