// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_user_profile_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateUserProfileResponse _$CreateUserProfileResponseFromJson(
        Map<String, dynamic> json) =>
    CreateUserProfileResponse(
      userId: json['user_id'] as String,
      nickname: json['nickname'] as String,
      learningMotivation:
          LearningMotivation.from<PERSON>son(json['learning_motivation'] as String),
      referralCode: json['referral_code'] as String,
      nativeLanguage: json['native_language'] as String?,
      explanationLanguage: ExplanationLanguagePreference.fromJson(
          json['explanation_language'] as String),
    );

Map<String, dynamic> _$CreateUserProfileResponseToJson(
        CreateUserProfileResponse instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'nickname': instance.nickname,
      'learning_motivation':
          _$LearningMotivationEnumMap[instance.learningMotivation]!,
      'referral_code': instance.referralCode,
      'native_language': instance.nativeLanguage,
      'explanation_language':
          _$ExplanationLanguagePreferenceEnumMap[instance.explanationLanguage]!,
    };

const _$LearningMotivationEnumMap = {
  LearningMotivation.career: 'career',
  LearningMotivation.emigration: 'emigration',
  LearningMotivation.fun: 'fun',
  LearningMotivation.other: 'other',
  LearningMotivation.social: 'social',
  LearningMotivation.studyAbroad: 'study_abroad',
  LearningMotivation.$unknown: r'$unknown',
};

const _$ExplanationLanguagePreferenceEnumMap = {
  ExplanationLanguagePreference.diaryLanguage: 'diary_language',
  ExplanationLanguagePreference.nativeLanguage: 'native_language',
  ExplanationLanguagePreference.$unknown: r'$unknown',
};
