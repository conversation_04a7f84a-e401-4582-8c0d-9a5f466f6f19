// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'error.g.dart';

@JsonSerializable()
class Error {
  const Error({
    required this.error,
    required this.traceId,
  });
  
  factory Error.fromJson(Map<String, Object?> json) => _$ErrorFromJson(json);
  
  /// Error message
  final String error;

  /// Trace ID for error tracking
  final String traceId;

  Map<String, Object?> toJson() => _$ErrorToJson(this);
}
