// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'recovered_task_data.g.dart';

@JsonSerializable()
class RecoveredTaskData {
  const RecoveredTaskData({
    required this.taskId,
    required this.type,
    required this.userId,
  });
  
  factory RecoveredTaskData.fromJson(Map<String, Object?> json) => _$RecoveredTaskDataFromJson(json);
  
  final String taskId;
  final String type;
  final String userId;

  Map<String, Object?> toJson() => _$RecoveredTaskDataToJson(this);
}
