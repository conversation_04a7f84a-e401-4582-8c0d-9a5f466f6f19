// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'streak_calendar_entry.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StreakCalendarEntry _$StreakCalendarEntryFromJson(Map<String, dynamic> json) =>
    StreakCalendarEntry(
      date: json['date'] as String,
      rewardAmount: (json['reward_amount'] as num).toInt(),
      isBonus: json['is_bonus'] as bool,
      isClaimed: json['is_claimed'] as bool,
      dayInStreak: (json['day_in_streak'] as num).toInt(),
    );

Map<String, dynamic> _$StreakCalendarEntryToJson(
        StreakCalendarEntry instance) =>
    <String, dynamic>{
      'date': instance.date,
      'reward_amount': instance.rewardAmount,
      'is_bonus': instance.isBonus,
      'is_claimed': instance.isClaimed,
      'day_in_streak': instance.dayInStreak,
    };
