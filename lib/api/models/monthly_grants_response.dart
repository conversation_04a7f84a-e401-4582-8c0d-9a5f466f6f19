// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'monthly_grants_response.g.dart';

@JsonSerializable()
class MonthlyGrantsResponse {
  const MonthlyGrantsResponse({
    required this.success,
    required this.processedAt,
  });
  
  factory MonthlyGrantsResponse.fromJson(Map<String, Object?> json) => _$MonthlyGrantsResponseFromJson(json);
  
  /// Whether the monthly grants were processed successfully
  final bool success;

  /// When the monthly grants were processed
  final DateTime processedAt;

  Map<String, Object?> toJson() => _$MonthlyGrantsResponseToJson(this);
}
