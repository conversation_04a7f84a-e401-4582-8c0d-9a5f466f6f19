// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'delete_user_response.g.dart';

@JsonSerializable()
class DeleteUserResponse {
  const DeleteUserResponse({
    required this.id,
  });
  
  factory DeleteUserResponse.fromJson(Map<String, Object?> json) => _$DeleteUserResponseFromJson(json);
  
  /// ID of the deleted user
  final String id;

  Map<String, Object?> toJson() => _$DeleteUserResponseToJson(this);
}
