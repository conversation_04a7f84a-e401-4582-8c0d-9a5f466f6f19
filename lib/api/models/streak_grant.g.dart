// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'streak_grant.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StreakGrant _$StreakGrantFromJson(Map<String, dynamic> json) => StreakGrant(
      amount: (json['amount'] as num).toInt(),
      type: StreakGrantType.fromJson(json['type'] as String),
      milestone: StreakMilestone.fromJson(json['milestone'] as String),
    );

Map<String, dynamic> _$StreakGrantToJson(StreakGrant instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'type': _$StreakGrantTypeEnumMap[instance.type]!,
      'milestone': _$StreakMilestoneEnumMap[instance.milestone]!,
    };

const _$StreakGrantTypeEnumMap = {
  StreakGrantType.streakMilestone: 'streak_milestone',
  StreakGrantType.$unknown: r'$unknown',
};

const _$StreakMilestoneEnumMap = {
  StreakMilestone.value5Day: '5_day',
  StreakMilestone.value10Day: '10_day',
  StreakMilestone.value30Day: '30_day',
  StreakMilestone.$unknown: r'$unknown',
};
