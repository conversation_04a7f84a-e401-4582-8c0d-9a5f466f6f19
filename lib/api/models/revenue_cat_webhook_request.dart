// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'revenue_cat_webhook_request.g.dart';

@JsonSerializable()
class RevenueCatWebhookRequest {
  const RevenueCatWebhookRequest({
    required this.apiVersion,
    required this.event,
  });
  
  factory RevenueCatWebhookRequest.fromJson(Map<String, Object?> json) => _$RevenueCatWebhookRequestFromJson(json);
  
  /// RevenueCat API version
  @JsonKey(name: 'api_version')
  final String apiVersion;

  /// RevenueCat event payload
  final Map<String, dynamic> event;

  Map<String, Object?> toJson() => _$RevenueCatWebhookRequestToJson(this);
}
