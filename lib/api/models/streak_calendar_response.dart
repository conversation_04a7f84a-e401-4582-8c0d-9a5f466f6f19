// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

import 'streak_calendar_entry.dart';

part 'streak_calendar_response.g.dart';

@JsonSerializable()
class StreakCalendarResponse {
  const StreakCalendarResponse({
    required this.startDate,
    required this.endDate,
    required this.rewards,
  });
  
  factory StreakCalendarResponse.fromJson(Map<String, Object?> json) => _$StreakCalendarResponseFromJson(json);
  
  /// Start date of the calendar range (YYYY-MM-DD)
  @Json<PERSON>ey(name: 'start_date')
  final String startDate;

  /// End date of the calendar range (YYYY-MM-DD)
  @Json<PERSON><PERSON>(name: 'end_date')
  final String endDate;

  /// List of daily rewards in the calendar range
  final List<StreakCalendarEntry> rewards;

  Map<String, Object?> toJson() => _$StreakCalendarResponseToJson(this);
}
