// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

import 'feedback_type.dart';

part 'correction_feedback_request_body.g.dart';

@JsonSerializable()
class CorrectionFeedbackRequestBody {
  const CorrectionFeedbackRequestBody({
    required this.type,
    required this.userId,
  });
  
  factory CorrectionFeedbackRequestBody.fromJson(Map<String, Object?> json) => _$CorrectionFeedbackRequestBodyFromJson(json);
  
  final FeedbackType type;
  @J<PERSON><PERSON><PERSON>(name: 'user_id')
  final String userId;

  Map<String, Object?> toJson() => _$CorrectionFeedbackRequestBodyToJson(this);
}
