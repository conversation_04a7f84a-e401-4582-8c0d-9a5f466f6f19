// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

@JsonEnum()
enum StreakMilestone {
  @JsonValue('5_day')
  value5Day('5_day'),
  @JsonValue('10_day')
  value10Day('10_day'),
  @JsonValue('30_day')
  value30Day('30_day'),
  /// Default value for all unparsed values, allows backward compatibility when adding new values on the backend.
  $unknown(null);

  const StreakMilestone(this.json);

  factory StreakMilestone.fromJson(String json) => values.firstWhere(
        (e) => e.json == json,
        orElse: () => $unknown,
      );

  final String? json;
}
