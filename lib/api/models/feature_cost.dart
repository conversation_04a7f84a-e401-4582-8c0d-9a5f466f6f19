// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

import 'feature_cost_type.dart';
import 'subscription_type.dart';

part 'feature_cost.g.dart';

/// Mapping of feature IDs to their cost configuration
@JsonSerializable()
class FeatureCost {
  const FeatureCost({
    required this.cost,
    required this.type,
    required this.freeFor,
  });
  
  factory FeatureCost.fromJson(Map<String, Object?> json) => _$FeatureCostFromJson(json);
  
  /// Cost of the feature
  final num cost;
  final FeatureCostType type;

  /// List of subscriptions that get the feature for free
  final List<SubscriptionType> freeFor;

  Map<String, Object?> toJson() => _$FeatureCostToJson(this);
}
