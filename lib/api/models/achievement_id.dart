// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

/// Identifier for an achievement
@JsonEnum()
enum AchievementId {
  @JsonValue('diary_complete')
  diaryComplete('diary_complete'),
  @JsonValue('perfect_score')
  perfectScore('perfect_score'),
  /// Default value for all unparsed values, allows backward compatibility when adding new values on the backend.
  $unknown(null);

  const AchievementId(this.json);

  factory AchievementId.fromJson(String json) => values.firstWhere(
        (e) => e.json == json,
        orElse: () => $unknown,
      );

  final String? json;
}
