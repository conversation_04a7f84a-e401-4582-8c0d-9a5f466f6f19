// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'achievement_grant.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AchievementGrant _$AchievementGrantFromJson(Map<String, dynamic> json) =>
    AchievementGrant(
      amount: (json['amount'] as num).toInt(),
      type: AchievementGrantType.fromJson(json['type'] as String),
      achievementId: AchievementId.fromJson(json['achievementId'] as String),
      achievementType:
          AchievementType.fromJson(json['achievementType'] as String),
    );

Map<String, dynamic> _$AchievementGrantToJson(AchievementGrant instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'type': _$AchievementGrantTypeEnumMap[instance.type]!,
      'achievementId': _$AchievementIdEnumMap[instance.achievementId]!,
      'achievementType': _$AchievementTypeEnumMap[instance.achievementType]!,
    };

const _$AchievementGrantTypeEnumMap = {
  AchievementGrantType.achievement: 'achievement',
  AchievementGrantType.$unknown: r'$unknown',
};

const _$AchievementIdEnumMap = {
  AchievementId.diaryComplete: 'diary_complete',
  AchievementId.perfectScore: 'perfect_score',
  AchievementId.$unknown: r'$unknown',
};

const _$AchievementTypeEnumMap = {
  AchievementType.oneTime: 'one_time',
  AchievementType.repeatable: 'repeatable',
  AchievementType.$unknown: r'$unknown',
};
