// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'deep_explanation_data.g.dart';

@JsonSerializable()
class DeepExplanationData {
  const DeepExplanationData({
    required this.correctionId,
    required this.explanation,
    required this.explanationUserLanguage,
  });
  
  factory DeepExplanationData.fromJson(Map<String, Object?> json) => _$DeepExplanationDataFromJson(json);
  
  /// Unique identifier for the correction
  final String correctionId;

  /// Deep explanation in diary language (English)
  final String explanation;

  /// Deep explanation in user language (Korean)
  final String explanationUserLanguage;

  Map<String, Object?> toJson() => _$DeepExplanationDataToJson(this);
}
