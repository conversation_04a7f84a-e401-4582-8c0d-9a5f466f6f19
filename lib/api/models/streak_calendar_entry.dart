// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'streak_calendar_entry.g.dart';

@JsonSerializable()
class StreakCalendarEntry {
  const StreakCalendarEntry({
    required this.date,
    required this.rewardAmount,
    required this.isBonus,
    required this.isClaimed,
    required this.dayInStreak,
  });
  
  factory StreakCalendarEntry.fromJson(Map<String, Object?> json) => _$StreakCalendarEntryFromJson(json);
  
  /// Date in YYYY-MM-DD format
  final String date;

  /// Amount of currency to be rewarded
  @Json<PERSON><PERSON>(name: 'reward_amount')
  final int rewardAmount;

  /// Whether this is a bonus reward (every 5th day)
  @JsonKey(name: 'is_bonus')
  final bool isBonus;

  /// Whether the user has already claimed this day's reward
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_claimed')
  final bool isClaimed;

  /// Which day in the streak this represents
  @JsonKey(name: 'day_in_streak')
  final int dayInStreak;

  Map<String, Object?> toJson() => _$StreakCalendarEntryToJson(this);
}
