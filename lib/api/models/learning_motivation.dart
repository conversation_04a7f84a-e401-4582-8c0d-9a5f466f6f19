// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

@JsonEnum()
enum LearningMotivation {
  @JsonValue('career')
  career('career'),
  @JsonValue('emigration')
  emigration('emigration'),
  @JsonValue('fun')
  fun('fun'),
  @JsonValue('other')
  other('other'),
  @JsonValue('social')
  social('social'),
  @JsonValue('study_abroad')
  studyAbroad('study_abroad'),
  /// Default value for all unparsed values, allows backward compatibility when adding new values on the backend.
  $unknown(null);

  const LearningMotivation(this.json);

  factory LearningMotivation.fromJson(String json) => values.firstWhere(
        (e) => e.json == json,
        orElse: () => $unknown,
      );

  final String? json;
}
