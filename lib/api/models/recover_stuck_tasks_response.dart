// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

import 'recovered_task_data.dart';

part 'recover_stuck_tasks_response.g.dart';

@JsonSerializable()
class RecoverStuckTasksResponse {
  const RecoverStuckTasksResponse({
    required this.recovered,
    required this.tasks,
  });
  
  factory RecoverStuckTasksResponse.fromJson(Map<String, Object?> json) => _$RecoverStuckTasksResponseFromJson(json);
  
  final num recovered;
  final List<RecoveredTaskData> tasks;

  Map<String, Object?> toJson() => _$RecoverStuckTasksResponseToJson(this);
}
