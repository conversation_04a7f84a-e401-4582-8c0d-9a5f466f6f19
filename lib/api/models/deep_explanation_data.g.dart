// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'deep_explanation_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeepExplanationData _$DeepExplanationDataFromJson(Map<String, dynamic> json) =>
    DeepExplanationData(
      correctionId: json['correctionId'] as String,
      explanation: json['explanation'] as String,
      explanationUserLanguage: json['explanationUserLanguage'] as String,
    );

Map<String, dynamic> _$DeepExplanationDataToJson(
        DeepExplanationData instance) =>
    <String, dynamic>{
      'correctionId': instance.correctionId,
      'explanation': instance.explanation,
      'explanationUserLanguage': instance.explanationUserLanguage,
    };
