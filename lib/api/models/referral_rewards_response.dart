// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'referral_rewards_response.g.dart';

@JsonSerializable()
class ReferralRewardsResponse {
  const ReferralRewardsResponse({
    required this.perSignup,
    required this.inviteeReward,
  });
  
  factory ReferralRewardsResponse.fromJson(Map<String, Object?> json) => _$ReferralRewardsResponseFromJson(json);
  
  /// Amount of currency rewarded to the referrer when someone signs up using their code
  @JsonKey(name: 'per_signup')
  final int perSignup;

  /// Amount of currency rewarded to the person who signs up using a referral code
  @JsonKey(name: 'invitee_reward')
  final int inviteeReward;

  Map<String, Object?> toJson() => _$ReferralRewardsResponseToJson(this);
}
