// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'periodic_grant.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PeriodicGrant _$PeriodicGrantFromJson(Map<String, dynamic> json) =>
    PeriodicGrant(
      amount: (json['amount'] as num).toInt(),
      type: PeriodicGrantType.fromJson(json['type'] as String),
      period: PeriodicGrantPeriod.fromJson(json['period'] as String),
      subscriptionType:
          SubscriptionType.from<PERSON>son(json['subscriptionType'] as String),
    );

Map<String, dynamic> _$PeriodicGrantToJson(PeriodicGrant instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'type': _$PeriodicGrantTypeEnumMap[instance.type]!,
      'period': _$PeriodicGrantPeriodEnumMap[instance.period]!,
      'subscriptionType': _$SubscriptionTypeEnumMap[instance.subscriptionType]!,
    };

const _$PeriodicGrantTypeEnumMap = {
  PeriodicGrantType.periodicReward: 'periodic_reward',
  PeriodicGrantType.$unknown: r'$unknown',
};

const _$PeriodicGrantPeriodEnumMap = {
  PeriodicGrantPeriod.initial: 'initial',
  PeriodicGrantPeriod.daily: 'daily',
  PeriodicGrantPeriod.monthly: 'monthly',
  PeriodicGrantPeriod.$unknown: r'$unknown',
};

const _$SubscriptionTypeEnumMap = {
  SubscriptionType.trial: 'trial',
  SubscriptionType.basic: 'basic',
  SubscriptionType.$unknown: r'$unknown',
};
