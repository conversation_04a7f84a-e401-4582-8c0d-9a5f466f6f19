// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'phrase_help_request_body.g.dart';

@JsonSerializable()
class PhraseHelpRequestBody {
  const PhraseHelpRequestBody({
    required this.query,
  });
  
  factory PhraseHelpRequestBody.fromJson(Map<String, Object?> json) => _$PhraseHelpRequestBodyFromJson(json);
  
  /// User query asking for translation help (max 200 chars)
  final String query;

  Map<String, Object?> toJson() => _$PhraseHelpRequestBodyToJson(this);
}
