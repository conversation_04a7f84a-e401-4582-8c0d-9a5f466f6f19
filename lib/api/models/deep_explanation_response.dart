import "./base_currency_response.dart";
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

import 'deep_explanation_data.dart';

part 'deep_explanation_response.g.dart';

@JsonSerializable()
class DeepExplanationResponse  implements BaseCurrencyResponse {
  const DeepExplanationResponse({
    required this.updatedCurrencyBalance,
    required this.data,
  });
  
  factory DeepExplanationResponse.fromJson(Map<String, Object?> json) => _$DeepExplanationResponseFromJson(json);
  
  /// User's updated currency balance after the operation
  final num updatedCurrencyBalance;
  final DeepExplanationData data;

  Map<String, Object?> toJson() => _$DeepExplanationResponseToJson(this);
}
