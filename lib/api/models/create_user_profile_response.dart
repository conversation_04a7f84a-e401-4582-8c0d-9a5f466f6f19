// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

import 'explanation_language_preference.dart';
import 'learning_motivation.dart';

part 'create_user_profile_response.g.dart';

@JsonSerializable()
class CreateUserProfileResponse {
  const CreateUserProfileResponse({
    required this.userId,
    required this.nickname,
    required this.learningMotivation,
    required this.referralCode,
    required this.nativeLanguage,
    required this.explanationLanguage,
  });
  
  factory CreateUserProfileResponse.fromJson(Map<String, Object?> json) => _$CreateUserProfileResponseFromJson(json);
  
  /// The ID of the created user profile
  @JsonKey(name: 'user_id')
  final String userId;

  /// User's chosen nickname
  final String nickname;
  @JsonKey(name: 'learning_motivation')
  final LearningMotivation learningMotivation;

  /// The newly generated referral code for this user
  @<PERSON><PERSON><PERSON><PERSON>(name: 'referral_code')
  final String referralCode;

  /// User's native language (ISO 639-1 code), null if not set
  @JsonKey(name: 'native_language')
  final String? nativeLanguage;
  @JsonKey(name: 'explanation_language')
  final ExplanationLanguagePreference explanationLanguage;

  Map<String, Object?> toJson() => _$CreateUserProfileResponseToJson(this);
}
