// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'upgrade_check_response.g.dart';

@JsonSerializable()
class UpgradeCheckResponse {
  const UpgradeCheckResponse({
    required this.upgradeRequired,
  });
  
  factory UpgradeCheckResponse.fromJson(Map<String, Object?> json) => _$UpgradeCheckResponseFromJson(json);
  
  /// Whether an upgrade is required for this build number
  final bool upgradeRequired;

  Map<String, Object?> toJson() => _$UpgradeCheckResponseToJson(this);
}
