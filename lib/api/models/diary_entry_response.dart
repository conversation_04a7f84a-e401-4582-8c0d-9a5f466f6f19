import "./base_currency_response.dart";
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

import 'currency_grant.dart';
import 'data.dart';

part 'diary_entry_response.g.dart';

@JsonSerializable()
class DiaryEntryResponse  implements BaseCurrencyResponse {
  const DiaryEntryResponse({
    required this.updatedCurrencyBalance,
    required this.data,
    required this.currencyGrants,
  });
  
  factory DiaryEntryResponse.fromJson(Map<String, Object?> json) => _$DiaryEntryResponseFromJson(json);
  
  /// User's updated currency balance after the operation
  final num updatedCurrencyBalance;
  final Data data;

  /// Currency grants awarded by this action, if any
  final List<CurrencyGrant> currencyGrants;

  Map<String, Object?> toJson() => _$DiaryEntryResponseToJson(this);
}
