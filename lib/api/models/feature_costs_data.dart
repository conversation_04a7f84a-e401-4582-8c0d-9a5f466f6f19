// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

import 'feature_cost.dart';

part 'feature_costs_data.g.dart';

@JsonSerializable()
class FeatureCostsData {
  const FeatureCostsData({
    required this.deepExplanation,
    required this.correctionChat,
  });
  
  factory FeatureCostsData.fromJson(Map<String, Object?> json) => _$FeatureCostsDataFromJson(json);
  
  @Json<PERSON>ey(name: 'deep_explanation')
  final FeatureCost deepExplanation;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'correction_chat')
  final FeatureCost correctionChat;

  Map<String, Object?> toJson() => _$FeatureCostsDataToJson(this);
}
