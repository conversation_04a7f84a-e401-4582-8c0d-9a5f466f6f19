// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recover_stuck_tasks_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RecoverStuckTasksResponse _$RecoverStuckTasksResponseFromJson(
        Map<String, dynamic> json) =>
    RecoverStuckTasksResponse(
      recovered: json['recovered'] as num,
      tasks: (json['tasks'] as List<dynamic>)
          .map((e) => RecoveredTaskData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$RecoverStuckTasksResponseToJson(
        RecoverStuckTasksResponse instance) =>
    <String, dynamic>{
      'recovered': instance.recovered,
      'tasks': instance.tasks,
    };
