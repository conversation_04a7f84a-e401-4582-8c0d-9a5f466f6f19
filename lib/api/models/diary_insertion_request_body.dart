// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'diary_insertion_request_body.g.dart';

@JsonSerializable()
class DiaryInsertionRequestBody {
  const DiaryInsertionRequestBody({
    required this.userId,
    required this.content,
    required this.weather,
    required this.date,
  });
  
  factory DiaryInsertionRequestBody.fromJson(Map<String, Object?> json) => _$DiaryInsertionRequestBodyFromJson(json);
  
  /// Unique identifier for the user
  @<PERSON>son<PERSON><PERSON>(name: 'user_id')
  final String userId;

  /// The diary entry content
  final String content;

  /// Weather condition during the entry
  final String weather;

  /// Date of the diary entry (YYYY-MM-DD)
  final String date;

  Map<String, Object?> toJson() => _$DiaryInsertionRequestBodyToJson(this);
}
