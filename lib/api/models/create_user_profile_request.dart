// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

import 'learning_motivation.dart';

part 'create_user_profile_request.g.dart';

@JsonSerializable()
class CreateUserProfileRequest {
  const CreateUserProfileRequest({
    required this.learningMotivation,
    required this.nickname,
    required this.explanationLanguage,
    this.referralCode,
    this.nativeLanguage,
  });
  
  factory CreateUserProfileRequest.fromJson(Map<String, Object?> json) => _$CreateUserProfileRequestFromJson(json);
  
  @JsonKey(name: 'learning_motivation')
  final LearningMotivation learningMotivation;

  /// User's chosen nickname
  final String nickname;

  /// Optional referral code of the person who referred this user
  @<PERSON><PERSON><PERSON><PERSON>(name: 'referral_code')
  final String? referralCode;

  /// Optional native language of the user (ISO 639-1 code)
  @Json<PERSON>ey(name: 'native_language')
  final String? nativeLanguage;
  @JsonKey(name: 'explanation_language')
  final dynamic explanationLanguage;

  Map<String, Object?> toJson() => _$CreateUserProfileRequestToJson(this);
}
