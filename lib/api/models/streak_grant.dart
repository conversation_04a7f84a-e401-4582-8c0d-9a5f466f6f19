// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

import 'streak_grant_type.dart';
import 'streak_milestone.dart';

part 'streak_grant.g.dart';

@JsonSerializable()
class StreakGrant {
  const StreakGrant({
    required this.amount,
    required this.type,
    required this.milestone,
  });
  
  factory StreakGrant.fromJson(Map<String, Object?> json) => _$StreakGrantFromJson(json);
  
  /// The amount of currency granted
  final int amount;
  final StreakGrantType type;
  final StreakMilestone milestone;

  Map<String, Object?> toJson() => _$StreakGrantToJson(this);
}
