// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'phrase_help_response_body.g.dart';

@JsonSerializable()
class PhraseHelpResponseBody {
  const PhraseHelpResponseBody({
    required this.response,
  });
  
  factory PhraseHelpResponseBody.fromJson(Map<String, Object?> json) => _$PhraseHelpResponseBodyFromJson(json);
  
  /// The response to the request for help with English phrasing.
  final String response;

  Map<String, Object?> toJson() => _$PhraseHelpResponseBodyToJson(this);
}
