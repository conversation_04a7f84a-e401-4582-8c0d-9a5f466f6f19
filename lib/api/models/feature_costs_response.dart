// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

import 'feature_costs_data.dart';

part 'feature_costs_response.g.dart';

@JsonSerializable()
class FeatureCostsResponse {
  const FeatureCostsResponse({
    required this.featureCosts,
  });
  
  factory FeatureCostsResponse.fromJson(Map<String, Object?> json) => _$FeatureCostsResponseFromJson(json);
  
  final FeatureCostsData featureCosts;

  Map<String, Object?> toJson() => _$FeatureCostsResponseToJson(this);
}
