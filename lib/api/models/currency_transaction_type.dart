// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

@JsonEnum()
enum CurrencyTransactionType {
  @JsonValue('achievement')
  achievement('achievement'),
  @JsonValue('feature_usage')
  featureUsage('feature_usage'),
  @JsonValue('feature_usage_refund')
  featureUsageRefund('feature_usage_refund'),
  @JsonValue('periodic_reward')
  periodicReward('periodic_reward'),
  @JsonValue('referral_reward')
  referralReward('referral_reward'),
  @JsonValue('streak_milestone')
  streakMilestone('streak_milestone'),
  /// Default value for all unparsed values, allows backward compatibility when adding new values on the backend.
  $unknown(null);

  const CurrencyTransactionType(this.json);

  factory CurrencyTransactionType.fromJson(String json) => values.firstWhere(
        (e) => e.json == json,
        orElse: () => $unknown,
      );

  final String? json;
}
