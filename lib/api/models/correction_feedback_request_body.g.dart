// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'correction_feedback_request_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CorrectionFeedbackRequestBody _$CorrectionFeedbackRequestBodyFromJson(
        Map<String, dynamic> json) =>
    CorrectionFeedbackRequestBody(
      type: FeedbackType.fromJson(json['type'] as String),
      userId: json['user_id'] as String,
    );

Map<String, dynamic> _$CorrectionFeedbackRequestBodyToJson(
        CorrectionFeedbackRequestBody instance) =>
    <String, dynamic>{
      'type': _$FeedbackTypeEnumMap[instance.type]!,
      'user_id': instance.userId,
    };

const _$FeedbackTypeEnumMap = {
  FeedbackType.upvote: 'upvote',
  FeedbackType.downvote: 'downvote',
  FeedbackType.$unknown: r'$unknown',
};
