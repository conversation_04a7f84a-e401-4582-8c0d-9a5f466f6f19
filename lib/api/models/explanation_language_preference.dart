// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

/// Preference for correction language
@JsonEnum()
enum ExplanationLanguagePreference {
  @JsonValue('diary_language')
  diaryLanguage('diary_language'),
  @JsonValue('native_language')
  nativeLanguage('native_language'),
  /// Default value for all unparsed values, allows backward compatibility when adding new values on the backend.
  $unknown(null);

  const ExplanationLanguagePreference(this.json);

  factory ExplanationLanguagePreference.fromJson(String json) => values.firstWhere(
        (e) => e.json == json,
        orElse: () => $unknown,
      );

  final String? json;
}
