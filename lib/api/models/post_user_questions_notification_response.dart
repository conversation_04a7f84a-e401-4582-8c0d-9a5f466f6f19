// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'post_user_questions_notification_response.g.dart';

@JsonSerializable()
class PostUserQuestionsNotificationResponse {
  const PostUserQuestionsNotificationResponse({
    required this.id,
  });
  
  factory PostUserQuestionsNotificationResponse.fromJson(Map<String, Object?> json) => _$PostUserQuestionsNotificationResponseFromJson(json);
  
  /// Notification ID
  final String id;

  Map<String, Object?> toJson() => _$PostUserQuestionsNotificationResponseToJson(this);
}
