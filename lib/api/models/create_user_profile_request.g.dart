// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_user_profile_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateUserProfileRequest _$CreateUserProfileRequestFromJson(
        Map<String, dynamic> json) =>
    CreateUserProfileRequest(
      learningMotivation:
          LearningMotivation.fromJson(json['learning_motivation'] as String),
      nickname: json['nickname'] as String,
      explanationLanguage: json['explanation_language'],
      referralCode: json['referral_code'] as String?,
      nativeLanguage: json['native_language'] as String?,
    );

Map<String, dynamic> _$CreateUserProfileRequestToJson(
        CreateUserProfileRequest instance) =>
    <String, dynamic>{
      'learning_motivation':
          _$LearningMotivationEnumMap[instance.learningMotivation]!,
      'nickname': instance.nickname,
      'referral_code': instance.referralCode,
      'native_language': instance.nativeLanguage,
      'explanation_language': instance.explanationLanguage,
    };

const _$LearningMotivationEnumMap = {
  LearningMotivation.career: 'career',
  LearningMotivation.emigration: 'emigration',
  LearningMotivation.fun: 'fun',
  LearningMotivation.other: 'other',
  LearningMotivation.social: 'social',
  LearningMotivation.studyAbroad: 'study_abroad',
  LearningMotivation.$unknown: r'$unknown',
};
