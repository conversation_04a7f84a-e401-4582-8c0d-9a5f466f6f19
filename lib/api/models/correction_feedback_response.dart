// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'correction_feedback_response.g.dart';

@JsonSerializable()
class CorrectionFeedbackResponse {
  const CorrectionFeedbackResponse({
    required this.id,
    required this.likes,
  });
  
  factory CorrectionFeedbackResponse.fromJson(Map<String, Object?> json) => _$CorrectionFeedbackResponseFromJson(json);
  
  /// The ID of the correction that received feedback
  final String id;

  /// The updated likes count for the correction
  final int likes;

  Map<String, Object?> toJson() => _$CorrectionFeedbackResponseToJson(this);
}
