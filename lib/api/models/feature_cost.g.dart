// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'feature_cost.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FeatureCost _$FeatureCostFromJson(Map<String, dynamic> json) => FeatureCost(
      cost: json['cost'] as num,
      type: FeatureCostType.fromJson(json['type'] as String),
      freeFor: (json['freeFor'] as List<dynamic>)
          .map((e) => SubscriptionType.fromJson(e as String))
          .toList(),
    );

Map<String, dynamic> _$FeatureCostToJson(FeatureCost instance) =>
    <String, dynamic>{
      'cost': instance.cost,
      'type': _$FeatureCostTypeEnumMap[instance.type]!,
      'freeFor':
          instance.freeFor.map((e) => _$SubscriptionTypeEnumMap[e]!).toList(),
    };

const _$FeatureCostTypeEnumMap = {
  FeatureCostType.perUse: 'per_use',
  FeatureCostType.$unknown: r'$unknown',
};

const _$SubscriptionTypeEnumMap = {
  SubscriptionType.trial: 'trial',
  SubscriptionType.basic: 'basic',
  SubscriptionType.$unknown: r'$unknown',
};
