// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'user_question_request_body.g.dart';

@JsonSerializable()
class UserQuestionRequestBody {
  const UserQuestionRequestBody({
    required this.title,
    required this.body,
    required this.userId,
    required this.deviceInfo,
  });
  
  factory UserQuestionRequestBody.fromJson(Map<String, Object?> json) => _$UserQuestionRequestBodyFromJson(json);
  
  /// Title of the user question
  final String title;

  /// Content of the user question
  final String body;

  /// UUID of the user asking the question
  final String userId;

  /// Information about the user's device
  final String deviceInfo;

  Map<String, Object?> toJson() => _$UserQuestionRequestBodyToJson(this);
}
