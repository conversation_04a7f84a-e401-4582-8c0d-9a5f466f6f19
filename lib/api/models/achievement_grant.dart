// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

import 'achievement_grant_type.dart';
import 'achievement_id.dart';
import 'achievement_type.dart';

part 'achievement_grant.g.dart';

@JsonSerializable()
class AchievementGrant {
  const AchievementGrant({
    required this.amount,
    required this.type,
    required this.achievementId,
    required this.achievementType,
  });
  
  factory AchievementGrant.fromJson(Map<String, Object?> json) => _$AchievementGrantFromJson(json);
  
  /// The amount of currency granted
  final int amount;
  final AchievementGrantType type;
  final AchievementId achievementId;
  final AchievementType achievementType;

  Map<String, Object?> toJson() => _$AchievementGrantToJson(this);
}
