// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'streak_calendar_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StreakCalendarResponse _$StreakCalendarResponseFromJson(
        Map<String, dynamic> json) =>
    StreakCalendarResponse(
      startDate: json['start_date'] as String,
      endDate: json['end_date'] as String,
      rewards: (json['rewards'] as List<dynamic>)
          .map((e) => StreakCalendarEntry.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$Streak<PERSON>alendarResponseToJson(
        StreakCalendarResponse instance) =>
    <String, dynamic>{
      'start_date': instance.startDate,
      'end_date': instance.endDate,
      'rewards': instance.rewards,
    };
