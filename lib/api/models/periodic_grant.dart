// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

import 'periodic_grant_period.dart';
import 'periodic_grant_type.dart';
import 'subscription_type.dart';

part 'periodic_grant.g.dart';

@JsonSerializable()
class PeriodicGrant {
  const PeriodicGrant({
    required this.amount,
    required this.type,
    required this.period,
    required this.subscriptionType,
  });
  
  factory PeriodicGrant.fromJson(Map<String, Object?> json) => _$PeriodicGrantFromJson(json);
  
  /// The amount of currency granted
  final int amount;
  final PeriodicGrantType type;
  final PeriodicGrantPeriod period;
  final SubscriptionType subscriptionType;

  Map<String, Object?> toJson() => _$PeriodicGrantToJson(this);
}
