// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'base_currency_grant.g.dart';

@JsonSerializable()
class BaseCurrencyGrant {
  const BaseCurrencyGrant({
    required this.amount,
  });
  
  factory BaseCurrencyGrant.fromJson(Map<String, Object?> json) => _$BaseCurrencyGrantFromJson(json);
  
  /// The amount of currency granted
  final int amount;

  Map<String, Object?> toJson() => _$BaseCurrencyGrantToJson(this);
}
