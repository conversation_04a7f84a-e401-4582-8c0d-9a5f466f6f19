// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

/// Subscription type
@JsonEnum()
enum SubscriptionType {
  @JsonValue('trial')
  trial('trial'),
  @JsonValue('basic')
  basic('basic'),
  /// Default value for all unparsed values, allows backward compatibility when adding new values on the backend.
  $unknown(null);

  const SubscriptionType(this.json);

  factory SubscriptionType.fromJson(String json) => values.firstWhere(
        (e) => e.json == json,
        orElse: () => $unknown,
      );

  final String? json;
}
