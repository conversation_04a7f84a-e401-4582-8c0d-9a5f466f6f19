// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:json_annotation/json_annotation.dart';

part 'revenue_cat_webhook_response.g.dart';

@JsonSerializable()
class RevenueCatWebhookResponse {
  const RevenueCatWebhookResponse({
    required this.success,
  });
  
  factory RevenueCatWebhookResponse.fromJson(Map<String, Object?> json) => _$RevenueCatWebhookResponseFromJson(json);
  
  /// Whether the webhook was processed successfully
  final bool success;

  Map<String, Object?> toJson() => _$RevenueCatWebhookResponseToJson(this);
}
