// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

import '../models/monthly_grants_response.dart';
import '../models/post_cron_reset_broken_streaks_response.dart';
import '../models/recover_stuck_tasks_response.dart';

part 'cron_client.g.dart';

@RestApi()
abstract class CronClient {
  factory CronClient(Dio dio, {String? baseUrl}) = _CronClient;

  /// Process monthly gem grants for annual subscriptions
  @POST('/cron/monthly-subscription-grants')
  Future<MonthlyGrantsResponse> postCronMonthlySubscriptionGrants();

  /// Recover stuck tasks and issue refunds
  @POST('/cron/recover-stuck-tasks')
  Future<RecoverStuckTasksResponse> postCronRecoverStuckTasks();

  /// Reset streaks for users who have not written a diary in the last day
  @POST('/cron/reset-broken-streaks')
  Future<PostCronResetBrokenStreaksResponse> postCronResetBrokenStreaks({
    @Header('Authorization') required String authorization,
  });
}
