// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

import '../models/create_user_profile_request.dart';
import '../models/create_user_profile_response.dart';
import '../models/delete_user_response.dart';

part 'user_management_client.g.dart';

@RestApi()
abstract class UserManagementClient {
  factory UserManagementClient(Dio dio, {String? baseUrl}) = _UserManagementClient;

  /// Create a new user profile with optional referral
  @POST('/users/profile')
  Future<CreateUserProfileResponse> postUsersProfile({
    @Body() CreateUserProfileRequest? body,
  });

  /// Delete the authenticated user and all associated data
  @DELETE('/users/me')
  Future<DeleteUserResponse> deleteUsersMe();
}
