// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

import '../models/correction_feedback_request_body.dart';
import '../models/correction_feedback_response.dart';

part 'corrections_client.g.dart';

@RestApi()
abstract class CorrectionsClient {
  factory CorrectionsClient(Dio dio, {String? baseUrl}) = _CorrectionsClient;

  /// Provide feedback (upvote/downvote) for a correction
  @POST('/corrections/{id}/feedback')
  Future<CorrectionFeedbackResponse> postCorrectionsIdFeedback({
    @Path('id') required String id,
    @Body() CorrectionFeedbackRequestBody? body,
  });
}
