// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

import '../models/feature_costs_response.dart';
import '../models/referral_rewards_response.dart';
import '../models/streak_calendar_response.dart';

part 'currency_client.g.dart';

@RestApi()
abstract class CurrencyClient {
  factory CurrencyClient(Dio dio, {String? baseUrl}) = _CurrencyClient;

  /// Expose the cost configuration for feature usage
  @GET('/currency/feature-costs')
  Future<FeatureCostsResponse> getCurrencyFeatureCosts();

  /// Get the streak calendar showing upcoming rewards.
  ///
  /// [days] - Number of days to include in the forecast, starting from today.
  @GET('/currency/rewards/streak-calendar')
  Future<StreakCalendarResponse> getCurrencyRewardsStreakCalendar({
    @Query('days') int days = 14,
  });

  /// Get the referral rewards configuration including per-signup and invitee rewards
  @GET('/currency/rewards/referral')
  Future<ReferralRewardsResponse> getCurrencyRewardsReferral();
}
