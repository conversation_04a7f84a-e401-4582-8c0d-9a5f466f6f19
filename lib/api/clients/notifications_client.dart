// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

import '../models/post_user_questions_notification_response.dart';
import '../models/user_question_request_body.dart';

part 'notifications_client.g.dart';

@RestApi()
abstract class NotificationsClient {
  factory NotificationsClient(Dio dio, {String? baseUrl}) = _NotificationsClient;

  /// Send a notification for a user question
  @POST('/user-questions/notification')
  Future<PostUserQuestionsNotificationResponse> postUserQuestionsNotification({
    @Body() required UserQuestionRequestBody body,
  });
}
