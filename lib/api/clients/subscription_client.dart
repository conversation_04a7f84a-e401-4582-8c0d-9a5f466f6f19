// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

import '../models/revenue_cat_webhook_request.dart';
import '../models/revenue_cat_webhook_response.dart';

part 'subscription_client.g.dart';

@RestApi()
abstract class SubscriptionClient {
  factory SubscriptionClient(Dio dio, {String? baseUrl}) = _SubscriptionClient;

  /// Handle RevenueCat subscription webhook events
  @POST('/revenuecat/webhook')
  Future<RevenueCatWebhookResponse> postRevenuecatWebhook({
    @Body() RevenueCatWebhookRequest? body,
  });
}
