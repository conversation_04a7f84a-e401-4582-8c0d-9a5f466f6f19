// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

import '../models/upgrade_check_response.dart';

part 'system_client.g.dart';

@RestApi()
abstract class SystemClient {
  factory SystemClient(Dio dio, {String? baseUrl}) = _SystemClient;

  /// Check if the application needs to be upgraded based on build number
  @GET('/upgrade-required/{buildNumber}')
  Future<UpgradeCheckResponse> getUpgradeRequiredBuildNumber({
    @Path('buildNumber') required String buildNumber,
  });
}
