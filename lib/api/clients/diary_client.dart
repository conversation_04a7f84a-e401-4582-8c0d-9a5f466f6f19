// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

import '../models/deep_explanation_response.dart';
import '../models/diary_entry_response.dart';
import '../models/diary_insertion_request_body.dart';
import '../models/get_entry_date_response.dart';
import '../models/phrase_help_request_body.dart';
import '../models/phrase_help_response_body.dart';

part 'diary_client.g.dart';

@RestApi()
abstract class DiaryClient {
  factory DiaryClient(Dio dio, {String? baseUrl}) = _DiaryClient;

  /// Create a new diary entry
  @POST('/insert')
  Future<DiaryEntryResponse> postInsert({
    @Body() required DiaryInsertionRequestBody body,
  });

  /// Get the date for which the user can make an entry
  @GET('/entry-date')
  Future<GetEntryDateResponse> getEntryDate({
    @Query('userId') required String userId,
  });

  /// Generate a deep explanation for a correction
  @POST('/corrections/{id}/deep-explain')
  Future<DeepExplanationResponse> postCorrectionsIdDeepExplain({
    @Path('id') required String id,
  });

  /// Provides translation help for specific words/phrases asked by the user.
  @POST('/diary/phrase-help')
  Future<PhraseHelpResponseBody> postDiaryPhraseHelp({
    @Body() required PhraseHelpRequestBody body,
  });
}
