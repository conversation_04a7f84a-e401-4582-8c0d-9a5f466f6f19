// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_import

import 'package:dio/dio.dart';

import 'clients/system_client.dart';
import 'clients/diary_client.dart';
import 'clients/notifications_client.dart';
import 'clients/user_management_client.dart';
import 'clients/corrections_client.dart';
import 'clients/currency_client.dart';
import 'clients/cron_client.dart';
import 'clients/subscription_client.dart';

/// Langda API `v1.0.0`.
///
/// API for the Langda application.
class RestClient {
  RestClient(
    Dio dio, {
    String? baseUrl,
  })  : _dio = dio,
        _baseUrl = baseUrl;

  final Dio _dio;
  final String? _baseUrl;

  static String get version => '1.0.0';

  SystemClient? _system;
  DiaryClient? _diary;
  NotificationsClient? _notifications;
  UserManagementClient? _userManagement;
  CorrectionsClient? _corrections;
  CurrencyClient? _currency;
  CronClient? _cron;
  SubscriptionClient? _subscription;

  SystemClient get system => _system ??= SystemClient(_dio, baseUrl: _baseUrl);

  DiaryClient get diary => _diary ??= DiaryClient(_dio, baseUrl: _baseUrl);

  NotificationsClient get notifications => _notifications ??= NotificationsClient(_dio, baseUrl: _baseUrl);

  UserManagementClient get userManagement => _userManagement ??= UserManagementClient(_dio, baseUrl: _baseUrl);

  CorrectionsClient get corrections => _corrections ??= CorrectionsClient(_dio, baseUrl: _baseUrl);

  CurrencyClient get currency => _currency ??= CurrencyClient(_dio, baseUrl: _baseUrl);

  CronClient get cron => _cron ??= CronClient(_dio, baseUrl: _baseUrl);

  SubscriptionClient get subscription => _subscription ??= SubscriptionClient(_dio, baseUrl: _baseUrl);
}
