import 'package:flutter/foundation.dart';

import '../api/export.dart';
import '../backend/service/diary_service.dart';
import '../queries/diary_query.dart';
import '../states/app_state.dart';

enum ProcessingStatus { idle, processing, completed, error }

class DiaryProcessingService {
  Future<DiaryEntryResponse?> processDiary(String content, String weather,
      DateTime date, VoidCallback callbackAction) async {
    final mutationResult =
        await addDiaryMutationJubo(content, weather, date).mutate();

    callbackAction();
    return mutationResult.data;
  }
}

class DiaryProcessingProvider extends ChangeNotifier {
  ProcessingStatus _status = ProcessingStatus.idle;
  DiaryEntryResponse? _processedDiaryUrl;
  String? _errorMessage;
  bool _shouldShowStreakReview = false;

  ProcessingStatus get status => _status;
  DiaryEntryResponse? get processedDiaryUrl => _processedDiaryUrl;
  String? get errorMessage => _errorMessage;
  bool get shouldShowStreakReview => _shouldShowStreakReview;

  final DiaryProcessingService _service = DiaryProcessingService();

  Future<void> processDiary(String content, String weather, DateTime date,
      VoidCallback onDone) async {
    if (_status == ProcessingStatus.processing) return;
    _status = ProcessingStatus.processing;
    notifyListeners();

    try {
      _processedDiaryUrl =
          await _service.processDiary(content, weather, date, onDone);

      // Check if this diary entry creates a 3-day streak for review popup
      await _checkForStreakReview(date);

      _status = ProcessingStatus.completed;
      notifyListeners();
    } catch (e) {
      _status = ProcessingStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  Future<void> _checkForStreakReview(DateTime entryDate) async {
    try {
      // Check if user has already been shown the streak review
      if (LDAppState().hasShownStreakReview()) {
        return;
      }

      final streakData = await DiaryService().fetchNumberOfDiary(entryDate);
      final currentStreak = streakData['streak'] ?? 0;

      // Show review popup if user just completed their 3rd consecutive day
      if (currentStreak == 3) {
        _shouldShowStreakReview = true;
      }
    } catch (e) {
      // Silently fail - don't interrupt the diary submission process
      print('Error checking streak for review: $e');
    }
  }

  void reset() {
    _status = ProcessingStatus.idle;
    _processedDiaryUrl = null;
    _errorMessage = null;
    notifyListeners();
  }

  void dismissStreakReview() {
    _shouldShowStreakReview = false;
    notifyListeners();
  }
}
