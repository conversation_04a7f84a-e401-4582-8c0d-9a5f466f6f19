import 'package:flutter/material.dart';

class CreateProfileProvider with ChangeNotifier {
  String? _selectedPurpose;
  String? _explanationLanguage;
  String? _nativeLanguage;

  String? get selectedPurpose => _selectedPurpose;
  String? get explanationLanguage => _explanationLanguage;
  String? get nativeLanguage => _nativeLanguage;

  void updateLanguages({String? explanation, String? native}) {
    _explanationLanguage = explanation;
    _nativeLanguage = native;
    // Don't notify listeners here if the update happens before moving to the next page.
    // Notify listeners when the state change should reflect in the UI immediately.
  }

  void updatePurpose(String? purpose) {
    _selectedPurpose = purpose;
    // Notify listeners if the purpose selection should immediately update other parts of the UI.
    // If it's just for the final step, notification might not be needed here.
  }

  // Call this method when moving to the Nickname page or submitting the final form
  // to ensure all dependent widgets have the latest data.
  void finalizeStep() {
    notifyListeners();
  }

  // Optionally, add a reset method if needed when the flow completes or is cancelled
  void reset() {
    _selectedPurpose = null;
    _explanationLanguage = null;
    _nativeLanguage = null;
    notifyListeners();
  }
}
