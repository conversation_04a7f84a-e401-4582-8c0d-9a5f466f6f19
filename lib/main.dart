import 'dart:async';

import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:go_router/go_router.dart'; // Ensure GoRouter is imported
import 'package:kakao_flutter_sdk_user/kakao_flutter_sdk_user.dart';
import 'package:langda/firebase_options.dart';
import 'package:langda/presentation/nav.dart';
import 'package:langda/presentation/pages/mobile/constants.dart';
import 'package:langda/presentation/services/app_state_service.dart';
import 'package:langda/presentation/services/auth_service.dart';
import 'package:langda/providers/process_diary_provider.dart';
import 'package:langda/states/app_state.dart';
import 'package:langda/utils/my_logger.dart';
import 'package:langda/utils/revenucat_helper.dart';
import 'package:langda/utils/sentry_handler.dart';
import 'package:provider/provider.dart'; // Add provider import
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'providers/create_profile_provider.dart'; // Import the new provider

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await Future.wait([
      LDAppState().initializeLDAppState(),
      SentryHandler.init(),
      Supabase.initialize(
        url: 'https://dvuooizbwkktbrxbzpyj.supabase.co',
        anonKey:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.fYPf7sFhA4hoVTvYzCW2pt1jVh75bYcxH3AoHX2c3e4',
        debug: true,
      ),
    ]);
  } catch (e) {
    myLog('Error initializing app: $e', LogLevel.error);
    return;
  }

  // 6e6: Kakao "네이티브 앱 키"
  // e7d: Kakao "REST API 키"
  // 42b: Kakao "자바스크립트 키"
  KakaoSdk.init(
    nativeAppKey: '6e6a93ad25dc965b111e0d0132b328bf',
    loggingEnabled: true,
  );
  CachedQuery.instance.configFlutter(
    config: QueryConfigFlutter(
      cacheDuration: const Duration(hours: 1),
      refetchDuration: kDebugMode
          ? const Duration(seconds: 30)
          : const Duration(minutes: 30),
      refetchOnResume: false,
    ),
  );

  try {
    await Future.wait([
      Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform),
      initPlatformState(),
    ]);
    await Future.wait([
      FirebaseMessaging.instance.requestPermission(provisional: true),
    ]);
  } catch (e) {
    // Log the error to Sentry
    Sentry.captureException(e, stackTrace: StackTrace.current);

    if (e.toString().contains('AuthApiException')) {
      try {
        await Supabase.instance.client.auth.signOut();
      } catch (signOutError) {
        Sentry.captureException(
          signOutError,
          stackTrace: StackTrace.current,
        );
      }
    }
  }

  // --- Provider Setting ---
  final authService = AuthService();
  final appStateService = AppStateService();

  final router = createRouter(authService, appStateService);

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider<AuthService>.value(value: authService),
        ChangeNotifierProvider<AppStateService>.value(
          value: appStateService,
        ),
        routeRedirectorProvider,
        ChangeNotifierProvider<CreateProfileProvider>(
          create: (_) => CreateProfileProvider(),
        ),
        ChangeNotifierProvider(create: (_) => DiaryProcessingProvider())
      ],
      child: SentryWidget(child: MyApp(router: router)),
    ),
  );
}

class MyApp extends StatefulWidget {
  @override
  State<MyApp> createState() => _MyAppState();

  final GoRouter router;
  static _MyAppState of(BuildContext context) {
    if (isMobile(context)) {
      // 모바일 화면에서는 세로 모드만 허용
      SystemChrome.setPreferredOrientations(
          [DeviceOrientation.portraitDown, DeviceOrientation.portraitUp]);
    }
    final _MyAppState? state = context.findAncestorStateOfType<_MyAppState>();
    if (state == null) {
      throw Exception('MyApp state not found in context');
    }
    return state;
  }

  const MyApp({super.key, required this.router});
}

class _MyAppState extends State<MyApp> {
  late Size screenSize;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveSizer(
      builder: (BuildContext context, Orientation orientation,
          ScreenType screenType) {
        screenSize = MediaQuery.of(context).size;

        // Removed FutureBuilder
        return ChangeNotifierProvider(
          create: (_) => LDAppState(),
          child: MaterialApp.router(
            title: 'Langda',
            theme: ThemeData(
              fontFamily: 'Pretendard',
              useMaterial3: true,
              colorScheme: const ColorScheme.light(
                primary: Color(0xFFD6ED17),
                secondary: Color(0xFF606060),
                surface: Colors.white,
              ),
            ),
            routerConfig: widget.router, // Use the router directly
            builder: (context, child) => child ?? const SizedBox(),
            localizationsDelegates: [
              FlutterI18nDelegate(
                translationLoader: FileTranslationLoader(
                  basePath: 'assets/i18n',
                  forcedLocale: LDAppState().getLocale(),
                ),
              ),
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('en'),
              Locale('ko'),
              Locale('ja'),
            ],
            locale: LDAppState().getLocale(),
          ),
        );
      },
    );
  }
}
