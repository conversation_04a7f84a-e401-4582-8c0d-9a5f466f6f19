import 'dart:math';

import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/providers/process_diary_provider.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../backend/model/diary_model.dart';
import '../presentation/pages/component.dart';
import '../presentation/pages/mobile/constants.dart';
import '../presentation/router_constants.dart';

class NotificationBoxModel {
  final bool isFirstDiary;
  final String? nickname;
  final int? gems;
  final DiaryListModelStruct note;

  NotificationBoxModel({
    required this.isFirstDiary,
    this.nickname,
    this.gems,
    required this.note,
  });
}

class NotificationBoxWidget extends StatefulWidget {
  const NotificationBoxWidget({
    super.key,
  });

  @override
  State<NotificationBoxWidget> createState() => _NotificationBoxWidgetState();
}

class _NotificationBoxWidgetState extends State<NotificationBoxWidget>
    with TickerProviderStateMixin {
  late ConfettiController _controllerCenter;
  String? diaryId;

  @override
  void initState() {
    _controllerCenter =
        ConfettiController(duration: const Duration(seconds: 10));
    super.initState();
  }

  @override
  void dispose() {
    _controllerCenter.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final DiaryProcessingProvider diaryProcessingProvider =
        context.watch<DiaryProcessingProvider>();
    return QueryBuilder<NotificationBoxModel>(
      queryKey: 'notification_box',
      builder: (context, state) {
        final modelData = state.data;

        if (modelData != null) {
          diaryId = modelData.note.id;
          if (modelData.isFirstDiary) {
            _controllerCenter.play();
          }
          return Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (modelData.isFirstDiary)
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: _firstNoticeBoxBuilder(
                    context,
                    _controllerCenter,
                    modelData.nickname ?? 'NAME_HERE',
                    modelData.gems?.toString() ?? 'GEMS_HERE',
                    diaryProcessingProvider,
                  ).animate()
                    ..moveY(
                      begin: MediaQuery.of(context).size.height / 4.5 + 50.0,
                      end: MediaQuery.of(context).size.height / 4.5,
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeInOut,
                    )
                    ..fadeIn(
                      begin: 1.0,
                    ),
                ),
              if (!modelData.isFirstDiary)
                Container(
                  alignment: Alignment.center,
                  padding: EdgeInsets.symmetric(
                    horizontal: 15.sp,
                    vertical: 8.sp,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                  ),
                  height: 200,
                  child: _noticeBoxBuilder(
                    context,
                    modelData.note,
                    diaryProcessingProvider,
                  ).animate()
                    ..moveY(
                      begin: 2.0,
                      end: 20.0,
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeInOut,
                    )
                    ..fadeIn(
                      begin: 1.0,
                    ),
                ),
            ],
          );
        } else {
          if (state.status == QueryStatus.loading ||
              state.status == QueryStatus.error ||
              modelData == null) {
            return Container();
          }
        }

        return Container();
      },
    );
  }

  Container _firstNoticeBoxBuilder(
      BuildContext context,
      ConfettiController _controllerCenter,
      String nickname,
      String gems,
      DiaryProcessingProvider diaryStateService) {
    Path drawStar(Size size) {
      double degToRad(double deg) => deg * (pi / 180.0);

      const numberOfPoints = 5;
      final halfWidth = size.width / 2;
      final externalRadius = halfWidth;
      final internalRadius = halfWidth / 2.5;
      final degreesPerStep = degToRad(360 / numberOfPoints);
      final halfDegreesPerStep = degreesPerStep / 2;
      final path = Path();
      final fullAngle = degToRad(360);
      path.moveTo(size.width, halfWidth);

      for (double step = 0; step < fullAngle; step += degreesPerStep) {
        path.lineTo(halfWidth + externalRadius * cos(step),
            halfWidth + externalRadius * sin(step));
        path.lineTo(halfWidth + internalRadius * cos(step + halfDegreesPerStep),
            halfWidth + internalRadius * sin(step + halfDegreesPerStep));
      }
      path.close();
      return path;
    }

    return Container(
      alignment: Alignment.topCenter,
      color: Colors.transparent,
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height - 20,
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                alignment: Alignment.center,
                child: ConfettiWidget(
                  confettiController: _controllerCenter,
                  blastDirectionality: BlastDirectionality
                      .explosive, // don't specify a direction, blast randomly
                  shouldLoop:
                      false, // start again as soon as the animation is finished
                  colors: [
                    Colors.green,
                    Colors.blue,
                    Colors.pink,
                    Colors.orange,
                    Colors.purple,
                    Theme.of(context).primaryColor,
                  ], // manually specify the colors to be used
                  createParticlePath: drawStar, // define a custom shape/path.
                ),
              ),
              Lottie.asset(
                'assets/lottie/success.json',
                width: 150,
                height: 150,
                repeat: false,
              ),
              Padding(
                padding: const EdgeInsets.only(top: 10.0, bottom: 4.0),
                child: Text(
                  FlutterI18n.translate(
                    context,
                    'diary.write.confetti.first',
                  ),
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                    decoration: TextDecoration.none,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 4.0, bottom: 10.0),
                child: Text(
                  FlutterI18n.translate(
                    context,
                    'diary.write.confetti.title',
                  ),
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                    decoration: TextDecoration.none,
                  ),
                ),
              ),
              if (showSubscription)
                Container(
                  alignment: Alignment.center,
                  padding: const EdgeInsets.all(10.0),
                  child: Text(
                    FlutterI18n.translate(
                      context,
                      'diary.write.confetti.description1',
                      translationParams: {
                        'name': nickname,
                        'currency_amount': gems,
                      },
                    ),
                    style: TextStyle(
                      fontSize: 17.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                      decoration: TextDecoration.none,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              Container(
                alignment: Alignment.center,
                padding: const EdgeInsets.all(10.0),
                child: Text(
                  FlutterI18n.translate(
                    context,
                    'diary.write.confetti.description2',
                  ),
                  style: TextStyle(
                    fontSize: 15.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                    decoration: TextDecoration.none,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 20.0),
                child: LDButton(
                  label: FlutterI18n.translate(context, 'button.label.confirm'),
                  onPressed: () {
                    diaryStateService.reset();
                    context.canPop()
                        ? context.pop()
                        : context.push(RoutePaths.home);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Container _noticeBoxBuilder(
    BuildContext context,
    DiaryListModelStruct diary,
    DiaryProcessingProvider diaryStateService,
  ) {
    return Container(
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(
        horizontal: 15.sp,
      ),
      height: isMobile(context) ? 130 : 200,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(
              alpha: 0.4,
            ),
            spreadRadius: 1,
            blurRadius: 1,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            flex: 1,
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: Lottie.asset(
                'assets/lottie/confetti.json',
                width: 100,
                height: 100,
              ),
            ),
          ),
          Flexible(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  FlutterI18n.translate(context, 'diary.write.success.title'),
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                    decoration: TextDecoration.none,
                  ),
                ),
                Text(
                  FlutterI18n.translate(context, 'diary.write.success.content'),
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: Colors.black,
                    decoration: TextDecoration.none,
                  ),
                ),
                SizedBox(
                  height: 1.sp,
                ),
                GestureDetector(
                  onTap: () {
                    print('diary id: ${diaryId}');
                    context.push(RoutePaths.diaryDetailPath(diaryId!));
                    diaryStateService.reset();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Text(
                          FlutterI18n.translate(
                              context, 'diary.write.success.button'),
                          style: TextStyle(
                            fontSize: 15.sp,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                            decoration: TextDecoration.none,
                          ),
                        ),
                        Icon(
                          Icons.arrow_forward_ios_rounded,
                          size: 14.sp,
                          color: Colors.black,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Flexible(
            flex: 1,
            child: IconButton(
              onPressed: () {
                diaryStateService.reset();
              },
              icon: Icon(
                Icons.close,
                size: 18.sp,
                color: LDColors.mainGrey,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
