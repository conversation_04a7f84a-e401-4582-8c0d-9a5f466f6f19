import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:just_the_tooltip/just_the_tooltip.dart';
import 'package:langda/presentation/pages/mobile/constants.dart';
import 'package:moon_design/moon_design.dart';

class LDField extends StatefulWidget {
  LDField({
    super.key,
    required this.title,
    required this.hintText,
    required this.fieldKey,
    required this.focusNode,
    required this.controller,
    this.tooltipText,
    this.capitalize = false,
  });

  final String title;
  final String hintText;
  final GlobalKey<FormState> fieldKey;
  final FocusNode focusNode;
  final TextEditingController controller;
  final String? tooltipText;
  bool capitalize;

  @override
  State<LDField> createState() => _LDFieldState();
}

class _LDFieldState extends State<LDField> {
  final JustTheController tooltipController = JustTheController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    tooltipController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(6.0),
          child: Row(
            children: [
              Text(
                widget.title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(
                width: 5,
              ),
              if (widget.tooltipText != null && widget.tooltipText!.isNotEmpty)
                GestureDetector(
                  onTap: () {
                    tooltipController.showTooltip();
                  },
                  onTapCancel: () {
                    tooltipController.hideTooltip();
                  },
                  child: JustTheTooltip(
                    controller: tooltipController,
                    child: Material(
                      color: LDColors.darkGrey,
                      shape: const CircleBorder(),
                      child: Icon(
                        Icons.info,
                        color: Colors.white,
                        size: 18,
                      ),
                    ),
                    content: Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Text(
                        widget.tooltipText ?? 'tooltip message',
                      ),
                    ),
                  ),
                )
            ],
          ),
        ),
        SizedBox(
          height: 5,
        ),
        Form(
          key: widget.fieldKey,
          child: MoonFormTextInput(
            autofocus: true,
            focusNode: widget.focusNode,
            controller: widget.controller,
            activeBorderColor: Theme.of(context).colorScheme.primary,
            hintText: widget.hintText,
            maxLines: 1,
            textCapitalization: widget.capitalize
                ? TextCapitalization.characters
                : TextCapitalization.none,
            onChanged: (value) {
              value = value.trim();
              widget.fieldKey.currentState?.validate();
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return FlutterI18n.translate(
                  context,
                  'registration.nickname.request',
                );
              } else if (value.length < 2) {
                return FlutterI18n.translate(
                  context,
                  'registration.nickname.minLength',
                );
              } else if (value.length > 20) {
                return FlutterI18n.translate(
                  context,
                  'registration.nickname.maxLength',
                );
              }
              return null;
            },
          ),
        ),
      ],
    );
  }
}
