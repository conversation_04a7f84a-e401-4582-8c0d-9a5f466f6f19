import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:langda/common/ld_dialog_component.dart';

import '../presentation/pages/component.dart';
import '../presentation/pages/mobile/constants.dart';

abstract class LDState<T extends StatefulWidget> extends State<T>
    with TickerProviderStateMixin {
  String pageName = '';

  PreferredSize appBarWrapper(String title, Widget? leading, Widget? trailing) {
    return PreferredSize(
      preferredSize: const Size.fromHeight(50),
      child: Container(
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: LDColors.subGrey,
              width: 1,
            ),
          ),
        ),
        child: AppBar(
          leading: leading ?? BackButton(),
          title: Text(title, style: appTitleStyle),
          centerTitle: true,
          actions: [trailing ?? Container()],
          backgroundColor: LDColors.subGrey.withAlpha(25),
          surfaceTintColor: LDColors.subGrey,
        ),
      ),
    );
  }

  final appTitleStyle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w700,
  );

  @override
  initState() {
    if (kDebugMode) debugPrint('init $pageName');
    super.initState();
  }

  showLDToaster(BuildContext context, String message, ToastType type) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            LDtoast(message, type, maxWidth: MediaQuery.of(context).size.width),
        backgroundColor: Colors.transparent,
        elevation: 0,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Future<void> showLDDialog(
    BuildContext context,
    String content, {
    required Function() onConfirmed,
    Function()? onCanceled,
  }) async {
    await showDialog(
      context: context,
      builder: (context) {
        return LDDialogComponent(
          content: content,
          onConfirm: onConfirmed,
          onCancel: onCanceled,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      bottom: false,
      child: Center(
        child: Stack(
          children: [
            Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              color: LDColors.mainWhiteGrey,
            ),
            buildContent(context),
          ],
        ),
      ),
    );
  }

  Widget buildContent(BuildContext context);
}
