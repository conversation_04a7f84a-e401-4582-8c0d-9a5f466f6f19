import 'package:flutter/material.dart';
import 'package:moon_design/moon_design.dart';

enum FormFieldXType {
  text,
  password,
  email,
  number,
}

class FormFieldX extends MoonFormTextInput {
  FormFieldX({
    super.key,
    required String fieldName,
    required FormFieldXType type,
    required TextEditingController controller,
    bool autofocus = false,
    FocusNode? focusNode,
    Color? activeBorderColor,
    double? borderRadius,
    String? hintText,
    IconData? leading,
    IconData? trailing,
    TextInputType keyboardType = TextInputType.text,
    TextInputAction textInputAction = TextInputAction.done,
    int minLength = 8,
    int maxLength = 20,
    MoonTextInputErrorBuilder? errorBuilder,
  }) : super(
          focusNode: focusNode,
          autofocus: autofocus,
          activeBorderColor: activeBorderColor,
          borderRadius: BorderRadius.circular(borderRadius ?? 8),
          controller: controller,
          hintText: hintText,
          textInputAction: textInputAction,
          keyboardType: keyboardType,
          leading: leading != null
              ? Icon(
                  leading,
                  size: 15,
                )
              : null,
          trailing: trailing != null
              ? Icon(
                  trailing,
                  size: 15,
                )
              : null,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter $fieldName';
            } else if (type == FormFieldXType.email) {
            } else if (type == FormFieldXType.password) {
              if (value.length < 8) {
                return 'Password must be at least ${minLength} characters';
              } else if (value.length > 20) {
                return 'Password must be less than ${maxLength} characters';
              }
            } else if (type == FormFieldXType.number) {
            } else {
              return null;
            }
            return null;
          },
          errorBuilder: errorBuilder,
        );
}
