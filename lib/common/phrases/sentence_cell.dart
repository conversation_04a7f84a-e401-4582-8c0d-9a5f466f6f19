import 'package:flutter/material.dart';

import '../../backend/model/diary_model.dart';
import '../../presentation/pages/mobile/constants.dart';

class SentenceCell extends StatelessWidget {
  final List<LdCharacter> sentences;
  final bool isCorrection;
  final Function(String id)? onTap;
  final List<String> selectedIds;

  SentenceCell({
    super.key,
    required this.sentences,
    this.onTap,
    required this.isCorrection,
    required this.selectedIds,
  });
  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 0,
      direction: Axis.horizontal,
      verticalDirection: VerticalDirection.down,
      alignment: WrapAlignment.start,
      children: [
        for (var e in sentences)
          Container(
            decoration: BoxDecoration(
              color: e.correctionId != null
                  ? selectedIds.contains(e.correctionId)
                      ? Theme.of(context).colorScheme.primary
                      : Colors.transparent
                  : Colors.transparent,
            ),
            child: oneTextCell(
              isCorrection,
              e.character,
              e.correctionId,
              onTap,
            ),
          ),
      ],
    );
  }

  StatelessWidget oneTextCell(
      bool isOriginal, String e, String? id, Function(String id)? onTap) {
    final isCorrectedWord = id != null;

    return isCorrectedWord
        ? GestureDetector(
            onTap: () {
              onTap!(id);
            },
            child: Text(
              e,
              style: TextStyle(
                fontSize: 16,
                color: isOriginal ? LDColors.mainBlue : LDColors.mainRed,
                fontWeight: FontWeight.w600,
              ),
            ),
          )
        : Text(
            e,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          );
  }
}
