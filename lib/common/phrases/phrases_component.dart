import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../api/models/explanation_language_preference.dart';
import '../../presentation/pages/mobile/home_page/card_page/component/correction_card.dart';
import '../../queries/card_query.dart';
import '../../queries/diary_query.dart';
import '../../queries/product_query.dart';
import 'sentence_cell.dart';

class PhrasesComponent extends StatefulWidget {
  final String entryId;
  final String originalVersionId;
  final String? correctedVersionId;
  final String originalContent;
  final String? correctedContent;
  final bool showCorrection;
  final ExplanationLanguagePreference explanationLanguagePreference;

  const PhrasesComponent({
    super.key,
    required this.entryId,
    required this.originalVersionId,
    this.correctedVersionId,
    required this.originalContent,
    this.correctedContent,
    required this.showCorrection,
    required this.explanationLanguagePreference,
  });

  @override
  State<PhrasesComponent> createState() => _PhrasesComponentState();
}

class _PhrasesComponentState extends State<PhrasesComponent> {
  Set<String> selectedIds = {};

  bool _hasSelectedFirstCorrection = false;

  void _wordTap(String id) {
    setState(() {
      if (selectedIds.contains(id)) {
        selectedIds.remove(id);
      } else {
        selectedIds.add(id);
      }
    });
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return QueryBuilder(
      query: fetchParagraphsQuery(
        widget.originalVersionId,
        widget.originalContent,
        true,
      ),
      builder: (context, state) {
        final originalPhrases = state.data;

        if (originalPhrases == null) {
          return Container();
        }

        return QueryBuilder(
          query: fetchParagraphsQuery(
            widget.correctedVersionId ?? '',
            widget.correctedContent ?? '',
            false,
          ),
          builder: (context, state) {
            final correctedPhrases = state.data;

            if (correctedPhrases != null && !_hasSelectedFirstCorrection) {
              try {
                for (var phraseGroup in correctedPhrases) {
                  if (phraseGroup.isNotEmpty) {
                    for (var phrase in phraseGroup) {
                      if (phrase.isNotEmpty) {
                        final firstOneId = phrase.first.correctionId;
                        if (firstOneId != null) {
                          if (!selectedIds.contains(firstOneId)) {
                            setState(() {
                              selectedIds.add(firstOneId);
                            });
                          }
                          _hasSelectedFirstCorrection = true;
                          break;
                        }
                      }
                    }
                    if (_hasSelectedFirstCorrection) break;
                  }
                }
              } catch (e) {
                print('Error selecting first correction: $e');
              }
            }

            return QueryBuilder(
              query: fetchDeepExplanationCostQuery(),
              builder: (context, costState) => Skeletonizer(
                enabled: state.status == QueryStatus.loading,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: Adaptive.sp(20),
                    ),
                    if (originalPhrases.isEmpty)
                      Container(
                        child: Text('${widget.originalContent}'),
                      ),
                    for (var i = 0; i < originalPhrases.length; i++)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Wrap(
                            children: [
                              SizedBox(
                                height: Adaptive.sp(10),
                              ),
                              if (originalPhrases[i].isNotEmpty)
                                for (var sentence in originalPhrases[i])
                                  SentenceCell(
                                    sentences: sentence,
                                    onTap: (id) => _wordTap(id),
                                    isCorrection: false,
                                    selectedIds: selectedIds.toList(),
                                  ),
                              if (correctedPhrases != null)
                                for (var sentence in correctedPhrases[i])
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      if (widget.showCorrection)
                                        SentenceCell(
                                          sentences: sentence,
                                          onTap: (id) => _wordTap(id),
                                          isCorrection: true,
                                          selectedIds: selectedIds.toList(),
                                        ),
                                      SizedBox(
                                        height: Adaptive.sp(5),
                                      ),
                                      for (String correctionId in Set.from(
                                          sentence
                                              .map((item) => item.correctionId)
                                              .where((e) => e != null)))
                                        QueryBuilder(
                                            query: fetchCardQuery(correctionId),
                                            builder: (context, state) {
                                              if (state.data == null ||
                                                  !selectedIds
                                                      .contains(correctionId)) {
                                                return Container();
                                              } else {
                                                // 첫번째 카드 true

                                                return Padding(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                    vertical: 10,
                                                  ),
                                                  child: CorrectionCard(
                                                    card: state.data!,
                                                    deepExplanationCost:
                                                        costState.data ?? 5,
                                                    explanationLanguagePreference:
                                                        widget
                                                            .explanationLanguagePreference,
                                                  ),
                                                );
                                              }
                                            }),
                                      SizedBox(
                                        height: Adaptive.sp(10),
                                      ),
                                    ],
                                  ),
                            ],
                          ),
                        ],
                      ),
                    SizedBox(
                      height: Adaptive.sp(5),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
