import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';

import '../presentation/pages/component.dart';
import '../presentation/pages/mobile/constants.dart';

class LDDialogComponent extends Dialog {
  LDDialogComponent({
    super.key,
    required this.content,
    required this.onConfirm,
    this.onCancel,
  });

  final String content;
  final Function onConfirm;
  final Function? onCancel;
  List<Widget> buildActions(BuildContext context) {
    return [
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          Flexible(
            child: LDButton(
              label: FlutterI18n.translate(context, 'button.label.confirm'),
              onPressed: () {
                Navigator.of(context).pop();
                onConfirm();
              },
            ),
          ),
          if (onCancel != null)
            const SizedBox(
              width: 10,
            ),
          if (onCancel != null)
            Flexible(
              child: LD<PERSON>utton(
                label: FlutterI18n.translate(context, 'button.label.cancel'),
                backgroundColor: LDColors.lightGrey,
                textColor: Theme.of(context).colorScheme.secondary,
                onPressed: () {
                  Navigator.of(context).pop();
                  onCancel!();
                },
              ),
            ),
        ],
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      contentPadding: EdgeInsets.fromLTRB(40, 35, 40, 0),
      actionsPadding: EdgeInsets.fromLTRB(40, 16, 40, 35),
      actionsAlignment: MainAxisAlignment.center,
      content: Text(
        content,
        textAlign: TextAlign.center,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black,
        ),
      ),
      actions: buildActions(context),
    );
  }
}
