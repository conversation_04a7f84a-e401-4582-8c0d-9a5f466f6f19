import 'package:flutter/material.dart';

import '../presentation/pages/mobile/constants.dart';

AppBar customAppBar(
    {required String title,
    double? preferredSize,
    Widget? leadingWidget,
    String? leadingLabel,
    Function()? leadingOnTap,
    Widget? trailing,
    String? trailingLabel,
    Widget? bottom}) {
  return AppBar(
    backgroundColor: LDColors.mainWhiteGrey,
    surfaceTintColor: LDColors.subGrey,
    bottom: PreferredSize(
      preferredSize: Size.fromHeight(preferredSize ?? -1),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          Container(
            padding: EdgeInsets.only(
              top: 5,
              bottom: 0,
            ),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: LDColors.subGrey,
                  width: 1,
                ),
              ),
            ),
            child: <PERSON><PERSON>(
              children: [
                Container(
                  alignment: Alignment.center,
                  padding: const EdgeInsets.only(
                    top: 10,
                  ),
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    if (leadingWidget != null)
                      GestureDetector(
                        onTap: leadingOnTap,
                        child: Semantics(
                          label: leadingLabel,
                          child: leadingWidget,
                        ),
                      ),
                    Spacer(),
                    if (trailing != null)
                      Semantics(
                        label: trailingLabel,
                        child: trailing,
                      ),
                  ],
                ),
              ],
            ),
          ),
          bottom ?? Container(),
        ],
      ),
    ),
  );
}
