// ignore_for_file: non_constant_identifier_names, camel_case_types, file_namesimport, file_names, unnecessary_null_comparison

// WARNING: This code is auto-generated by <PERSON>pad<PERSON>.
// WARNING: Modifications may be overwritten. Please make changes in the SupaDart configuration.

// SDK
import 'package:supabase_flutter/supabase_flutter.dart';

// No Intl package needed
// No Dart Convert needed
// Supadart Class
abstract class SupadartClass<T> {
  static Map<String, dynamic> insert(Map<String, dynamic> data) {
    throw UnimplementedError();
  }

  static Map<String, dynamic> update(Map<String, dynamic> data) {
    throw UnimplementedError();
  }

  factory SupadartClass.fromJson(Map<String, dynamic> json) {
    throw UnimplementedError();
  }

  static converter(List<Map<String, dynamic>> data) {
    throw UnimplementedError();
  }

  static converterSingle(Map<String, dynamic> data) {
    throw UnimplementedError();
  }
}

// Supabase Client Extension
extension SupadartClient on SupabaseClient {
  SupabaseQueryBuilder get currency_transactions =>
      from('currency_transactions');
  SupabaseQueryBuilder get referrals => from('referrals');
  SupabaseQueryBuilder get corrections_correction_categories =>
      from('corrections_correction_categories');
  SupabaseQueryBuilder get corrections => from('corrections');
  SupabaseQueryBuilder get diary_versions => from('diary_versions');
  SupabaseQueryBuilder get diary_entries => from('diary_entries');
  SupabaseQueryBuilder get diary_version_chunks => from('diary_version_chunks');
  SupabaseQueryBuilder get subject_today => from('subject_today');
  SupabaseQueryBuilder get profiles => from('profiles');
  SupabaseQueryBuilder get kysely_migration => from('kysely_migration');
  SupabaseQueryBuilder get generation_tasks => from('generation_tasks');
  SupabaseQueryBuilder get revenuecat_processed_events =>
      from('revenuecat_processed_events');
  SupabaseQueryBuilder get phrases => from('phrases');
  SupabaseQueryBuilder get kysely_migration_lock =>
      from('kysely_migration_lock');
  SupabaseQueryBuilder get correction_categories =>
      from('correction_categories');
  SupabaseQueryBuilder get user_questions => from('user_questions');
  SupabaseQueryBuilder get feature_usage_transactions =>
      from('feature_usage_transactions');
  SupabaseQueryBuilder get notifications => from('notifications');
}

// Supabase Storage Client Extension
extension SupadartStorageClient on SupabaseStorageClient {}

// Enums
enum DIARY_VERSION_TYPE { original, corrected }

enum LEARNING_MOTIVATION {
  social,
  study_abroad,
  emigration,
  career,
  fun,
  other
}

enum CURRENCY_TRANSACTION_TYPE {
  streak_milestone,
  achievement,
  trial_bonus,
  periodic_reward,
  feature_usage
}

enum GENERATION_TASK_STATUS { a }

enum EXPLANATION_LANGUAGE_PREFERENCE { native_language, diary_language }

// Utils
class CurrencyTransactions implements SupadartClass<CurrencyTransactions> {
  final String id;
  final String userId;
  final int amount;
  final CURRENCY_TRANSACTION_TYPE transactionType;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;

  const CurrencyTransactions({
    required this.id,
    required this.userId,
    required this.amount,
    required this.transactionType,
    this.metadata,
    required this.createdAt,
  });

  static String get table_name => 'currency_transactions';
  static String get c_id => 'id';
  static String get c_userId => 'user_id';
  static String get c_amount => 'amount';
  static String get c_transactionType => 'transaction_type';
  static String get c_metadata => 'metadata';
  static String get c_createdAt => 'created_at';

  static List<CurrencyTransactions> converter(List<Map<String, dynamic>> data) {
    return data.map(CurrencyTransactions.fromJson).toList();
  }

  static CurrencyTransactions converterSingle(Map<String, dynamic> data) {
    return CurrencyTransactions.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? id,
    String? userId,
    int? amount,
    CURRENCY_TRANSACTION_TYPE? transactionType,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
  }) {
    return {
      if (id != null) 'id': id,
      if (userId != null) 'user_id': userId,
      if (amount != null) 'amount': amount,
      if (transactionType != null)
        'transaction_type': transactionType.toString().split('.').last,
      if (metadata != null) 'metadata': metadata,
      if (createdAt != null) 'created_at': createdAt.toUtc().toIso8601String(),
    };
  }

  static Map<String, dynamic> insert({
    String? id,
    required String userId,
    required int amount,
    required CURRENCY_TRANSACTION_TYPE transactionType,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
  }) {
    return _generateMap(
      id: id,
      userId: userId,
      amount: amount,
      transactionType: transactionType,
      metadata: metadata,
      createdAt: createdAt,
    );
  }

  static Map<String, dynamic> update({
    String? id,
    String? userId,
    int? amount,
    CURRENCY_TRANSACTION_TYPE? transactionType,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
  }) {
    return _generateMap(
      id: id,
      userId: userId,
      amount: amount,
      transactionType: transactionType,
      metadata: metadata,
      createdAt: createdAt,
    );
  }

  factory CurrencyTransactions.fromJson(Map<String, dynamic> jsonn) {
    return CurrencyTransactions(
      id: jsonn['id'] != null ? jsonn['id'].toString() : '',
      userId: jsonn['user_id'] != null ? jsonn['user_id'].toString() : '',
      amount:
          jsonn['amount'] != null ? int.parse(jsonn['amount'].toString()) : 0,
      transactionType: jsonn['transaction_type'] != null
          ? CURRENCY_TRANSACTION_TYPE.values
              .byName(jsonn['transaction_type'].toString())
          : CURRENCY_TRANSACTION_TYPE.values.first,
      metadata: jsonn['metadata'] != null
          ? jsonn['metadata'] as Map<String, dynamic>
          : <String, dynamic>{},
      createdAt: jsonn['created_at'] != null
          ? DateTime.parse(jsonn['created_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      id: id,
      userId: userId,
      amount: amount,
      transactionType: transactionType,
      metadata: metadata,
      createdAt: createdAt,
    );
  }
}

class Referrals implements SupadartClass<Referrals> {
  final String id;
  final String referrerId;
  final String referredId;
  final DateTime createdAt;

  const Referrals({
    required this.id,
    required this.referrerId,
    required this.referredId,
    required this.createdAt,
  });

  static String get table_name => 'referrals';
  static String get c_id => 'id';
  static String get c_referrerId => 'referrer_id';
  static String get c_referredId => 'referred_id';
  static String get c_createdAt => 'created_at';

  static List<Referrals> converter(List<Map<String, dynamic>> data) {
    return data.map(Referrals.fromJson).toList();
  }

  static Referrals converterSingle(Map<String, dynamic> data) {
    return Referrals.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? id,
    String? referrerId,
    String? referredId,
    DateTime? createdAt,
  }) {
    return {
      if (id != null) 'id': id,
      if (referrerId != null) 'referrer_id': referrerId,
      if (referredId != null) 'referred_id': referredId,
      if (createdAt != null) 'created_at': createdAt.toUtc().toIso8601String(),
    };
  }

  static Map<String, dynamic> insert({
    String? id,
    required String referrerId,
    required String referredId,
    DateTime? createdAt,
  }) {
    return _generateMap(
      id: id,
      referrerId: referrerId,
      referredId: referredId,
      createdAt: createdAt,
    );
  }

  static Map<String, dynamic> update({
    String? id,
    String? referrerId,
    String? referredId,
    DateTime? createdAt,
  }) {
    return _generateMap(
      id: id,
      referrerId: referrerId,
      referredId: referredId,
      createdAt: createdAt,
    );
  }

  factory Referrals.fromJson(Map<String, dynamic> jsonn) {
    return Referrals(
      id: jsonn['id'] != null ? jsonn['id'].toString() : '',
      referrerId:
          jsonn['referrer_id'] != null ? jsonn['referrer_id'].toString() : '',
      referredId:
          jsonn['referred_id'] != null ? jsonn['referred_id'].toString() : '',
      createdAt: jsonn['created_at'] != null
          ? DateTime.parse(jsonn['created_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      id: id,
      referrerId: referrerId,
      referredId: referredId,
      createdAt: createdAt,
    );
  }
}

class CorrectionsCorrectionCategories
    implements SupadartClass<CorrectionsCorrectionCategories> {
  final String correctionId;
  final String categoryId;
  final DateTime createdAt;

  const CorrectionsCorrectionCategories({
    required this.correctionId,
    required this.categoryId,
    required this.createdAt,
  });

  static String get table_name => 'corrections_correction_categories';
  static String get c_correctionId => 'correction_id';
  static String get c_categoryId => 'category_id';
  static String get c_createdAt => 'created_at';

  static List<CorrectionsCorrectionCategories> converter(
      List<Map<String, dynamic>> data) {
    return data.map(CorrectionsCorrectionCategories.fromJson).toList();
  }

  static CorrectionsCorrectionCategories converterSingle(
      Map<String, dynamic> data) {
    return CorrectionsCorrectionCategories.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? correctionId,
    String? categoryId,
    DateTime? createdAt,
  }) {
    return {
      if (correctionId != null) 'correction_id': correctionId,
      if (categoryId != null) 'category_id': categoryId,
      if (createdAt != null) 'created_at': createdAt.toUtc().toIso8601String(),
    };
  }

  static Map<String, dynamic> insert({
    String? correctionId,
    String? categoryId,
    DateTime? createdAt,
  }) {
    return _generateMap(
      correctionId: correctionId,
      categoryId: categoryId,
      createdAt: createdAt,
    );
  }

  static Map<String, dynamic> update({
    String? correctionId,
    String? categoryId,
    DateTime? createdAt,
  }) {
    return _generateMap(
      correctionId: correctionId,
      categoryId: categoryId,
      createdAt: createdAt,
    );
  }

  factory CorrectionsCorrectionCategories.fromJson(Map<String, dynamic> jsonn) {
    return CorrectionsCorrectionCategories(
      correctionId: jsonn['correction_id'] != null
          ? jsonn['correction_id'].toString()
          : '',
      categoryId:
          jsonn['category_id'] != null ? jsonn['category_id'].toString() : '',
      createdAt: jsonn['created_at'] != null
          ? DateTime.parse(jsonn['created_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      correctionId: correctionId,
      categoryId: categoryId,
      createdAt: createdAt,
    );
  }
}

class Corrections implements SupadartClass<Corrections> {
  final String id;
  final String? originalPhraseId;
  final String? correctedPhraseId;
  final List<int>? correctionTags;
  final String explanationUserLanguage;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String explanationDiaryLanguage;
  final bool isSaved;
  final String userId;
  final int likes;
  final String? deepExplanationDiaryLanguage;
  final String? deepExplanationUserLanguage;

  const Corrections({
    required this.id,
    this.originalPhraseId,
    this.correctedPhraseId,
    this.correctionTags,
    required this.explanationUserLanguage,
    required this.createdAt,
    required this.updatedAt,
    required this.explanationDiaryLanguage,
    required this.isSaved,
    required this.userId,
    required this.likes,
    this.deepExplanationDiaryLanguage,
    this.deepExplanationUserLanguage,
  });

  static String get table_name => 'corrections';
  static String get c_id => 'id';
  static String get c_originalPhraseId => 'original_phrase_id';
  static String get c_correctedPhraseId => 'corrected_phrase_id';
  static String get c_correctionTags => 'correction_tags';
  static String get c_explanationUserLanguage => 'explanation_user_language';
  static String get c_createdAt => 'created_at';
  static String get c_updatedAt => 'updated_at';
  static String get c_explanationDiaryLanguage => 'explanation_diary_language';
  static String get c_isSaved => 'is_saved';
  static String get c_userId => 'user_id';
  static String get c_likes => 'likes';
  static String get c_deepExplanationDiaryLanguage =>
      'deep_explanation_diary_language';
  static String get c_deepExplanationUserLanguage =>
      'deep_explanation_user_language';

  static List<Corrections> converter(List<Map<String, dynamic>> data) {
    return data.map(Corrections.fromJson).toList();
  }

  static Corrections converterSingle(Map<String, dynamic> data) {
    return Corrections.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? id,
    String? originalPhraseId,
    String? correctedPhraseId,
    List<int>? correctionTags,
    String? explanationUserLanguage,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? explanationDiaryLanguage,
    bool? isSaved,
    String? userId,
    int? likes,
    String? deepExplanationDiaryLanguage,
    String? deepExplanationUserLanguage,
  }) {
    return {
      if (id != null) 'id': id,
      if (originalPhraseId != null) 'original_phrase_id': originalPhraseId,
      if (correctedPhraseId != null) 'corrected_phrase_id': correctedPhraseId,
      if (correctionTags != null) 'correction_tags': correctionTags,
      if (explanationUserLanguage != null)
        'explanation_user_language': explanationUserLanguage,
      if (createdAt != null) 'created_at': createdAt.toUtc().toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt.toUtc().toIso8601String(),
      if (explanationDiaryLanguage != null)
        'explanation_diary_language': explanationDiaryLanguage,
      if (isSaved != null) 'is_saved': isSaved,
      if (userId != null) 'user_id': userId,
      if (likes != null) 'likes': likes,
      if (deepExplanationDiaryLanguage != null)
        'deep_explanation_diary_language': deepExplanationDiaryLanguage,
      if (deepExplanationUserLanguage != null)
        'deep_explanation_user_language': deepExplanationUserLanguage,
    };
  }

  static Map<String, dynamic> insert({
    String? id,
    String? originalPhraseId,
    String? correctedPhraseId,
    List<int>? correctionTags,
    required String explanationUserLanguage,
    DateTime? createdAt,
    DateTime? updatedAt,
    required String explanationDiaryLanguage,
    bool? isSaved,
    required String userId,
    int? likes,
    String? deepExplanationDiaryLanguage,
    String? deepExplanationUserLanguage,
  }) {
    return _generateMap(
      id: id,
      originalPhraseId: originalPhraseId,
      correctedPhraseId: correctedPhraseId,
      correctionTags: correctionTags,
      explanationUserLanguage: explanationUserLanguage,
      createdAt: createdAt,
      updatedAt: updatedAt,
      explanationDiaryLanguage: explanationDiaryLanguage,
      isSaved: isSaved,
      userId: userId,
      likes: likes,
      deepExplanationDiaryLanguage: deepExplanationDiaryLanguage,
      deepExplanationUserLanguage: deepExplanationUserLanguage,
    );
  }

  static Map<String, dynamic> update({
    String? id,
    String? originalPhraseId,
    String? correctedPhraseId,
    List<int>? correctionTags,
    String? explanationUserLanguage,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? explanationDiaryLanguage,
    bool? isSaved,
    String? userId,
    int? likes,
    String? deepExplanationDiaryLanguage,
    String? deepExplanationUserLanguage,
  }) {
    return _generateMap(
      id: id,
      originalPhraseId: originalPhraseId,
      correctedPhraseId: correctedPhraseId,
      correctionTags: correctionTags,
      explanationUserLanguage: explanationUserLanguage,
      createdAt: createdAt,
      updatedAt: updatedAt,
      explanationDiaryLanguage: explanationDiaryLanguage,
      isSaved: isSaved,
      userId: userId,
      likes: likes,
      deepExplanationDiaryLanguage: deepExplanationDiaryLanguage,
      deepExplanationUserLanguage: deepExplanationUserLanguage,
    );
  }

  factory Corrections.fromJson(Map<String, dynamic> jsonn) {
    return Corrections(
      id: jsonn['id'] != null ? jsonn['id'].toString() : '',
      originalPhraseId: jsonn['original_phrase_id'] != null
          ? jsonn['original_phrase_id'].toString()
          : '',
      correctedPhraseId: jsonn['corrected_phrase_id'] != null
          ? jsonn['corrected_phrase_id'].toString()
          : '',
      correctionTags: jsonn['correction_tags'] != null
          ? (jsonn['correction_tags'] as List<dynamic>)
              .map((v) => int.parse(v.toString()))
              .toList()
          : <int>[],
      explanationUserLanguage: jsonn['explanation_user_language'] != null
          ? jsonn['explanation_user_language'].toString()
          : '',
      createdAt: jsonn['created_at'] != null
          ? DateTime.parse(jsonn['created_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
      updatedAt: jsonn['updated_at'] != null
          ? DateTime.parse(jsonn['updated_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
      explanationDiaryLanguage: jsonn['explanation_diary_language'] != null
          ? jsonn['explanation_diary_language'].toString()
          : '',
      isSaved: jsonn['is_saved'] != null ? jsonn['is_saved'] as bool : false,
      userId: jsonn['user_id'] != null ? jsonn['user_id'].toString() : '',
      likes: jsonn['likes'] != null ? int.parse(jsonn['likes'].toString()) : 0,
      deepExplanationDiaryLanguage:
          jsonn['deep_explanation_diary_language'] != null
              ? jsonn['deep_explanation_diary_language'].toString()
              : '',
      deepExplanationUserLanguage:
          jsonn['deep_explanation_user_language'] != null
              ? jsonn['deep_explanation_user_language'].toString()
              : '',
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      id: id,
      originalPhraseId: originalPhraseId,
      correctedPhraseId: correctedPhraseId,
      correctionTags: correctionTags,
      explanationUserLanguage: explanationUserLanguage,
      createdAt: createdAt,
      updatedAt: updatedAt,
      explanationDiaryLanguage: explanationDiaryLanguage,
      isSaved: isSaved,
      userId: userId,
      likes: likes,
      deepExplanationDiaryLanguage: deepExplanationDiaryLanguage,
      deepExplanationUserLanguage: deepExplanationUserLanguage,
    );
  }
}

class DiaryVersions implements SupadartClass<DiaryVersions> {
  final String id;
  final String diaryEntryId;
  final DIARY_VERSION_TYPE versionType;
  final String content;
  final DateTime createdAt;
  final DateTime updatedAt;

  const DiaryVersions({
    required this.id,
    required this.diaryEntryId,
    required this.versionType,
    required this.content,
    required this.createdAt,
    required this.updatedAt,
  });

  static String get table_name => 'diary_versions';
  static String get c_id => 'id';
  static String get c_diaryEntryId => 'diary_entry_id';
  static String get c_versionType => 'version_type';
  static String get c_content => 'content';
  static String get c_createdAt => 'created_at';
  static String get c_updatedAt => 'updated_at';

  static List<DiaryVersions> converter(List<Map<String, dynamic>> data) {
    return data.map(DiaryVersions.fromJson).toList();
  }

  static DiaryVersions converterSingle(Map<String, dynamic> data) {
    return DiaryVersions.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? id,
    String? diaryEntryId,
    DIARY_VERSION_TYPE? versionType,
    String? content,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return {
      if (id != null) 'id': id,
      if (diaryEntryId != null) 'diary_entry_id': diaryEntryId,
      if (versionType != null)
        'version_type': versionType.toString().split('.').last,
      if (content != null) 'content': content,
      if (createdAt != null) 'created_at': createdAt.toUtc().toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt.toUtc().toIso8601String(),
    };
  }

  static Map<String, dynamic> insert({
    String? id,
    required String diaryEntryId,
    required DIARY_VERSION_TYPE versionType,
    required String content,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return _generateMap(
      id: id,
      diaryEntryId: diaryEntryId,
      versionType: versionType,
      content: content,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  static Map<String, dynamic> update({
    String? id,
    String? diaryEntryId,
    DIARY_VERSION_TYPE? versionType,
    String? content,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return _generateMap(
      id: id,
      diaryEntryId: diaryEntryId,
      versionType: versionType,
      content: content,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  factory DiaryVersions.fromJson(Map<String, dynamic> jsonn) {
    return DiaryVersions(
      id: jsonn['id'] != null ? jsonn['id'].toString() : '',
      diaryEntryId: jsonn['diary_entry_id'] != null
          ? jsonn['diary_entry_id'].toString()
          : '',
      versionType: jsonn['version_type'] != null
          ? DIARY_VERSION_TYPE.values.byName(jsonn['version_type'].toString())
          : DIARY_VERSION_TYPE.values.first,
      content: jsonn['content'] != null ? jsonn['content'].toString() : '',
      createdAt: jsonn['created_at'] != null
          ? DateTime.parse(jsonn['created_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
      updatedAt: jsonn['updated_at'] != null
          ? DateTime.parse(jsonn['updated_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      id: id,
      diaryEntryId: diaryEntryId,
      versionType: versionType,
      content: content,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}

class DiaryEntries implements SupadartClass<DiaryEntries> {
  final String id;
  final String userId;
  final String weather;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime entryDate;
  final String? subjectId;

  const DiaryEntries({
    required this.id,
    required this.userId,
    required this.weather,
    required this.createdAt,
    required this.updatedAt,
    required this.entryDate,
    this.subjectId,
  });

  static String get table_name => 'diary_entries';
  static String get c_id => 'id';
  static String get c_userId => 'user_id';
  static String get c_weather => 'weather';
  static String get c_createdAt => 'created_at';
  static String get c_updatedAt => 'updated_at';
  static String get c_entryDate => 'entry_date';
  static String get c_subjectId => 'subject_id';

  static List<DiaryEntries> converter(List<Map<String, dynamic>> data) {
    return data.map(DiaryEntries.fromJson).toList();
  }

  static DiaryEntries converterSingle(Map<String, dynamic> data) {
    return DiaryEntries.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? id,
    String? userId,
    String? weather,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? entryDate,
    String? subjectId,
  }) {
    return {
      if (id != null) 'id': id,
      if (userId != null) 'user_id': userId,
      if (weather != null) 'weather': weather,
      if (createdAt != null) 'created_at': createdAt.toUtc().toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt.toUtc().toIso8601String(),
      if (entryDate != null) 'entry_date': entryDate.toIso8601String(),
      if (subjectId != null) 'subject_id': subjectId,
    };
  }

  static Map<String, dynamic> insert({
    String? id,
    required String userId,
    String? weather,
    DateTime? createdAt,
    DateTime? updatedAt,
    required DateTime entryDate,
    String? subjectId,
  }) {
    return _generateMap(
      id: id,
      userId: userId,
      weather: weather,
      createdAt: createdAt,
      updatedAt: updatedAt,
      entryDate: entryDate,
      subjectId: subjectId,
    );
  }

  static Map<String, dynamic> update({
    String? id,
    String? userId,
    String? weather,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? entryDate,
    String? subjectId,
  }) {
    return _generateMap(
      id: id,
      userId: userId,
      weather: weather,
      createdAt: createdAt,
      updatedAt: updatedAt,
      entryDate: entryDate,
      subjectId: subjectId,
    );
  }

  factory DiaryEntries.fromJson(Map<String, dynamic> jsonn) {
    return DiaryEntries(
      id: jsonn['id'] != null ? jsonn['id'].toString() : '',
      userId: jsonn['user_id'] != null ? jsonn['user_id'].toString() : '',
      weather: jsonn['weather'] != null ? jsonn['weather'].toString() : '',
      createdAt: jsonn['created_at'] != null
          ? DateTime.parse(jsonn['created_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
      updatedAt: jsonn['updated_at'] != null
          ? DateTime.parse(jsonn['updated_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
      entryDate: jsonn['entry_date'] != null
          ? DateTime.parse(jsonn['entry_date'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
      subjectId:
          jsonn['subject_id'] != null ? jsonn['subject_id'].toString() : '',
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      id: id,
      userId: userId,
      weather: weather,
      createdAt: createdAt,
      updatedAt: updatedAt,
      entryDate: entryDate,
      subjectId: subjectId,
    );
  }
}

class DiaryVersionChunks implements SupadartClass<DiaryVersionChunks> {
  final String id;
  final DateTime createdAt;
  final String diaryVersionId;
  final BigInt? sequenceNumber;
  final BigInt? startIndex;
  final BigInt? endIndex;
  final List<String>? sentences;

  const DiaryVersionChunks({
    required this.id,
    required this.createdAt,
    required this.diaryVersionId,
    this.sequenceNumber,
    this.startIndex,
    this.endIndex,
    this.sentences,
  });

  static String get table_name => 'diary_version_chunks';
  static String get c_id => 'id';
  static String get c_createdAt => 'created_at';
  static String get c_diaryVersionId => 'diary_version_id';
  static String get c_sequenceNumber => 'sequence_number';
  static String get c_startIndex => 'start_index';
  static String get c_endIndex => 'end_index';
  static String get c_sentences => 'sentences';

  static List<DiaryVersionChunks> converter(List<Map<String, dynamic>> data) {
    return data.map(DiaryVersionChunks.fromJson).toList();
  }

  static DiaryVersionChunks converterSingle(Map<String, dynamic> data) {
    return DiaryVersionChunks.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? id,
    DateTime? createdAt,
    String? diaryVersionId,
    BigInt? sequenceNumber,
    BigInt? startIndex,
    BigInt? endIndex,
    List<String>? sentences,
  }) {
    return {
      if (id != null) 'id': id,
      if (createdAt != null) 'created_at': createdAt.toUtc().toIso8601String(),
      if (diaryVersionId != null) 'diary_version_id': diaryVersionId,
      if (sequenceNumber != null) 'sequence_number': sequenceNumber.toString(),
      if (startIndex != null) 'start_index': startIndex.toString(),
      if (endIndex != null) 'end_index': endIndex.toString(),
      if (sentences != null) 'sentences': sentences.map((e) => e).toList(),
    };
  }

  static Map<String, dynamic> insert({
    String? id,
    DateTime? createdAt,
    required String diaryVersionId,
    BigInt? sequenceNumber,
    BigInt? startIndex,
    BigInt? endIndex,
    List<String>? sentences,
  }) {
    return _generateMap(
      id: id,
      createdAt: createdAt,
      diaryVersionId: diaryVersionId,
      sequenceNumber: sequenceNumber,
      startIndex: startIndex,
      endIndex: endIndex,
      sentences: sentences,
    );
  }

  static Map<String, dynamic> update({
    String? id,
    DateTime? createdAt,
    String? diaryVersionId,
    BigInt? sequenceNumber,
    BigInt? startIndex,
    BigInt? endIndex,
    List<String>? sentences,
  }) {
    return _generateMap(
      id: id,
      createdAt: createdAt,
      diaryVersionId: diaryVersionId,
      sequenceNumber: sequenceNumber,
      startIndex: startIndex,
      endIndex: endIndex,
      sentences: sentences,
    );
  }

  factory DiaryVersionChunks.fromJson(Map<String, dynamic> jsonn) {
    return DiaryVersionChunks(
      id: jsonn['id'] != null ? jsonn['id'].toString() : '',
      createdAt: jsonn['created_at'] != null
          ? DateTime.parse(jsonn['created_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
      diaryVersionId: jsonn['diary_version_id'] != null
          ? jsonn['diary_version_id'].toString()
          : '',
      sequenceNumber: jsonn['sequence_number'] != null
          ? BigInt.parse(jsonn['sequence_number'].toString())
          : BigInt.from(0),
      startIndex: jsonn['start_index'] != null
          ? BigInt.parse(jsonn['start_index'].toString())
          : BigInt.from(0),
      endIndex: jsonn['end_index'] != null
          ? BigInt.parse(jsonn['end_index'].toString())
          : BigInt.from(0),
      sentences: jsonn['sentences'] != null
          ? (jsonn['sentences'] as List<dynamic>)
              .map((v) => v.toString())
              .toList()
          : <String>[],
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      id: id,
      createdAt: createdAt,
      diaryVersionId: diaryVersionId,
      sequenceNumber: sequenceNumber,
      startIndex: startIndex,
      endIndex: endIndex,
      sentences: sentences,
    );
  }
}

class SubjectToday implements SupadartClass<SubjectToday> {
  final String id;
  final DateTime date;
  final String emoji;
  final String subject;

  const SubjectToday({
    required this.id,
    required this.date,
    required this.emoji,
    required this.subject,
  });

  static String get table_name => 'subject_today';
  static String get c_id => 'id';
  static String get c_date => 'date';
  static String get c_emoji => 'emoji';
  static String get c_subject => 'subject';

  static List<SubjectToday> converter(List<Map<String, dynamic>> data) {
    return data.map(SubjectToday.fromJson).toList();
  }

  static SubjectToday converterSingle(Map<String, dynamic> data) {
    return SubjectToday.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? id,
    DateTime? date,
    String? emoji,
    String? subject,
  }) {
    return {
      if (id != null) 'id': id,
      if (date != null) 'date': date.toIso8601String(),
      if (emoji != null) 'emoji': emoji,
      if (subject != null) 'subject': subject,
    };
  }

  static Map<String, dynamic> insert({
    String? id,
    required DateTime date,
    required String emoji,
    required String subject,
  }) {
    return _generateMap(
      id: id,
      date: date,
      emoji: emoji,
      subject: subject,
    );
  }

  static Map<String, dynamic> update({
    String? id,
    DateTime? date,
    String? emoji,
    String? subject,
  }) {
    return _generateMap(
      id: id,
      date: date,
      emoji: emoji,
      subject: subject,
    );
  }

  factory SubjectToday.fromJson(Map<String, dynamic> jsonn) {
    return SubjectToday(
      id: jsonn['id'] != null ? jsonn['id'].toString() : '',
      date: jsonn['date'] != null
          ? DateTime.parse(jsonn['date'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
      emoji: jsonn['emoji'] != null ? jsonn['emoji'].toString() : '',
      subject: jsonn['subject'] != null ? jsonn['subject'].toString() : '',
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      id: id,
      date: date,
      emoji: emoji,
      subject: subject,
    );
  }
}

class Profiles implements SupadartClass<Profiles> {
  final String userId;
  final DateTime createdAt;
  final String nickname;
  final DateTime updatedAt;
  final DateTime? trialEndsAt;
  final String? fcmToken;
  final int currencyBalance;
  final LEARNING_MOTIVATION learningMotivation;
  final String? referralCode;
  final int currentStreak;
  final String? nativeLanguage;
  final EXPLANATION_LANGUAGE_PREFERENCE explanationLanguage;

  const Profiles({
    required this.userId,
    required this.createdAt,
    required this.nickname,
    required this.updatedAt,
    this.trialEndsAt,
    this.fcmToken,
    required this.currencyBalance,
    required this.learningMotivation,
    this.referralCode,
    required this.currentStreak,
    this.nativeLanguage,
    required this.explanationLanguage,
  });

  static String get table_name => 'profiles';
  static String get c_userId => 'user_id';
  static String get c_createdAt => 'created_at';
  static String get c_nickname => 'nickname';
  static String get c_updatedAt => 'updated_at';
  static String get c_trialEndsAt => 'trial_ends_at';
  static String get c_fcmToken => 'fcm_token';
  static String get c_currencyBalance => 'currency_balance';
  static String get c_learningMotivation => 'learning_motivation';
  static String get c_referralCode => 'referral_code';
  static String get c_currentStreak => 'current_streak';
  static String get c_nativeLanguage => 'native_language';
  static String get c_explanationLanguage => 'explanation_language';

  static List<Profiles> converter(List<Map<String, dynamic>> data) {
    return data.map(Profiles.fromJson).toList();
  }

  static Profiles converterSingle(Map<String, dynamic> data) {
    return Profiles.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? userId,
    DateTime? createdAt,
    String? nickname,
    DateTime? updatedAt,
    DateTime? trialEndsAt,
    String? fcmToken,
    int? currencyBalance,
    LEARNING_MOTIVATION? learningMotivation,
    String? referralCode,
    int? currentStreak,
    String? nativeLanguage,
    EXPLANATION_LANGUAGE_PREFERENCE? explanationLanguage,
  }) {
    return {
      if (userId != null) 'user_id': userId,
      if (createdAt != null) 'created_at': createdAt.toUtc().toIso8601String(),
      if (nickname != null) 'nickname': nickname,
      if (updatedAt != null) 'updated_at': updatedAt.toUtc().toIso8601String(),
      if (trialEndsAt != null) 'trial_ends_at': trialEndsAt.toIso8601String(),
      if (fcmToken != null) 'fcm_token': fcmToken,
      if (currencyBalance != null) 'currency_balance': currencyBalance,
      if (learningMotivation != null)
        'learning_motivation': learningMotivation.toString().split('.').last,
      if (referralCode != null) 'referral_code': referralCode,
      if (currentStreak != null) 'current_streak': currentStreak,
      if (nativeLanguage != null) 'native_language': nativeLanguage,
      if (explanationLanguage != null)
        'explanation_language': explanationLanguage.toString().split('.').last,
    };
  }

  static Map<String, dynamic> insert({
    String? userId,
    DateTime? createdAt,
    required String nickname,
    DateTime? updatedAt,
    DateTime? trialEndsAt,
    String? fcmToken,
    int? currencyBalance,
    required LEARNING_MOTIVATION learningMotivation,
    String? referralCode,
    int? currentStreak,
    String? nativeLanguage,
    EXPLANATION_LANGUAGE_PREFERENCE? explanationLanguage,
  }) {
    return _generateMap(
      userId: userId,
      createdAt: createdAt,
      nickname: nickname,
      updatedAt: updatedAt,
      trialEndsAt: trialEndsAt,
      fcmToken: fcmToken,
      currencyBalance: currencyBalance,
      learningMotivation: learningMotivation,
      referralCode: referralCode,
      currentStreak: currentStreak,
      nativeLanguage: nativeLanguage,
      explanationLanguage: explanationLanguage,
    );
  }

  static Map<String, dynamic> update({
    String? userId,
    DateTime? createdAt,
    String? nickname,
    DateTime? updatedAt,
    DateTime? trialEndsAt,
    String? fcmToken,
    int? currencyBalance,
    LEARNING_MOTIVATION? learningMotivation,
    String? referralCode,
    int? currentStreak,
    String? nativeLanguage,
    EXPLANATION_LANGUAGE_PREFERENCE? explanationLanguage,
  }) {
    return _generateMap(
      userId: userId,
      createdAt: createdAt,
      nickname: nickname,
      updatedAt: updatedAt,
      trialEndsAt: trialEndsAt,
      fcmToken: fcmToken,
      currencyBalance: currencyBalance,
      learningMotivation: learningMotivation,
      referralCode: referralCode,
      currentStreak: currentStreak,
      nativeLanguage: nativeLanguage,
      explanationLanguage: explanationLanguage,
    );
  }

  factory Profiles.fromJson(Map<String, dynamic> jsonn) {
    return Profiles(
      userId: jsonn['user_id'] != null ? jsonn['user_id'].toString() : '',
      createdAt: jsonn['created_at'] != null
          ? DateTime.parse(jsonn['created_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
      nickname: jsonn['nickname'] != null ? jsonn['nickname'].toString() : '',
      updatedAt: jsonn['updated_at'] != null
          ? DateTime.parse(jsonn['updated_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
      trialEndsAt: jsonn['trial_ends_at'] != null
          ? DateTime.parse(jsonn['trial_ends_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
      fcmToken: jsonn['fcm_token'] != null ? jsonn['fcm_token'].toString() : '',
      currencyBalance: jsonn['currency_balance'] != null
          ? int.parse(jsonn['currency_balance'].toString())
          : 0,
      learningMotivation: jsonn['learning_motivation'] != null
          ? LEARNING_MOTIVATION.values
              .byName(jsonn['learning_motivation'].toString())
          : LEARNING_MOTIVATION.values.first,
      referralCode: jsonn['referral_code'] != null
          ? jsonn['referral_code'].toString()
          : '',
      currentStreak: jsonn['current_streak'] != null
          ? int.parse(jsonn['current_streak'].toString())
          : 0,
      nativeLanguage: jsonn['native_language'] != null
          ? jsonn['native_language'].toString()
          : '',
      explanationLanguage: jsonn['explanation_language'] != null
          ? EXPLANATION_LANGUAGE_PREFERENCE.values
              .byName(jsonn['explanation_language'].toString())
          : EXPLANATION_LANGUAGE_PREFERENCE.values.first,
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      userId: userId,
      createdAt: createdAt,
      nickname: nickname,
      updatedAt: updatedAt,
      trialEndsAt: trialEndsAt,
      fcmToken: fcmToken,
      currencyBalance: currencyBalance,
      learningMotivation: learningMotivation,
      referralCode: referralCode,
      currentStreak: currentStreak,
      nativeLanguage: nativeLanguage,
      explanationLanguage: explanationLanguage,
    );
  }
}

class KyselyMigration implements SupadartClass<KyselyMigration> {
  final String name;
  final String timestamp;

  const KyselyMigration({
    required this.name,
    required this.timestamp,
  });

  static String get table_name => 'kysely_migration';
  static String get c_name => 'name';
  static String get c_timestamp => 'timestamp';

  static List<KyselyMigration> converter(List<Map<String, dynamic>> data) {
    return data.map(KyselyMigration.fromJson).toList();
  }

  static KyselyMigration converterSingle(Map<String, dynamic> data) {
    return KyselyMigration.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? name,
    String? timestamp,
  }) {
    return {
      if (name != null) 'name': name,
      if (timestamp != null) 'timestamp': timestamp,
    };
  }

  static Map<String, dynamic> insert({
    String? name,
    required String timestamp,
  }) {
    return _generateMap(
      name: name,
      timestamp: timestamp,
    );
  }

  static Map<String, dynamic> update({
    String? name,
    String? timestamp,
  }) {
    return _generateMap(
      name: name,
      timestamp: timestamp,
    );
  }

  factory KyselyMigration.fromJson(Map<String, dynamic> jsonn) {
    return KyselyMigration(
      name: jsonn['name'] != null ? jsonn['name'].toString() : '',
      timestamp:
          jsonn['timestamp'] != null ? jsonn['timestamp'].toString() : '',
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      name: name,
      timestamp: timestamp,
    );
  }
}

class GenerationTasks implements SupadartClass<GenerationTasks> {
  final String generationTaskId;
  final String userId;
  final String taskType;
  final GENERATION_TASK_STATUS status;
  final Map<String, dynamic> requestData;
  final Map<String, dynamic>? resultData;
  final DateTime createdAt;
  final DateTime updatedAt;

  const GenerationTasks({
    required this.generationTaskId,
    required this.userId,
    required this.taskType,
    required this.status,
    required this.requestData,
    this.resultData,
    required this.createdAt,
    required this.updatedAt,
  });

  static String get table_name => 'generation_tasks';
  static String get c_generationTaskId => 'generation_task_id';
  static String get c_userId => 'user_id';
  static String get c_taskType => 'task_type';
  static String get c_status => 'status';
  static String get c_requestData => 'request_data';
  static String get c_resultData => 'result_data';
  static String get c_createdAt => 'created_at';
  static String get c_updatedAt => 'updated_at';

  static List<GenerationTasks> converter(List<Map<String, dynamic>> data) {
    return data.map(GenerationTasks.fromJson).toList();
  }

  static GenerationTasks converterSingle(Map<String, dynamic> data) {
    return GenerationTasks.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? generationTaskId,
    String? userId,
    String? taskType,
    GENERATION_TASK_STATUS? status,
    Map<String, dynamic>? requestData,
    Map<String, dynamic>? resultData,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return {
      if (generationTaskId != null) 'generation_task_id': generationTaskId,
      if (userId != null) 'user_id': userId,
      if (taskType != null) 'task_type': taskType,
      if (status != null) 'status': status.toString().split('.').last,
      if (requestData != null) 'request_data': requestData,
      if (resultData != null) 'result_data': resultData,
      if (createdAt != null) 'created_at': createdAt.toUtc().toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt.toUtc().toIso8601String(),
    };
  }

  static Map<String, dynamic> insert({
    String? generationTaskId,
    required String userId,
    required String taskType,
    required GENERATION_TASK_STATUS status,
    required Map<String, dynamic> requestData,
    Map<String, dynamic>? resultData,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return _generateMap(
      generationTaskId: generationTaskId,
      userId: userId,
      taskType: taskType,
      status: status,
      requestData: requestData,
      resultData: resultData,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  static Map<String, dynamic> update({
    String? generationTaskId,
    String? userId,
    String? taskType,
    GENERATION_TASK_STATUS? status,
    Map<String, dynamic>? requestData,
    Map<String, dynamic>? resultData,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return _generateMap(
      generationTaskId: generationTaskId,
      userId: userId,
      taskType: taskType,
      status: status,
      requestData: requestData,
      resultData: resultData,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  factory GenerationTasks.fromJson(Map<String, dynamic> jsonn) {
    return GenerationTasks(
      generationTaskId: jsonn['generation_task_id'] != null
          ? jsonn['generation_task_id'].toString()
          : '',
      userId: jsonn['user_id'] != null ? jsonn['user_id'].toString() : '',
      taskType: jsonn['task_type'] != null ? jsonn['task_type'].toString() : '',
      status: jsonn['status'] != null
          ? GENERATION_TASK_STATUS.values.byName(jsonn['status'].toString())
          : GENERATION_TASK_STATUS.values.first,
      requestData: jsonn['request_data'] != null
          ? jsonn['request_data'] as Map<String, dynamic>
          : <String, dynamic>{},
      resultData: jsonn['result_data'] != null
          ? jsonn['result_data'] as Map<String, dynamic>
          : <String, dynamic>{},
      createdAt: jsonn['created_at'] != null
          ? DateTime.parse(jsonn['created_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
      updatedAt: jsonn['updated_at'] != null
          ? DateTime.parse(jsonn['updated_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      generationTaskId: generationTaskId,
      userId: userId,
      taskType: taskType,
      status: status,
      requestData: requestData,
      resultData: resultData,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}

class RevenuecatProcessedEvents
    implements SupadartClass<RevenuecatProcessedEvents> {
  final String eventId;
  final String eventType;
  final String userId;
  final DateTime processedAt;
  final Map<String, dynamic> rawPayload;

  const RevenuecatProcessedEvents({
    required this.eventId,
    required this.eventType,
    required this.userId,
    required this.processedAt,
    required this.rawPayload,
  });

  static String get table_name => 'revenuecat_processed_events';
  static String get c_eventId => 'event_id';
  static String get c_eventType => 'event_type';
  static String get c_userId => 'user_id';
  static String get c_processedAt => 'processed_at';
  static String get c_rawPayload => 'raw_payload';

  static List<RevenuecatProcessedEvents> converter(
      List<Map<String, dynamic>> data) {
    return data.map(RevenuecatProcessedEvents.fromJson).toList();
  }

  static RevenuecatProcessedEvents converterSingle(Map<String, dynamic> data) {
    return RevenuecatProcessedEvents.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? eventId,
    String? eventType,
    String? userId,
    DateTime? processedAt,
    Map<String, dynamic>? rawPayload,
  }) {
    return {
      if (eventId != null) 'event_id': eventId,
      if (eventType != null) 'event_type': eventType,
      if (userId != null) 'user_id': userId,
      if (processedAt != null)
        'processed_at': processedAt.toUtc().toIso8601String(),
      if (rawPayload != null) 'raw_payload': rawPayload,
    };
  }

  static Map<String, dynamic> insert({
    String? eventId,
    required String eventType,
    required String userId,
    DateTime? processedAt,
    required Map<String, dynamic> rawPayload,
  }) {
    return _generateMap(
      eventId: eventId,
      eventType: eventType,
      userId: userId,
      processedAt: processedAt,
      rawPayload: rawPayload,
    );
  }

  static Map<String, dynamic> update({
    String? eventId,
    String? eventType,
    String? userId,
    DateTime? processedAt,
    Map<String, dynamic>? rawPayload,
  }) {
    return _generateMap(
      eventId: eventId,
      eventType: eventType,
      userId: userId,
      processedAt: processedAt,
      rawPayload: rawPayload,
    );
  }

  factory RevenuecatProcessedEvents.fromJson(Map<String, dynamic> jsonn) {
    return RevenuecatProcessedEvents(
      eventId: jsonn['event_id'] != null ? jsonn['event_id'].toString() : '',
      eventType:
          jsonn['event_type'] != null ? jsonn['event_type'].toString() : '',
      userId: jsonn['user_id'] != null ? jsonn['user_id'].toString() : '',
      processedAt: jsonn['processed_at'] != null
          ? DateTime.parse(jsonn['processed_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
      rawPayload: jsonn['raw_payload'] != null
          ? jsonn['raw_payload'] as Map<String, dynamic>
          : <String, dynamic>{},
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      eventId: eventId,
      eventType: eventType,
      userId: userId,
      processedAt: processedAt,
      rawPayload: rawPayload,
    );
  }
}

class Phrases implements SupadartClass<Phrases> {
  final String id;
  final String diaryVersionId;
  final String content;
  final int startIndex;
  final int endIndex;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Phrases({
    required this.id,
    required this.diaryVersionId,
    required this.content,
    required this.startIndex,
    required this.endIndex,
    required this.createdAt,
    required this.updatedAt,
  });

  static String get table_name => 'phrases';
  static String get c_id => 'id';
  static String get c_diaryVersionId => 'diary_version_id';
  static String get c_content => 'content';
  static String get c_startIndex => 'start_index';
  static String get c_endIndex => 'end_index';
  static String get c_createdAt => 'created_at';
  static String get c_updatedAt => 'updated_at';

  static List<Phrases> converter(List<Map<String, dynamic>> data) {
    return data.map(Phrases.fromJson).toList();
  }

  static Phrases converterSingle(Map<String, dynamic> data) {
    return Phrases.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? id,
    String? diaryVersionId,
    String? content,
    int? startIndex,
    int? endIndex,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return {
      if (id != null) 'id': id,
      if (diaryVersionId != null) 'diary_version_id': diaryVersionId,
      if (content != null) 'content': content,
      if (startIndex != null) 'start_index': startIndex,
      if (endIndex != null) 'end_index': endIndex,
      if (createdAt != null) 'created_at': createdAt.toUtc().toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt.toUtc().toIso8601String(),
    };
  }

  static Map<String, dynamic> insert({
    String? id,
    required String diaryVersionId,
    required String content,
    required int startIndex,
    required int endIndex,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return _generateMap(
      id: id,
      diaryVersionId: diaryVersionId,
      content: content,
      startIndex: startIndex,
      endIndex: endIndex,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  static Map<String, dynamic> update({
    String? id,
    String? diaryVersionId,
    String? content,
    int? startIndex,
    int? endIndex,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return _generateMap(
      id: id,
      diaryVersionId: diaryVersionId,
      content: content,
      startIndex: startIndex,
      endIndex: endIndex,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  factory Phrases.fromJson(Map<String, dynamic> jsonn) {
    return Phrases(
      id: jsonn['id'] != null ? jsonn['id'].toString() : '',
      diaryVersionId: jsonn['diary_version_id'] != null
          ? jsonn['diary_version_id'].toString()
          : '',
      content: jsonn['content'] != null ? jsonn['content'].toString() : '',
      startIndex: jsonn['start_index'] != null
          ? int.parse(jsonn['start_index'].toString())
          : 0,
      endIndex: jsonn['end_index'] != null
          ? int.parse(jsonn['end_index'].toString())
          : 0,
      createdAt: jsonn['created_at'] != null
          ? DateTime.parse(jsonn['created_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
      updatedAt: jsonn['updated_at'] != null
          ? DateTime.parse(jsonn['updated_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      id: id,
      diaryVersionId: diaryVersionId,
      content: content,
      startIndex: startIndex,
      endIndex: endIndex,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}

class KyselyMigrationLock implements SupadartClass<KyselyMigrationLock> {
  final String id;
  final int isLocked;

  const KyselyMigrationLock({
    required this.id,
    required this.isLocked,
  });

  static String get table_name => 'kysely_migration_lock';
  static String get c_id => 'id';
  static String get c_isLocked => 'is_locked';

  static List<KyselyMigrationLock> converter(List<Map<String, dynamic>> data) {
    return data.map(KyselyMigrationLock.fromJson).toList();
  }

  static KyselyMigrationLock converterSingle(Map<String, dynamic> data) {
    return KyselyMigrationLock.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? id,
    int? isLocked,
  }) {
    return {
      if (id != null) 'id': id,
      if (isLocked != null) 'is_locked': isLocked,
    };
  }

  static Map<String, dynamic> insert({
    String? id,
    int? isLocked,
  }) {
    return _generateMap(
      id: id,
      isLocked: isLocked,
    );
  }

  static Map<String, dynamic> update({
    String? id,
    int? isLocked,
  }) {
    return _generateMap(
      id: id,
      isLocked: isLocked,
    );
  }

  factory KyselyMigrationLock.fromJson(Map<String, dynamic> jsonn) {
    return KyselyMigrationLock(
      id: jsonn['id'] != null ? jsonn['id'].toString() : '',
      isLocked: jsonn['is_locked'] != null
          ? int.parse(jsonn['is_locked'].toString())
          : 0,
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      id: id,
      isLocked: isLocked,
    );
  }
}

class CorrectionCategories implements SupadartClass<CorrectionCategories> {
  final String id;
  final Map<String, dynamic> names;
  final String? nameEn;
  final String? nameKo;

  const CorrectionCategories({
    required this.id,
    required this.names,
    this.nameEn,
    this.nameKo,
  });

  static String get table_name => 'correction_categories';
  static String get c_id => 'id';
  static String get c_names => 'names';
  static String get c_nameEn => 'name_en';
  static String get c_nameKo => 'name_ko';

  static List<CorrectionCategories> converter(List<Map<String, dynamic>> data) {
    return data.map(CorrectionCategories.fromJson).toList();
  }

  static CorrectionCategories converterSingle(Map<String, dynamic> data) {
    return CorrectionCategories.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? id,
    Map<String, dynamic>? names,
    String? nameEn,
    String? nameKo,
  }) {
    return {
      if (id != null) 'id': id,
      if (names != null) 'names': names,
      if (nameEn != null) 'name_en': nameEn,
      if (nameKo != null) 'name_ko': nameKo,
    };
  }

  static Map<String, dynamic> insert({
    String? id,
    required Map<String, dynamic> names,
    String? nameEn,
    String? nameKo,
  }) {
    return _generateMap(
      id: id,
      names: names,
      nameEn: nameEn,
      nameKo: nameKo,
    );
  }

  static Map<String, dynamic> update({
    String? id,
    Map<String, dynamic>? names,
    String? nameEn,
    String? nameKo,
  }) {
    return _generateMap(
      id: id,
      names: names,
      nameEn: nameEn,
      nameKo: nameKo,
    );
  }

  factory CorrectionCategories.fromJson(Map<String, dynamic> jsonn) {
    return CorrectionCategories(
      id: jsonn['id'] != null ? jsonn['id'].toString() : '',
      names: jsonn['names'] != null
          ? jsonn['names'] as Map<String, dynamic>
          : <String, dynamic>{},
      nameEn: jsonn['name_en'] != null ? jsonn['name_en'].toString() : '',
      nameKo: jsonn['name_ko'] != null ? jsonn['name_ko'].toString() : '',
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      id: id,
      names: names,
      nameEn: nameEn,
      nameKo: nameKo,
    );
  }
}

class UserQuestions implements SupadartClass<UserQuestions> {
  final String id;
  final String userId;
  final String title;
  final String body;
  final String deviceInfo;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserQuestions({
    required this.id,
    required this.userId,
    required this.title,
    required this.body,
    required this.deviceInfo,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  static String get table_name => 'user_questions';
  static String get c_id => 'id';
  static String get c_userId => 'user_id';
  static String get c_title => 'title';
  static String get c_body => 'body';
  static String get c_deviceInfo => 'device_info';
  static String get c_status => 'status';
  static String get c_createdAt => 'created_at';
  static String get c_updatedAt => 'updated_at';

  static List<UserQuestions> converter(List<Map<String, dynamic>> data) {
    return data.map(UserQuestions.fromJson).toList();
  }

  static UserQuestions converterSingle(Map<String, dynamic> data) {
    return UserQuestions.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? id,
    String? userId,
    String? title,
    String? body,
    String? deviceInfo,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return {
      if (id != null) 'id': id,
      if (userId != null) 'user_id': userId,
      if (title != null) 'title': title,
      if (body != null) 'body': body,
      if (deviceInfo != null) 'device_info': deviceInfo,
      if (status != null) 'status': status,
      if (createdAt != null) 'created_at': createdAt.toUtc().toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt.toUtc().toIso8601String(),
    };
  }

  static Map<String, dynamic> insert({
    String? id,
    required String userId,
    required String title,
    required String body,
    required String deviceInfo,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return _generateMap(
      id: id,
      userId: userId,
      title: title,
      body: body,
      deviceInfo: deviceInfo,
      status: status,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  static Map<String, dynamic> update({
    String? id,
    String? userId,
    String? title,
    String? body,
    String? deviceInfo,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return _generateMap(
      id: id,
      userId: userId,
      title: title,
      body: body,
      deviceInfo: deviceInfo,
      status: status,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  factory UserQuestions.fromJson(Map<String, dynamic> jsonn) {
    return UserQuestions(
      id: jsonn['id'] != null ? jsonn['id'].toString() : '',
      userId: jsonn['user_id'] != null ? jsonn['user_id'].toString() : '',
      title: jsonn['title'] != null ? jsonn['title'].toString() : '',
      body: jsonn['body'] != null ? jsonn['body'].toString() : '',
      deviceInfo:
          jsonn['device_info'] != null ? jsonn['device_info'].toString() : '',
      status: jsonn['status'] != null ? jsonn['status'].toString() : '',
      createdAt: jsonn['created_at'] != null
          ? DateTime.parse(jsonn['created_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
      updatedAt: jsonn['updated_at'] != null
          ? DateTime.parse(jsonn['updated_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      id: id,
      userId: userId,
      title: title,
      body: body,
      deviceInfo: deviceInfo,
      status: status,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}

class FeatureUsageTransactions
    implements SupadartClass<FeatureUsageTransactions> {
  final String? id;
  final String? userId;
  final int? amount;
  final String? featureId;
  final Map<String, dynamic>? metadata;
  final DateTime? createdAt;

  const FeatureUsageTransactions({
    this.id,
    this.userId,
    this.amount,
    this.featureId,
    this.metadata,
    this.createdAt,
  });

  static String get table_name => 'feature_usage_transactions';
  static String get c_id => 'id';
  static String get c_userId => 'user_id';
  static String get c_amount => 'amount';
  static String get c_featureId => 'feature_id';
  static String get c_metadata => 'metadata';
  static String get c_createdAt => 'created_at';

  static List<FeatureUsageTransactions> converter(
      List<Map<String, dynamic>> data) {
    return data.map(FeatureUsageTransactions.fromJson).toList();
  }

  static FeatureUsageTransactions converterSingle(Map<String, dynamic> data) {
    return FeatureUsageTransactions.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? id,
    String? userId,
    int? amount,
    String? featureId,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
  }) {
    return {
      if (id != null) 'id': id,
      if (userId != null) 'user_id': userId,
      if (amount != null) 'amount': amount,
      if (featureId != null) 'feature_id': featureId,
      if (metadata != null) 'metadata': metadata,
      if (createdAt != null) 'created_at': createdAt.toUtc().toIso8601String(),
    };
  }

  static Map<String, dynamic> insert({
    String? id,
    String? userId,
    int? amount,
    String? featureId,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
  }) {
    return _generateMap(
      id: id,
      userId: userId,
      amount: amount,
      featureId: featureId,
      metadata: metadata,
      createdAt: createdAt,
    );
  }

  static Map<String, dynamic> update({
    String? id,
    String? userId,
    int? amount,
    String? featureId,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
  }) {
    return _generateMap(
      id: id,
      userId: userId,
      amount: amount,
      featureId: featureId,
      metadata: metadata,
      createdAt: createdAt,
    );
  }

  factory FeatureUsageTransactions.fromJson(Map<String, dynamic> jsonn) {
    return FeatureUsageTransactions(
      id: jsonn['id'] != null ? jsonn['id'].toString() : '',
      userId: jsonn['user_id'] != null ? jsonn['user_id'].toString() : '',
      amount:
          jsonn['amount'] != null ? int.parse(jsonn['amount'].toString()) : 0,
      featureId:
          jsonn['feature_id'] != null ? jsonn['feature_id'].toString() : '',
      metadata: jsonn['metadata'] != null
          ? jsonn['metadata'] as Map<String, dynamic>
          : <String, dynamic>{},
      createdAt: jsonn['created_at'] != null
          ? DateTime.parse(jsonn['created_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      id: id,
      userId: userId,
      amount: amount,
      featureId: featureId,
      metadata: metadata,
      createdAt: createdAt,
    );
  }
}

class Notifications implements SupadartClass<Notifications> {
  final String id;
  final String userId;
  final DateTime createdAt;
  final String body;

  const Notifications({
    required this.id,
    required this.userId,
    required this.createdAt,
    required this.body,
  });

  static String get table_name => 'notifications';
  static String get c_id => 'id';
  static String get c_userId => 'user_id';
  static String get c_createdAt => 'created_at';
  static String get c_body => 'body';

  static List<Notifications> converter(List<Map<String, dynamic>> data) {
    return data.map(Notifications.fromJson).toList();
  }

  static Notifications converterSingle(Map<String, dynamic> data) {
    return Notifications.fromJson(data);
  }

  static Map<String, dynamic> _generateMap({
    String? id,
    String? userId,
    DateTime? createdAt,
    String? body,
  }) {
    return {
      if (id != null) 'id': id,
      if (userId != null) 'user_id': userId,
      if (createdAt != null) 'created_at': createdAt.toUtc().toIso8601String(),
      if (body != null) 'body': body,
    };
  }

  static Map<String, dynamic> insert({
    String? id,
    required String userId,
    DateTime? createdAt,
    required String body,
  }) {
    return _generateMap(
      id: id,
      userId: userId,
      createdAt: createdAt,
      body: body,
    );
  }

  static Map<String, dynamic> update({
    String? id,
    String? userId,
    DateTime? createdAt,
    String? body,
  }) {
    return _generateMap(
      id: id,
      userId: userId,
      createdAt: createdAt,
      body: body,
    );
  }

  factory Notifications.fromJson(Map<String, dynamic> jsonn) {
    return Notifications(
      id: jsonn['id'] != null ? jsonn['id'].toString() : '',
      userId: jsonn['user_id'] != null ? jsonn['user_id'].toString() : '',
      createdAt: jsonn['created_at'] != null
          ? DateTime.parse(jsonn['created_at'].toString())
          : DateTime.fromMillisecondsSinceEpoch(0),
      body: jsonn['body'] != null ? jsonn['body'].toString() : '',
    );
  }

  Map<String, dynamic> toJson() {
    return _generateMap(
      id: id,
      userId: userId,
      createdAt: createdAt,
      body: body,
    );
  }
}
