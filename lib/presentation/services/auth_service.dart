import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:posthog_flutter/posthog_flutter.dart';
import 'package:purchases_flutter/purchases_flutter.dart' as RevenueCat;
import 'package:supabase_auth_ui/supabase_auth_ui.dart';

import '../../utils/my_logger.dart';
import '../../utils/supabase_auth_helper.dart';
import '../pages/mobile/home_page/card_page/card_list_page_widget.dart';
import '../pages/mobile/home_page/diary_page/screen/diary_list_screen.dart';
import '../pages/mobile/home_page/my_page/profile_page/profile_page.dart';
import '../pages/mobile/home_page/my_page/subscribe_page/component/product_box.dart';

enum LoginType {
  email,
  magicLink,
  kakao,
  apple,
  google,
}

class AuthService extends ChangeNotifier {
  bool _isLoggedIn = false; // 초기 상태: 로그아웃
  bool get isLoggedIn => _isLoggedIn;
  bool _isInitialized = false; // 초기화 완료 여부 플래그
  bool get isInitialized => _isInitialized;

  AuthService() {
    initialize();
  }

  void prefetchQueries() {
    myLog('Prefetching queries');
    ProfilePageWidget.prefetchQueries();
    NoteListScreen.prefetchQueries();
    ProductBox.prefetchQueries();
    CardListPageWidget.prefetchQueries();
  }

  void resetQueryCache() {
    CachedQuery.instance.deleteCache();
    prefetchQueries();
  }

  void initialize() {
    if (_isInitialized) return;
    // Supabase 초기화
    try {
      Supabase.instance.client.auth.onAuthStateChange.listen((data) async {
        final AuthChangeEvent event = data.event;
        final Session? session = data.session;

        bool previouslyLoggedIn = _isLoggedIn;

        if (event == AuthChangeEvent.signedIn ||
            event == AuthChangeEvent.userUpdated ||
            event == AuthChangeEvent.passwordRecovery ||
            event == AuthChangeEvent.initialSession) {
          print('Auth state changed: ${data.event}');
          _isLoggedIn = session != null && session.user.id.isNotEmpty;
          if (_isLoggedIn) {
            final identifyUser =
                Posthog().identify(userId: session!.user.id, userProperties: {
              "email": session.user.email ?? '',
            }, userPropertiesSetOnce: {
              "date_of_first_log_in": DateTime.now().toIso8601String(),
            });
            await Future.wait([
              identifyUser,
              Posthog().capture(eventName: 'user_login', properties: {
                "login_time_stemp": DateTime.now().toIso8601String(),
              }),
            ]);
          }
        } else if (event == AuthChangeEvent.signedOut) {
          _isLoggedIn = false;
          await Posthog().capture(eventName: 'user_signed_out', properties: {
            "signed_out_time_stemp": DateTime.now().toIso8601String(),
          });
        }
        if (previouslyLoggedIn != _isLoggedIn) {
          resetQueryCache();
        }
        print(
            "AuthService Listener executing on instance: ${this.hashCode}"); // Or just hashCode
        notifyListeners();
      });
    } catch (e) {
      // 초기화 실패 처리
      myLog(
        'SupabaseAuthHelper initialization failed: $e',
        LogLevel.error,
      );
      throw Exception('초기화 실패: $e');
    }

    RevenueCat.Purchases.addCustomerInfoUpdateListener((customerInfo) {
      // myLog(
      //     'Revenue cat customer info has been updated, invalidating cached query cache. New customerInfo: $customerInfo');
      // resetQueryCache();
    });

    _isInitialized = true; // 초기화 완료 표시
  }

  Future<void> login(LoginType type, String? email) async {
    // ... 실제 로그인 로직 ...
    try {
      if (type == LoginType.email) {
        // 이메일 로그인 로직
      } else if (type == LoginType.magicLink && email != null) {
        // Magic Link 로그인 로직
        await signInWithMagicLink(
          email,
        );
      }
      _isLoggedIn = true;
    } catch (e) {
      myLog(
        'Login error: $e',
        LogLevel.error,
      );
      throw Exception('로그인 실패: $e');
    }
  }

  Future<void> logout() async {
    // ... 실제 로그아웃 로직 ...
    try {
      await Supabase.instance.client.auth.signOut();
    } catch (e) {
      myLog(
        'Logout error: $e',
        LogLevel.error,
      );
      throw Exception('로그아웃 실패: $e');
    }
    _isLoggedIn = false;
  }
}
