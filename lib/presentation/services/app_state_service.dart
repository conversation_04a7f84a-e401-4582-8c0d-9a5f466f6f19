import 'package:flutter/foundation.dart';
import 'package:langda/backend/langda_api_client.dart';

import '../../states/app_state.dart';

enum UpdateState {
  unknown,
  upToDate,
  updateRequired,
}

class AppStateService with ChangeNotifier {
  bool _isLoading = false;
  bool get isLoading => _isLoading;
  UpdateState _updateState = UpdateState.unknown;
  UpdateState get updateState => _updateState;

  AppStateService() {
    checkVersionState();
  }

  Future<void> checkVersionState() async {
    try {
      final updateAvailable =
          await LangdaApiClient.client.system.getUpgradeRequiredBuildNumber(
        buildNumber: LDAppState().appBuildNumber.toString(),
      );
      if (updateAvailable.upgradeRequired) {
        _updateState = UpdateState.updateRequired;
      } else {
        _updateState = UpdateState.upToDate;
      }
    } catch (e) {
      _updateState = UpdateState.unknown;
    }

    notifyListeners();
  }

  Future<void> setLoading(bool loading) async {
    _isLoading = loading;
    notifyListeners();
  }

  // * diary slot
  // DiarySlot _diarySlot = DiarySlot();
  // DiarySlot get diarySlot => _diarySlot;
  // set diarySlot(DiarySlot value) {
  //   _diarySlot = value;
  // }

  // void updateDiarySlotState(
  //   DiarySlotState state, {
  //   String? entryId,
  //   String? content,
  //   String? error,
  //   String? nickname,
  //   int? gems,
  // }) {
  //   _diarySlot.state = state;
  //   if (entryId != null) {
  //     _diarySlot.entryId = entryId;
  //   }
  //   if (content != null) {
  //     _diarySlot.content = content;
  //   }
  //   if (error != null) {
  //     _diarySlot.error = error;
  //   }
  //   if (nickname != null) {
  //     _diarySlot.nickname = nickname;
  //   }
  //   if (gems != null) {
  //     _diarySlot.gems = gems;
  //   }
  //   notifyListeners();
  // }
}
