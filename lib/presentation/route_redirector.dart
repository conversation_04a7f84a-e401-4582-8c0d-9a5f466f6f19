import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/presentation/router_constants.dart';
import 'package:langda/presentation/services/app_state_service.dart';
import 'package:langda/queries/auth_query.dart';

import 'services/auth_service.dart';

class RouteRedirector {
  final AuthService _authService;
  final AppStateService _appStateService;

  RouteRedirector(
    this._authService,
    this._appStateService,
  );

  Future<String?> redirect(BuildContext context, GoRouterState state) async {
    final isLoggedIn = _authService.isLoggedIn;
    final currentPath = state.matchedLocation;
    final isFromMagicLink =
        state.uri.toString().contains(DeepLinkPaths.signInMagicLink) ||
            state.uri.toString().contains(DeepLinkPaths.signUpMagicLink);

    print(
        'Redirect Check: isLoggedIn=$isLoggedIn, currentPath=$currentPath, isFromMagicLink=$isFromMagicLink');
    // 1. Check if user is logged in with magic link
    if (isFromMagicLink) {
      final result = await Future.delayed(
        const Duration(milliseconds: 500),
      ).then((_) async {
        try {
          final userInfo = await fetchUserInfoQuery().result;
          if (userInfo.data != null) {
            return RoutePaths.home;
          } else {
            return RoutePaths.createAccountPath();
          }
        } catch (e) {
          print('Error fetching user info: $e');
          return RoutePaths.createAccountPath();
        }
      });
      return result;
    } else {
// 2. Check if user is logged in
      if (isLoggedIn) {
        if (currentPath == RoutePaths.home) {
          return null; // No redirect needed
        } else {
          final userInfo = await fetchUserInfoQuery().result;
          if (userInfo.data == null) {
            return RoutePaths.createAccountPath();
          }
        }
      }
      // 3. Check if user is logged in and comes from login
      if (isLoggedIn && isFromLogin(state)) {
        return RoutePaths.home;
      }
      // 4. Check if user is logged out and comes from login
      if (!isLoggedIn && (currentPath == RoutePaths.logoutPath())) {
        return RoutePaths.home;
      }
    }
  }

  bool isFromLogin(GoRouterState state) {
    final incomingUri = state.uri;
    // Sign in deep links and Sign up deep links
    final signInDeepLinks = [
      DeepLinkPaths.signInMagicLink,
      DeepLinkPaths.signInSocial,
    ];
    final isFromSignInDeeplink = incomingUri.scheme == DeepLinkPaths.scheme &&
        signInDeepLinks.any((link) => incomingUri.host.contains(link));
    final signUpDeepLinks = [
      DeepLinkPaths.signUpSocial,
      DeepLinkPaths.signUpMagicLink,
    ];
    final isFromSignUpDeeplink = incomingUri.scheme == DeepLinkPaths.scheme &&
        signUpDeepLinks.any((link) => incomingUri.host.contains(link));

    // Internal routes
    final fromLoginRoutes = [
      RoutePaths.login,
      RoutePaths.socialLogin,
      RoutePaths.signupWithSocial,
      RoutePaths.signupWithMagicLink,
    ];
    return isFromSignUpDeeplink ||
        isFromSignInDeeplink ||
        fromLoginRoutes.any((route) => incomingUri.path.contains(route));
  }
}
