import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/utils/supabase_auth_helper.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../queries/auth_query.dart';
import '../../../../utils/my_logger.dart';
import '../../../router_constants.dart';
import '../constants.dart';

class SocialAuthForm extends StatefulWidget {
  final List<SocialType> socialTypes;
  final String supportLanguage;
  final double buttonsGap;
  final bool showTrailingText;

  final Function(String error, String social) onSignInError;
  final Function() onSignInStart;
  final Function() onSignInEnd;

  const SocialAuthForm({
    super.key,
    required this.socialTypes,
    this.supportLanguage = 'ko',
    this.buttonsGap = 15,
    this.showTrailingText = false,
    required this.onSignInError,
    required this.onSignInStart,
    required this.onSignInEnd,
  });

  @override
  State<SocialAuthForm> createState() => _SocialAuthFormState();
}

class _SocialAuthFormState extends State<SocialAuthForm> {
  late final buttonList = <Widget>[];

  Future<void> onDone() async {
    try {
      final userResult = await fetchUserInfoQuery().result;
      final user = userResult.data;
      final loggedInUser = Supabase.instance.client.auth.currentUser;
      // TODO: 이제 Redirector 다 잘 처리하는 것 같아 밑 코드는 중복으로 삭제 필요할듯.
      // TODO: 테스트해보니까 "return" 해도 createAccountPath()로 잘 넘어가더라.
      if (loggedInUser == null) {
        widget.onSignInError('User not found', 'none');
        return;
      } else {
        final loggedInUserCreatedAtDatetime =
            DateTime.parse(loggedInUser.createdAt);
        if (DateUtils.isSameDay(
                loggedInUserCreatedAtDatetime, DateTime.now()) &&
            user == null) {
          context.go(
            RoutePaths.createAccountPath(),
          );
        } else {
          context.go(
            RoutePaths.signupWithSocial,
          );
        }
      }
    } catch (e) {
      myLog('Error fetching user info: $e', LogLevel.error);
      return;
    }
    widget.onSignInEnd();
  }

  @override
  void initState() {
    super.initState();
    for (var socialType in widget.socialTypes) {
      final Map<SocialType, SocialModel> pathMap = {
        SocialType.kakao: SocialModel(
            iconPath: 'assets/images/logos/kakao.png',
            text: widget.supportLanguage == 'ko'
                ? '카카오톡\n로그인'
                : 'Login with Kakao',
            color: const Color(0xfff7e600),
            onTap: () async {
              widget.onSignInStart();

              await signInWithKakao().then((value) async {
                await onDone();
              }).catchError((error) {
                widget.onSignInError(error.toString(), 'kakao');
              });
            }),
        SocialType.google: SocialModel(
          iconPath: 'assets/images/logos/google.png',
          text:
              widget.supportLanguage == 'ko' ? '구글\n로그인' : 'Login with Google',
          color: Colors.transparent,
          borderColor: LDColors.darkHoverGrey,
          onTap: () async {
            widget.onSignInStart();
            await signInWithGoogle().then((value) async {
              await onDone();
            }).catchError((error) {
              widget.onSignInError(error.toString(), 'google');
            });
          },
        ),
        SocialType.apple: SocialModel(
            iconPath: 'assets/images/logos/apple.png',
            text:
                widget.supportLanguage == 'ko' ? '애플\n로그인' : 'Login with Apple',
            color: Colors.transparent,
            borderColor: LDColors.darkHoverGrey,
            onTap: () async {
              widget.onSignInStart();
              await signInWithApple().then((value) async {
                await onDone();
              }).catchError((error) {
                widget.onSignInError(error.toString(), 'apple');
              });
            }),
      };
      final socialModel = pathMap[socialType];
      buttonList.add(
        button(
          iconPath: socialModel!.iconPath,
          text: socialModel.text,
          onTap: socialModel.onTap,
          color: socialModel.color,
          borderColor: socialModel.borderColor,
        ),
      );
    }
  }

  Widget button({
    required String iconPath,
    required String text,
    Color? textColor,
    required Future<dynamic> Function() onTap,
    double? gap,
    double? fontSize,
    required Color color,
    Color? borderColor,
    double? padding,
    double? borderRadius,
    double? iconWidth,
    double? iconHeight,
  }) {
    return Semantics(
      button: true,
      label: text,
      child: GestureDetector(
        onTap: () async {
          final result = await onTap();

          if (result == false) {
            widget.onSignInError(result, text);
            return;
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(
                  padding ?? 13,
                ),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(
                    borderRadius ?? 10,
                  ),
                  border: Border.all(
                    color: borderColor ?? Colors.transparent,
                    width: 1,
                  ),
                ),
                child: Image.asset(
                  iconPath,
                  width: iconWidth ?? 30,
                  height: iconHeight ?? 30,
                  fit: BoxFit.scaleDown,
                ),
              ),
              SizedBox(
                height: gap ?? 5,
              ),
              widget.showTrailingText
                  ? Text(
                      text,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: textColor ?? LDColors.darkHoverGrey,
                        fontSize: fontSize ?? 14.sp,
                      ),
                      maxLines: 2,
                    )
                  : Container(),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.max,
      children: [
        ...buttonList,
      ],
    );
  }
}

class SocialModel {
  final String iconPath;
  final String text;
  final Color color;
  final Color? borderColor;
  final Future<dynamic> Function() onTap;

  SocialModel({
    required this.iconPath,
    required this.text,
    required this.color,
    this.borderColor,
    required this.onTap,
  });
}

enum SocialType {
  kakao,
  google,
  apple,
  none,
}
