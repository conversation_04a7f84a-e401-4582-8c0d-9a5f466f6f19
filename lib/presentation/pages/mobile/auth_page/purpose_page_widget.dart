import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';

import '../../../../common/LDState.dart';
import '../../component.dart';
import '../constants.dart';

class PurposePageWidget extends StatefulWidget {
  final Function(String)? onTapNext;
  const PurposePageWidget({super.key, this.onTapNext});

  @override
  State<PurposePageWidget> createState() => _PurposePageWidgetState();
}

class _PurposePageWidgetState extends LDState<PurposePageWidget> {
  List<String> _purposes = [
    'social',
    'studyabroad',
    'emigration',
    'career',
    'fun',
    'other',
  ];

  String _selectedPurpose = '';

  @override
  void initState() {
    super.initState();
    _selectedPurpose = _purposes[0];
  }

  @override
  Widget buildContent(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(defaultPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              Container(
                child: Text(
                  FlutterI18n.translate(
                      context, 'registration.purposes.request'),
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                    color: LDColors.mainGrey,
                  ),
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              for (var purpose in _purposes)
                Padding(
                  padding: const EdgeInsets.only(bottom: 10),
                  child: _purposeListCellBuilder(
                      purposeName: FlutterI18n.translate(
                          context, 'registration.purposes.list.$purpose'),
                      imagePath:
                          'assets/images/icons/purposes/${purpose.toLowerCase()}.png',
                      isSelected: _selectedPurpose == purpose,
                      onTap: () {
                        setState(() {
                          _selectedPurpose = purpose;
                        });
                      }),
                ),
              const SizedBox(
                height: 10,
              ),
              LDButton(
                label: FlutterI18n.translate(context, 'button.label.next'),
                onPressed: () {
                  if (_selectedPurpose.isNotEmpty) {
                    widget.onTapNext?.call(_selectedPurpose);
                  }
                },
                isActivated: _selectedPurpose.isNotEmpty,
                fonSize: 18,
              ),
            ],
          ),
        ),
      ),
    );
  }

  GestureDetector _purposeListCellBuilder({
    String purposeName = 'Social',
    String imagePath = 'assets/images/icons/purposes/social.png',
    bool isSelected = false,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isSelected ? LDColors.mainLime : LDColors.lightGrey,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            Image.asset(
              imagePath,
              width: 40,
            ),
            const SizedBox(
              width: 20,
            ),
            Flexible(
              child: Text(
                purposeName,
                style: TextStyle(
                  fontSize: 20,
                  color: LDColors.mainGrey,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
