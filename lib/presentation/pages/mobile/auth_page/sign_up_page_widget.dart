import 'package:async_button_builder/async_button_builder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:langda/common/LDState.dart';
import 'package:moon_design/moon_design.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../../utils/supabase_auth_helper.dart';
import '../../component.dart';
import '../constants.dart';

class SignUpPageWidget extends StatefulWidget {
  const SignUpPageWidget({super.key});

  @override
  State<SignUpPageWidget> createState() => _SignUpPageWidgetState();
}

class _SignUpPageWidgetState extends LDState<SignUpPageWidget> {
  TextEditingController _emailController = TextEditingController();
  GlobalKey<FormState> _emailKey = GlobalKey<FormState>();
  bool isValidEmail(String email) {
    final emailRegExp = RegExp(r'^.+@.+\...+$');
    return emailRegExp.hasMatch(email);
  }

  @override
  void initState() {
    pageName = 'Sign Up';

    super.initState();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _emailKey.currentState?.dispose();
    super.dispose();
  }

  @override
  buildContent(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        appBar: AppBar(
          centerTitle: true,
          title: Text(
            FlutterI18n.translate(
              context,
              'registration.title',
            ),
            style: appTitleStyle,
          ),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Padding(
                  padding: const EdgeInsets.all(6.0),
                  child: Text(
                    FlutterI18n.translate(
                      context,
                      'login.email.hint',
                    ),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                SizedBox(
                  height: 5,
                ),
                Form(
                  key: _emailKey,
                  child: MoonFormTextInput(
                    controller: _emailController,
                    activeBorderColor: Theme.of(context).colorScheme.primary,
                    hintText: FlutterI18n.translate(
                        context, 'registration.email.hint'),
                    maxLines: 1,
                    onChanged: (value) => setState(() {}),
                  ),
                ),
                SizedBox(
                  height: 5,
                ),
                Padding(
                  padding: const EdgeInsets.all(6.0),
                  child: Text(
                    FlutterI18n.translate(
                        context, 'registration.email.description'),
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w400,
                      color: LDColors.mainGrey,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                SizedBox(
                  height: 70,
                  child: AsyncButtonBuilder(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: _emailController.text.trim().isEmpty
                            ? LDColors.lightGrey
                            : Theme.of(context).primaryColor,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Text(
                            FlutterI18n.translate(
                              context,
                              'button.label.complete',
                            ),
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    onPressed: () async {
                      if (_emailController.text.trim().isNotEmpty) {
                        if (isValidEmail(_emailController.text.trim())) {
                          try {
                            await signUpWithMagicLink(
                                _emailController.text.trim());
                          } catch (e) {
                            Sentry.addBreadcrumb(
                              Breadcrumb(
                                message: 'Error: ${e.toString()}',
                                timestamp: DateTime.now(),
                                data: {
                                  'email': _emailController.text.trim(),
                                },
                              ),
                            );

                            showLDToaster(
                              context,
                              FlutterI18n.translate(
                                context,
                                'registration.email.error.invalid',
                              ),
                              ToastType.error,
                            );
                            return;
                          }
                          showLDToaster(
                            context,
                            FlutterI18n.translate(
                              context,
                              'registration.email.success',
                            ),
                            ToastType.success,
                          );
                        } else {
                          showLDToaster(
                            context,
                            FlutterI18n.translate(
                              context,
                              'registration.email.error.invalid.pattern',
                            ),
                            ToastType.error,
                          );
                        }
                      } else {
                        showLDToaster(
                          context,
                          FlutterI18n.translate(
                            context,
                            'registration.email.error.required',
                          ),
                          ToastType.error,
                        );
                      }
                    },
                    builder: (context, child, callback, _) {
                      return Container(
                        alignment: Alignment.center,
                        child: GestureDetector(
                          onTap: callback,
                          child: child,
                        ),
                      );
                    },
                    loadingWidget: CircularProgressIndicator(),
                    showSuccess: false,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
