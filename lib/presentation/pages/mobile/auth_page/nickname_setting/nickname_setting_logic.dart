import 'dart:math';

import 'package:langda/backend/service/auth_service.dart';
import 'package:langda/providers/create_profile_provider.dart'; // Import the provider
import 'package:langda/states/app_state.dart';
import 'package:langda/utils/my_logger.dart';
import 'package:random_nickname/random_nickname.dart';

import '../../../mobile/constants.dart';

class NicknameSettingLogic {
  // Removed explanationLanguage and nativeLanguage from constructor parameters
  final AuthService _authService;
  final CreateProfileProvider _profileProvider; // Add provider instance

  NicknameSettingLogic({
    AuthService? authService,
    required CreateProfileProvider profileProvider, // Require provider
  })  : _authService = authService ?? AuthService(),
        _profileProvider = profileProvider; // Initialize provider

  String generateNickname(String locale) {
    if (locale == 'ko') {
      return randomNickname([korAdjectiveEmotion, korNounAnimal])
          .replaceAll(' ', '');
    } else {
      final random = Random();
      final adjective =
          engAdjectiveEmotion[random.nextInt(engAdjectiveEmotion.length)];
      final animal = engNounAnimal[random.nextInt(engNounAnimal.length)];
      return "$adjective$animal";
    }
  }

  Future<void> updateProfile(
    String nickname, {
    String? purpose, // Purpose might still come from widget or provider
    String? referralCode,
    // Removed explanationLanguage and nativeLanguage parameters
  }) async {
    // Get languages from the provider
    final explanationLanguage = _profileProvider.explanationLanguage;
    final nativeLanguage = _profileProvider.nativeLanguage;

    if (purpose != null) {
      await _authService.createInitialProfile(
        nickname,
        purpose,
        referralCode: referralCode,
        explanationLanguage: explanationLanguage, // Use provider value
        nativeLanguage: nativeLanguage, // Use provider value
      );
    } else {
      // If purpose is null, it implies updating existing profile,
      // which might not need language info update here.
      // Adjust logic if language update is needed for existing profiles too.
      await _authService.updateUser(nickname, null);
    }
  }

  String getCurrentLocale() {
    return LDAppState().getLocale().languageCode;
  }

  fetchCurrentNickname() {
    return _authService.getUser().then((user) {
      if (user != null) {
        return user.nickname;
      }
      return null;
    }).catchError((error) {
      myLog('Error fetching current nickname: $error', LogLevel.error);
      return null;
    });
  }
}
