import 'package:async_button_builder/async_button_builder.dart';
import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/common/LDState.dart';
import 'package:langda/common/ld_field.dart';
import 'package:langda/providers/create_profile_provider.dart'; // Import the provider
import 'package:langda/queries/auth_query.dart';
import 'package:langda/utils/my_logger.dart';
import 'package:provider/provider.dart'; // Import provider package

import '../../../../router_constants.dart';
import '../../../component.dart';
import '../../../mobile/constants.dart';
import 'nickname_setting_logic.dart';

class NicknameSettingPage extends StatefulWidget {
  final bool isFromSignUp;
  const NicknameSettingPage({
    super.key,
    this.isFromSignUp = false,
  });

  @override
  State<NicknameSettingPage> createState() => _NicknameSettingPageState();
}

class _NicknameSettingPageState extends LDState<NicknameSettingPage> {
  final TextEditingController _nicknameController = TextEditingController();
  final TextEditingController _referralCodeController = TextEditingController();
  final GlobalKey<FormState> _nicknameKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _referralCodeKey = GlobalKey<FormState>();
  final FocusNode _nicknameFocusNode = FocusNode();
  final FocusNode _referralCodeFocusNode = FocusNode();
  bool _buttonActivate = false;
  late final NicknameSettingLogic _logic;
  late final CreateProfileProvider
      _profileProvider; // Add provider instance variable

  void switchShowButton(bool show) {
    setState(() {
      _buttonActivate = show;
    });
  }

  @override
  void initState() {
    super.initState();
    pageName = 'Nickname Setting';

    _profileProvider =
        Provider.of<CreateProfileProvider>(context, listen: false);

    _logic = NicknameSettingLogic(profileProvider: _profileProvider);

    _nicknameController.addListener(() {
      if (_nicknameController.text.trim().isNotEmpty &&
          _nicknameKey.currentState?.validate() == true) {
        switchShowButton(true);
      } else {
        switchShowButton(false);
      }
    });

    _logic.fetchCurrentNickname().then((nickname) {
      if (nickname != null) {
        setState(() {
          _nicknameController.text = nickname;
          if (_nicknameController.text.trim().isNotEmpty) {
            switchShowButton(true);
          }
        });
      } else {
        final locale = _logic.getCurrentLocale();
        setState(() {
          _nicknameController.text = _logic.generateNickname(locale);
          if (_nicknameController.text.trim().isNotEmpty) {
            switchShowButton(true);
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    _referralCodeController.dispose(); // Dispose referral code controller too
    _nicknameKey.currentState?.dispose();
    _referralCodeKey.currentState?.dispose(); // Dispose referral code key
    _nicknameFocusNode.dispose();
    _referralCodeFocusNode.dispose(); // Dispose referral code focus node
    super.dispose();
  }

  @override
  Widget buildContent(BuildContext context) {
    return Scaffold(
      appBar: widget.isFromSignUp
          ? null
          : appBarWrapper(
              FlutterI18n.translate(
                context,
                'user.info.nickname.edit.title',
              ),
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  Navigator.of(context).canPop()
                      ? Navigator.of(context).pop()
                      : context.go(RoutePaths.home);
                },
              ),
              null,
            ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20.0),
        child: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              LDField(
                title: FlutterI18n.translate(
                    context, 'user.info.nickname.edit.hint'),
                hintText: FlutterI18n.translate(
                    context, 'user.info.nickname.edit.hint'),
                fieldKey: _nicknameKey,
                focusNode: _nicknameFocusNode,
                controller: _nicknameController,
              ),
              const SizedBox(
                height: 20,
              ),
              widget.isFromSignUp
                  ? Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        LDField(
                          title: FlutterI18n.translate(
                              context, 'registration.referral.title'),
                          hintText: FlutterI18n.translate(
                              context, 'registration.referral.hint'),
                          fieldKey: _referralCodeKey,
                          focusNode: _referralCodeFocusNode,
                          controller: _referralCodeController,
                          tooltipText: FlutterI18n.translate(
                              context, 'registration.referral.description'),
                          capitalize: true,
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                      ],
                    )
                  : const SizedBox(),
              SizedBox(
                height: 70,
                child: AsyncButtonBuilder(
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: _buttonActivate // Use _buttonActivate state
                          ? Theme.of(context).primaryColor
                          : LDColors.lightGrey,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Text(
                          FlutterI18n.translate(
                            context,
                            'button.label.save',
                          ),
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  onPressed: () async {
                    if (_buttonActivate == false) {
                      return;
                    } else {
                      final currentPurpose = _profileProvider.selectedPurpose;

                      final Mutation<void, void> mutation = Mutation(
                          queryFn: (args) async {
                            return await _logic.updateProfile(
                              _nicknameController.text.trim(),
                              purpose: currentPurpose, // Use provider value
                              referralCode:
                                  _referralCodeController.text.isNotEmpty
                                      ? _referralCodeController.text.trim()
                                      : null,
                            );
                          },
                          refetchQueries: [
                            userInfoKey(),
                          ],
                          onSuccess: (_, __) {
                            if (widget.isFromSignUp) {
                              showLDToaster(
                                  context,
                                  FlutterI18n.translate(
                                      context, 'registration.success'),
                                  ToastType.success);
                            } else {
                              showLDToaster(
                                  context,
                                  FlutterI18n.translate(context,
                                      'user.info.nickname.edit.success'),
                                  ToastType.success);
                            }

                            _profileProvider.reset();
                            context.go(RoutePaths.home);
                          },
                          onError: (_, a, b) {
                            myLog(
                                '[Update User Mutation onError] a: $a, b: $b');
                          });

                      mutation.mutate();
                    }
                  },
                  builder: (context, child, callback, _) {
                    return Container(
                      alignment: Alignment.center,
                      child: GestureDetector(
                        onTap: callback,
                        child: child,
                      ),
                    );
                  },
                  loadingWidget: const CircularProgressIndicator(),
                  showSuccess: false,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
