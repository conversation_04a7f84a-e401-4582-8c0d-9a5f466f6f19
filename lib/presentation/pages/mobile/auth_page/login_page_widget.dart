import 'dart:io';

import 'package:async_button_builder/async_button_builder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/common/LDState.dart';
import 'package:langda/presentation/router_constants.dart';
import 'package:langda/presentation/services/app_state_service.dart';
import 'package:langda/presentation/services/auth_service.dart';
import 'package:langda/states/app_state.dart';
import 'package:provider/provider.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../backend/repository/clients/api/api_auth_repository.dart';
import '../../../../utils/supabase_auth_helper.dart';
import '../../component.dart';
import '../constants.dart';
import 'agreement_page_widget.dart';
import 'social_auth_form.dart';

class LoginPageWidget extends StatefulWidget {
  final int index;
  const LoginPageWidget({super.key, this.index = 0});

  @override
  State<LoginPageWidget> createState() => _LoginPageWidgetState();
}

class _LoginPageWidgetState extends LDState<LoginPageWidget> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _emailController = TextEditingController();

  @override
  void initState() {
    pageName = 'Login';
    super.initState();
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  buildContent(BuildContext context) {
    final AuthService authService = context.read<AuthService>();
    final AppStateService appStateService = context.read<AppStateService>();

    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        appBar: isMobile(context)
            ? appBarWrapper(
                FlutterI18n.translate(context, 'login.title'),
                GestureDetector(
                  onTap: () {
                    context.go(RoutePaths.home);
                  },
                  child: Image.asset(
                    'assets/images/icons/chevron-left_1.5.png',
                    width: 48,
                    fit: BoxFit.scaleDown,
                    scale: 1.3,
                  ),
                ),
                const SizedBox(
                  width: 0,
                  height: 0,
                ),
              )
            : null,
        body: Stack(
          children: [
            Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Image.asset(
                        'assets/images/logos/langda.png',
                        width: 50.sp,
                      ),
                    ),
                    const SizedBox(
                      height: 40,
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Form(
                          key: _formKey,
                          child: TextFormField(
                            controller: _emailController,
                            decoration: InputDecoration(
                              hintText: FlutterI18n.translate(
                                context,
                                'login.email.hint',
                              ),
                              hintStyle: TextStyle(
                                color: LDColors.mainGrey,
                                fontSize: 14,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: LDColors.mainGrey,
                                  width: 2,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: Theme.of(context).primaryColor,
                                  width: 2,
                                ),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return FlutterI18n.translate(
                                  context,
                                  'login.email.error.required',
                                );
                              } else if (!value.contains('@')) {
                                return FlutterI18n.translate(
                                  context,
                                  'login.email.error.invalid',
                                );
                              }
                              return null;
                            },
                            keyboardType: TextInputType.emailAddress,
                          ),
                        ),
                        const SizedBox(
                          height: 16,
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 70,
                      child: AsyncButtonBuilder(
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: Theme.of(context).primaryColor,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Text(
                                FlutterI18n.translate(
                                  context,
                                  'login.title',
                                ),
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                        onPressed: () async {
                          if (_emailController.text.isEmpty) {
                            showLDToaster(
                              context,
                              FlutterI18n.translate(
                                context,
                                'login.email.error.required',
                              ),
                              ToastType.error,
                            );
                            return;
                          } else {
                            if (_emailController.text == '<EMAIL>') {
                              await signInWithEmail(
                                _emailController.text,
                                '*dkfs!doels27E',
                              );
                              context.go(RoutePaths.home);
                            } else if (_emailController.text ==
                                '<EMAIL>') {
                              await signInWithEmail(
                                _emailController.text,
                                '!iowl2@#dkslT',
                              );
                            }
                            {
                              ApiAuthRepository apiAuthRepository =
                                  ApiAuthRepository();

                              if (await apiAuthRepository
                                  .validateEmail(_emailController.text)) {
                                try {
                                  await authService.login(LoginType.magicLink,
                                      _emailController.text);
                                } catch (e) {
                                  Sentry.addBreadcrumb(
                                    Breadcrumb(
                                      message:
                                          'Error on sign in with magic link: $e',
                                      timestamp: DateTime.now(),
                                      data: {'email': _emailController.text},
                                    ),
                                  );

                                  if (e.toString().contains('422')) {
                                    showLDToaster(
                                      context,
                                      FlutterI18n.translate(
                                        context,
                                        'login.error.notFound',
                                      ),
                                      ToastType.error,
                                    );
                                    return;
                                  } else if (e
                                      .toString()
                                      .contains('AuthApiException')) {
                                    // Handle AuthApiException specifically
                                    showLDToaster(
                                      context,
                                      'Authentication error. Please try again later.',
                                      ToastType.error,
                                    );

                                    // Try to sign out to clear any corrupted session state
                                    try {
                                      await Supabase.instance.client.auth
                                          .signOut();
                                    } catch (signOutError) {
                                      // Just log the error and continue
                                      Sentry.captureException(
                                        signOutError,
                                        stackTrace: StackTrace.current,
                                      );
                                    }
                                    return;
                                  } else {
                                    showLDToaster(
                                      context,
                                      FlutterI18n.translate(
                                        context,
                                        'login.error.common',
                                      ),
                                      ToastType.error,
                                    );
                                    return;
                                  }
                                }
                                showLDToaster(
                                  context,
                                  FlutterI18n.translate(
                                    context,
                                    'login.email.success',
                                  ),
                                  ToastType.success,
                                );
                                return;
                              } else {
                                showLDToaster(
                                  context,
                                  FlutterI18n.translate(
                                    context,
                                    'registration.email.error.invalid.pattern',
                                  ),
                                  ToastType.error,
                                );
                                return;
                              }
                            }
                          }
                        },
                        builder: (context, child, callback, _) {
                          return Container(
                            child: GestureDetector(
                              onTap: callback,
                              child: child,
                            ),
                          );
                        },
                        loadingWidget: CircularProgressIndicator(),
                        showSuccess: false,
                      ),
                    ),
                    GestureDetector(
                      // sign up button
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => AgreementPageWidget(),
                          ),
                        );
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 20,
                        ),
                        child: Text(
                          FlutterI18n.translate(
                            context,
                            'registration.title',
                          ),
                          style: TextStyle(
                            color: LDColors.foundationLime,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    SocialAuthForm(
                      socialTypes: [
                        SocialType.kakao,
                        SocialType.google,
                        if (Platform.isIOS) SocialType.apple,
                      ],
                      onSignInStart: () {
                        appStateService.setLoading(true);
                      },
                      onSignInEnd: () {
                        appStateService.setLoading(false);
                      },
                      onSignInError: (error, social) {
                        if (error.contains('AuthorizationErrorCode.canceled')) {
                          showLDToaster(
                            context,
                            FlutterI18n.translate(
                              context,
                              'login.error.cancel',
                            ),
                            ToastType.error,
                          );
                        } else {
                          Sentry.addBreadcrumb(
                            Breadcrumb(
                              message: 'Error on social sign in: $error',
                              timestamp: DateTime.now(),
                              data: {'socialType': error},
                            ),
                          );
                          showLDToaster(
                            context,
                            FlutterI18n.translate(
                              context,
                              'login.error.common',
                            ),
                            ToastType.error,
                          );
                        }
                        Sentry.captureException(
                          error,
                          stackTrace: StackTrace.current,
                        );
                        appStateService.setLoading(false);
                      },
                      supportLanguage: LDAppState().getLocale().languageCode,
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // * privacy policy
                        GestureDetector(
                          onTap: () {
                            showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: Text(
                                  FlutterI18n.translate(
                                    context,
                                    'app.privacyPolicy.title',
                                  ),
                                ),
                                content: Container(
                                  height: 300,
                                  child: SingleChildScrollView(
                                    child: Text(
                                      FlutterI18n.translate(
                                        context,
                                        'app.privacyPolicy.content',
                                      ),
                                    ),
                                  ),
                                ),
                                actions: [
                                  LDButton(
                                    label: FlutterI18n.translate(
                                      context,
                                      'button.label.confirm',
                                    ),
                                    onPressed: () {
                                      setState(() {});
                                      Navigator.pop(context);
                                    },
                                  )
                                ],
                              ),
                            );
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 13),
                            child: Text(
                              FlutterI18n.translate(
                                  context, 'app.privacyPolicy.title'),
                              style: TextStyle(
                                color: LDColors.mainGrey,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),

                        // * terms of service
                        GestureDetector(
                          onTap: () {
                            showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: Text(
                                  FlutterI18n.translate(
                                    context,
                                    'app.termsOfService.title',
                                  ),
                                ),
                                content: Container(
                                  height: 300,
                                  child: SingleChildScrollView(
                                    child: Text(
                                      FlutterI18n.translate(
                                        context,
                                        'app.termsOfService.content',
                                      ),
                                    ),
                                  ),
                                ),
                                actions: [
                                  LDButton(
                                    label: FlutterI18n.translate(
                                      context,
                                      'button.label.confirm',
                                    ),
                                    onPressed: () {
                                      setState(() {});
                                      Navigator.pop(context);
                                    },
                                  )
                                ],
                              ),
                            );
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 13),
                            child: Text(
                              FlutterI18n.translate(
                                  context, 'app.termsOfService.title'),
                              style: TextStyle(
                                color: LDColors.mainGrey,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
