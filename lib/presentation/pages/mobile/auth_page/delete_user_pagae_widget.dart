import 'package:async_button_builder/async_button_builder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../common/LDState.dart';
import '../../../../common/custom_app_bar.dart';
import '../../../../queries/auth_query.dart';
import '../../../../states/app_state.dart';
import '../../component.dart';
import '../constants.dart';
import '../web_view_widget.dart';

class DeleteUserPagaeWidget extends StatefulWidget {
  const DeleteUserPagaeWidget({super.key});

  @override
  State<DeleteUserPagaeWidget> createState() => _DeleteUserPagaeWidgetState();
}

class _DeleteUserPagaeWidgetState extends LDState<DeleteUserPagaeWidget> {
  bool _agree = false;
  @override
  void initState() {
    pageName = 'Delete User';
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget buildContent(BuildContext context) {
    return Scaffold(
      appBar: customAppBar(
        title: FlutterI18n.translate(context, 'delete.title'),
        leadingWidget: Image.asset(
          'assets/images/icons/chevron-left_1.5.png',
          width: 48,
          fit: BoxFit.scaleDown,
          scale: 1.3,
        ),
        leadingLabel: 'Back',
        leadingOnTap: () {
          context.canPop() ? context.pop() : null;
        },
      ),
      body: SafeArea(
        child: Stack(
          alignment: Alignment.center,
          children: [
            Padding(
              padding: EdgeInsets.all(defaultPadding),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Langda',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    FlutterI18n.translate(context, 'delete.title'),
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    FlutterI18n.translate(context, 'delete.warning'),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  if (showSubscription) const SizedBox(height: 10),
                  if (showSubscription)
                    Text(
                      FlutterI18n.translate(context, 'delete.subscribe.cancel'),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                        color: LDColors.mainRed,
                      ),
                    ),
                  if (showSubscription)
                    Text(
                      FlutterI18n.translate(
                          context, 'delete.subscribe.description'),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  if (showSubscription)
                    Semantics(
                      label: FlutterI18n.translate(
                          context, 'delete.subscribe.navigate'),
                      button: true,
                      child: GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => WebViewPageWidget(
                                url: LDAppState().getLocale().languageCode ==
                                        'ko'
                                    ? 'https://chlorinated-carp-1da.notion.site/131cac1fd92f80929c44d28d9f3563f2?pvs=4' // ko
                                    : 'https://chlorinated-carp-1da.notion.site/131cac1fd92f80929c44d28d9f3563f2?pvs=4', // en
                              ),
                            ),
                          );
                        },
                        child: plainButtonText(
                          context,
                          text: FlutterI18n.translate(
                              context, 'delete.subscribe.navigate'),
                        ),
                      ),
                    ),
                  if (showSubscription)
                    Text(
                      FlutterI18n.translate(context, 'delete.subscribe.info'),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: LDColors.mainGrey,
                      ),
                    ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Checkbox(
                          value: _agree,
                          onChanged: (value) {
                            setState(() {
                              _agree = value ?? false;
                            });
                          }),
                      Text(
                        FlutterI18n.translate(
                            context, 'delete.agreement.checkbox'),
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Text(
                    FlutterI18n.translate(context, 'delete.description'),
                    style: TextStyle(
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  SizedBox(
                    height: 70,
                    width: double.infinity,
                    child: AsyncButtonBuilder(
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Theme.of(context).primaryColor,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Text(
                              FlutterI18n.translate(
                                context,
                                'delete.title',
                              ),
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      onPressed: () async {
                        if (!_agree) {
                          showLDToaster(
                              context,
                              FlutterI18n.translate(
                                context,
                                'delete.agreement.error',
                              ),
                              ToastType.error);
                        } else {
                          showLDDialog(
                              context,
                              FlutterI18n.translate(
                                context,
                                'delete.dialog',
                              ), onConfirmed: () async {
                            deleteUserMutation().mutate();
                            await Future.delayed(Duration(seconds: 1));
                            // AppStateNotifier.instance.setHomePageIndex(0);
                            context.go('/home');
                          }, onCanceled: () {});
                        }
                      },
                      builder: (context, child, callback, _) {
                        return Container(
                          child: GestureDetector(
                            onTap: callback,
                            child: child,
                          ),
                        );
                      },
                      loadingWidget: CircularProgressIndicator(),
                      showSuccess: false,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Padding plainButtonText(
    BuildContext context, {
    required String text,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: Adaptive.sp(14.5),
      ),
      child: Row(
        children: [
          Text(
            text,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: LDColors.mainGrey,
            ),
          ),
          SizedBox(
            width: 10,
          ),
          Icon(
            FontAwesomeIcons.chevronRight,
            size: 13,
            color: LDColors.mainGrey,
          ),
        ],
      ),
    );
  }
}
