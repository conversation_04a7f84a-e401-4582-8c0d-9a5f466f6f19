import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:langda/presentation/pages/component.dart';
import 'package:langda/states/app_state.dart';
import 'package:locale_names/locale_names.dart';

import '../../../../api/models/explanation_language_preference.dart';

class LanguageOptionPageWidget {
  final String? isoCode;
  final Locale? locale;

  // Removed const from constructor as it prevents usage in non-const list
  LanguageOptionPageWidget({required this.isoCode})
      : locale = isoCode != null ? Locale(isoCode) : null;

  String get id => isoCode ?? 'other';

  String get nativeName => locale?.nativeDisplayLanguage ?? 'Other';

  // Getter to get the display name in the current system UI language
  String get currentLocaleName {
    final systemLocale = LDAppState().getLocale();
    return locale?.displayLanguageIn(systemLocale) ?? "Other";
  }
}

class LanguageSelectionPage extends StatefulWidget {
  final Function(String?, String?)? onTapNext;
  const LanguageSelectionPage({super.key, this.onTapNext});

  @override
  State<LanguageSelectionPage> createState() => _LanguageSelectionPageState();
}

class _LanguageSelectionPageState extends State<LanguageSelectionPage> {
  String? _selectedLanguageId;

  // Made nullable and initialized to null
  ExplanationLanguagePreference? _explanationPreference = null;

  // Removed const from list initialization
  final List<LanguageOptionPageWidget> _languages = [
    LanguageOptionPageWidget(isoCode: "ko"),
    LanguageOptionPageWidget(isoCode: "ja"),
    LanguageOptionPageWidget(isoCode: "en"),
    LanguageOptionPageWidget(isoCode: null), // Represents "Other"
  ];

  void _handleContinue() {
    if (_selectedLanguageId == null) return;

    final selectedLang =
        _languages.firstWhere((l) => l.id == _selectedLanguageId);

    print({
      'nativeLanguageIsoCode': selectedLang.isoCode,
      // Handle null case for printing
      'explanationPreference': _explanationPreference?.json,
    });

    widget.onTapNext?.call(
      _explanationPreference?.json,
      selectedLang.isoCode,
    );
  }

  String _getSelectedLanguageDisplay() {
    if (_selectedLanguageId == null) return "";
    try {
      return _languages
          .firstWhere((l) => l.id == _selectedLanguageId)
          .currentLocaleName; // Use the getter
    } catch (e) {
      return "";
    }
  }

  bool get _showEnglishOption {
    // Corrected "english" to "en"
    return _selectedLanguageId != null &&
        _selectedLanguageId != "en" &&
        _selectedLanguageId != "other";
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);
    final ColorScheme colorScheme = theme.colorScheme;
    final TextTheme textTheme = theme.textTheme;

    final Color mutedForegroundColor = Colors.grey.shade600;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 448),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 32.0),
                  Text(
                    FlutterI18n.translate(context, "languageSelection.title"),
                    style: textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      letterSpacing: -0.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8.0),
                  Text(
                    FlutterI18n.translate(
                        context, "languageSelection.description"),
                    style: textTheme.bodyMedium
                        ?.copyWith(color: mutedForegroundColor),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24.0),
                  ..._languages
                      .map((language) => _buildLanguageCard(
                          language, mutedForegroundColor, colorScheme))
                      .toList(),
                  AnimatedOpacity(
                    opacity: _showEnglishOption ? 1.0 : 0.0,
                    duration: const Duration(milliseconds: 300),
                    child: Visibility(
                      visible: _showEnglishOption,
                      maintainState: true,
                      maintainAnimation: true,
                      maintainSize: true,
                      child: Padding(
                        padding: const EdgeInsets.only(top: 16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              FlutterI18n.translate(context,
                                  "languageSelection.explanation.title"),
                              style: textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.w500),
                            ),
                            const SizedBox(height: 4.0),
                            Text(
                              FlutterI18n.translate(context,
                                  "languageSelection.explanation.description",
                                  translationParams: {
                                    // Updated call
                                    "language": _getSelectedLanguageDisplay()
                                  }),
                              style: textTheme.bodySmall
                                  ?.copyWith(color: mutedForegroundColor),
                            ),
                            const SizedBox(height: 16.0),
                            _buildExplanationToggleButtons(
                                colorScheme, textTheme),
                            const SizedBox(height: 8.0),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const Spacer(),
                  Padding(
                    padding: const EdgeInsets.only(top: 20.0),
                    child: LDButton(
                      label: FlutterI18n.translate(
                          context, "languageSelection.continueButton"),
                      onPressed: _handleContinue,
                      isActivated: _selectedLanguageId != null,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageCard(LanguageOptionPageWidget language, Color mutedColor,
      ColorScheme colorScheme) {
    final bool isSelected = _selectedLanguageId == language.id;

    return Card(
      elevation: 0,
      margin: const EdgeInsets.only(bottom: 12.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
        side: BorderSide(
          color: isSelected
              ? colorScheme.primary
              : colorScheme.outline.withValues(alpha: 0.3),
          width: isSelected ? 1.5 : 1.0,
        ),
      ),
      color: isSelected
          ? colorScheme.primary.withValues(alpha: 0.05)
          : colorScheme.surface,
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedLanguageId = language.id;

            if (language.id == 'en' || language.id == 'other') {
              _explanationPreference = null;
            } else {
              _explanationPreference =
                  ExplanationLanguagePreference.nativeLanguage;
            }
          });
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 14.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: RichText(
                  text: TextSpan(
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: colorScheme.onSurface),
                      children: [
                        TextSpan(text: language.nativeName),
                      ]),
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.check,
                  color: colorScheme.primary,
                  size: 20.0,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExplanationToggleButtons(
      ColorScheme colorScheme, TextTheme textTheme) {
    final double buttonPadding = 12.0;
    final BorderRadius borderRadius = BorderRadius.circular(8.0);

    // Handle null case for selection state
    final isNativeSelected =
        _explanationPreference == ExplanationLanguagePreference.nativeLanguage;
    final isEnglishSelected =
        _explanationPreference == ExplanationLanguagePreference.diaryLanguage;

    // Find the selected language option to get its native name
    final selectedLanguageOption = _selectedLanguageId != null
        ? _languages.firstWhere((l) => l.id == _selectedLanguageId,
            orElse: () => LanguageOptionPageWidget(
                isoCode:
                    null)) // Fallback, though should not happen when visible
        : LanguageOptionPageWidget(isoCode: null); // Fallback

    return Container(
      width: double.infinity,
      child: ToggleButtons(
        isSelected: [isNativeSelected, isEnglishSelected],
        onPressed: (int index) {
          // Can only press if _showEnglishOption is true, so _explanationPreference won't be null here
          setState(() {
            _explanationPreference = (index == 0)
                ? ExplanationLanguagePreference.nativeLanguage
                : ExplanationLanguagePreference.diaryLanguage;
          });
        },
        borderRadius: borderRadius,
        constraints: BoxConstraints(
            minHeight: 40.0,
            minWidth: (MediaQuery.of(context).size.width - 48 - 4) / 2),
        selectedColor: colorScheme.onSurface.withAlpha(190),
        color: colorScheme.onSurface,
        fillColor: colorScheme.primary.withAlpha(30),
        borderColor: colorScheme.outline.withAlpha(127),
        selectedBorderColor: colorScheme.primary,
        borderWidth: 1.0,
        children: <Widget>[
          Padding(
            padding: EdgeInsets.symmetric(horizontal: buttonPadding),
            // Use nativeName of the selected language
            child: Text(selectedLanguageOption.nativeName),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: buttonPadding),
            // Use translation key for "English" for consistency
            child: Text("English"),
          ),
        ],
      ),
    );
  }
}
