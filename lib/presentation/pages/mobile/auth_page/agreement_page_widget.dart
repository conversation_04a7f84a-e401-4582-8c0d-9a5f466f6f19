import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/common/LDState.dart';
import 'package:langda/presentation/router_constants.dart';

import '../../component.dart';
import '../constants.dart';

class AgreementPageWidget extends StatefulWidget {
  AgreementPageWidget({
    super.key,
  });

  @override
  State<AgreementPageWidget> createState() => _AgreementPageWidgetState();
}

class _AgreementPageWidgetState extends LDState<AgreementPageWidget> {
  bool _termsChecked = false;
  bool _privacyChecked = false;

  bool _isAllChecked() {
    return _termsChecked && _privacyChecked;
  }

  @override
  void initState() {
    pageName = 'Agrement';
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget buildContent(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: SafeArea(
        child: Stack(
          alignment: Alignment.center,
          children: [
            Align(
              alignment: Alignment.lerp(
                Alignment.topCenter,
                Alignment.bottomCenter,
                0.3,
              )!,
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Langda ${FlutterI18n.translate(context, 'registration.title')}',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    Text(
                      FlutterI18n.translate(
                          context, 'registration.agreement.description'),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    SizedBox(
                      height: 60,
                    ),
                    tile(
                      true,
                      _isAllChecked(),
                      () {
                        setState(() {
                          if (_termsChecked == false &&
                              _privacyChecked == false) {
                            _termsChecked = true;
                            _privacyChecked = true;
                          } else {
                            if (_termsChecked == true &&
                                _privacyChecked == false) {
                              _privacyChecked = true;
                            } else if (_termsChecked == false &&
                                _privacyChecked == true) {
                              _termsChecked = true;
                            } else {
                              _termsChecked = false;
                              _privacyChecked = false;
                            }
                          }
                        });
                      },
                      title: FlutterI18n.translate(
                          context, 'registration.agreement.all.title'),
                    ),
                    SizedBox(
                      height: 12,
                    ),
                    tile(
                      false,
                      _termsChecked,
                      () {
                        setState(() {
                          _termsChecked = !_termsChecked;
                        });
                      },
                      title:
                          '[${FlutterI18n.translate(context, 'registration.agreement.required.title')}] ${FlutterI18n.translate(context, 'app.termsOfService.title')}',
                      type: 'termsOfService',
                    ),
                    tile(
                      false,
                      _privacyChecked,
                      () {
                        setState(() {
                          _privacyChecked = !_privacyChecked;
                        });
                      },
                      title:
                          '[${FlutterI18n.translate(context, 'registration.agreement.required.title')}] ${FlutterI18n.translate(context, 'app.privacyPolicy.title')}',
                      type: 'privacyPolicy',
                    ),
                  ],
                ),
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                padding: const EdgeInsets.only(
                  left: 20,
                  right: 20,
                  bottom: 20,
                ),
                child: LDButton(
                  label: FlutterI18n.translate(context, 'button.label.next'),
                  onPressed: () async {
                    if (_isAllChecked()) {
                      context.push(RoutePaths.signupPath());
                    } else {
                      showLDDialog(
                        context,
                        FlutterI18n.translate(
                            context, 'registration.agreement.error.required'),
                        onConfirmed: () {},
                        onCanceled: null,
                      );
                    }
                  },
                  isActivated: _isAllChecked(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Row tile(bool isFirst, bool isSelected, VoidCallback onTap,
      {String? title, String? type}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Semantics(
          button: true,
          label: isFirst
              ? '${FlutterI18n.translate(context, 'registration.agreement.all.checkbox')}'
              : type != null
                  ? '${FlutterI18n.translate(context, 'app.${type}.title')} ${FlutterI18n.translate(context, 'registration.agreement.title')}'
                  : '',
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: GestureDetector(
              onTap: onTap,
              child: Container(
                padding: EdgeInsets.all(2),
                alignment: Alignment.center,
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  border: isFirst
                      ? Border.all(
                          color: _isAllChecked() || isSelected
                              ? LDColors.foundationLime
                              : LDColors.mainGrey,
                          width: 1.5,
                        )
                      : null,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(
                    Icons.check,
                    color: _isAllChecked()
                        ? LDColors.foundationLime
                        : isSelected
                            ? LDColors.foundationLime
                            : LDColors.mainGrey,
                    size: isFirst ? 16 : 24,
                  ),
                ),
              ),
            ),
          ),
        ),
        Flexible(
          child: Text(
            title ?? '',
            style: TextStyle(
              fontSize: isFirst ? 16 : 14,
              fontWeight: isFirst ? FontWeight.w600 : FontWeight.w400,
            ),
          ),
        ),
        isFirst
            ? SizedBox()
            : IconButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: Text(
                        FlutterI18n.translate(
                          context,
                          'app.${type}.title',
                        ),
                      ),
                      content: Container(
                        height: 300,
                        child: SingleChildScrollView(
                          child: Text(
                            FlutterI18n.translate(
                              context,
                              'app.${type}.content',
                            ),
                          ),
                        ),
                      ),
                      actions: [
                        LDButton(
                          label: FlutterI18n.translate(
                              context, 'button.label.confirm'),
                          onPressed: () {
                            if (type == 'termsOfService') {
                              _termsChecked = true;
                            } else {
                              _privacyChecked = true;
                            }
                            setState(() {});
                            Navigator.pop(context);
                          },
                        )
                      ],
                    ),
                  );
                },
                icon: Semantics(
                  label: '${FlutterI18n.translate(
                    context,
                    'app.${type}.title',
                  )} ${FlutterI18n.translate(
                    context,
                    'button.label.show',
                  )}',
                  button: true,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Icon(
                      FontAwesomeIcons.chevronRight,
                      size: 25,
                    ),
                  ),
                ),
              ),
      ],
    );
  }
}
