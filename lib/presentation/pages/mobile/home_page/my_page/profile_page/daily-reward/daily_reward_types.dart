// Translated from daily-reward-types.ts to Dart

// Represents a single reward day.
class RewardDay {
  final String date;
  final double rewardAmount;
  final bool isBonus;
  final bool isClaimed;
  final int dayInStreak;

  RewardDay({
    required this.date,
    required this.rewardAmount,
    required this.isBonus,
    required this.isClaimed,
    required this.dayInStreak,
  });
  
  // Optionally add factory constructors or toJson/fromJson methods if needed.
}

// Represents the API response for rewards.
class RewardsApiResponse {
  final bool success;
  final String userId;
  final String startDate;
  final String endDate;
  final List<RewardDay> rewards;

  RewardsApiResponse({
    required this.success,
    required this.userId,
    required this.startDate,
    required this.endDate,
    required this.rewards,
  });
}

// Represents a next reward in the view model.
class NextReward {
  final int day;
  final double reward;
  final bool isBigReward;
  final String date;
  final bool isClaimed;
  final String dayOfWeek; // Added day of week

  NextReward({
    required this.day,
    required this.reward,
    required this.isBigReward,
    required this.date,
    required this.isClaimed,
    required this.dayOfWeek,
  });
}

// Represents the view model for daily rewards.
class DailyRewardViewModel {
  final int currentStreak;
  final List<NextReward> nextRewards;

  DailyRewardViewModel({
    required this.currentStreak,
    required this.nextRewards,
  });
}
