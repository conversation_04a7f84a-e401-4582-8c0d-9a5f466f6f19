// Translated from daily-reward.tsx (React) to Flutter code

import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:glowy_borders/glowy_borders.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/presentation/pages/mobile/constants.dart';
import 'package:langda/presentation/pages/mobile/home_page/my_page/profile_page/daily-reward/daily_reward_types.dart';
import 'package:langda/presentation/router_constants.dart';
import 'package:langda/providers/process_diary_provider.dart';
import 'package:provider/provider.dart';

import '../../../../../../../queries/diary_query.dart';
import '../../../diary_page/component/diary_write_button.dart';

class DailyReward extends StatefulWidget {
  final DailyRewardViewModel viewModel;
  const DailyReward({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  @override
  _DailyRewardState createState() => _DailyRewardState();
}

class _DailyRewardState extends State<DailyReward>
    with SingleTickerProviderStateMixin {
  bool showAnimation = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final DiaryProcessingProvider diaryProcessingProvider =
        context.read<DiaryProcessingProvider>();
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 16,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Card Header
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Title and currency info
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      FlutterI18n.translate(
                        context,
                        'diary.reward.title',
                      ),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: LDColors.mainGrey,
                      ),
                    ),
                    Row(
                      children: [
                        Icon(
                          FontAwesomeIcons.fireFlameSimple,
                          size: 16,
                          color: Colors.orange,
                        ),
                        SizedBox(width: 4),
                        Text(
                          '${FlutterI18n.plural(
                            context,
                            'diary.reward.streak.all',
                            widget.viewModel.currentStreak,
                          )}!',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: LDColors.darkHoverGrey,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // Grid view for rewards
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Stack(
              children: [
                GridView.count(
                  padding: EdgeInsets.zero,
                  crossAxisCount: 5,
                  shrinkWrap: true,
                  clipBehavior: Clip.none,
                  physics: NeverScrollableScrollPhysics(),
                  crossAxisSpacing: 4,
                  children: List.generate(widget.viewModel.nextRewards.length,
                      (index) {
                    final reward = widget.viewModel.nextRewards[index];
                    // NextReward(
                    //   day: index + 1,
                    //   reward: 0,
                    //   isBigReward: index == 4,
                    //   date: '',
                    //   isClaimed: false,
                    //   dayOfWeek: '',
                    // );

                    Color backgroundColor = reward.isBigReward
                        ? Colors.amber.shade100
                        : LDColors.lightGrey;
                    Widget rewardTile = Container(
                      clipBehavior: Clip.none,
                      decoration: BoxDecoration(
                        color: backgroundColor,
                        borderRadius: BorderRadius.circular(8),
                        border: index == 0
                            ? Border.all(
                                color: Theme.of(context).primaryColor,
                                width: 2,
                              )
                            : null,
                      ),
                      child: Stack(
                        clipBehavior: Clip.none,
                        children: [
                          // Content for reward tile
                          Padding(
                            padding: const EdgeInsets.all(4.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  reward.dayOfWeek,
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w700,
                                    color: LDColors.mainGrey,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.diamond_outlined,
                                      size: 14,
                                      color: reward.isBigReward
                                          ? Colors.amber
                                          : Colors.black54,
                                    ),
                                    const SizedBox(width: 2),
                                    Text(
                                      "${reward.reward.toInt()}",
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          // Check icon if the reward is claimed
                          if (reward.isClaimed)
                            Positioned(
                              top: -3,
                              right: -3,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.green,
                                  shape: BoxShape.circle,
                                ),
                                padding: EdgeInsets.all(2),
                                child: Icon(
                                  Icons.check,
                                  size: 10,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          // Animated pulsating circle for big reward that is not claimed
                          if (reward.isBigReward && !reward.isClaimed)
                            Positioned(
                              top: -2,
                              right: -2,
                              child: showAnimation
                                  ? PulsatingCircle()
                                  : SizedBox.shrink(),
                            ),
                        ],
                      ),
                    );
                    if (reward.isClaimed) {
                      rewardTile = Opacity(
                        opacity: 0.7,
                        child: rewardTile,
                      );
                    }
                    return rewardTile;
                  }),
                ),
                // if (viewModel == null)
                //   Center(
                //     child: Padding(
                //       padding: const EdgeInsets.all(8.0),
                //       child: CircularProgressIndicator(),
                //     ),
                //   ),
              ],
            ),
          ),

          // Complete lesson button
          diaryProcessingProvider.status == ProcessingStatus.processing
              ? Container()
              : QueryBuilder(
                  query: diaryWriteButtonModelQuery(DateTime.now()),
                  builder: (context, state) {
                    final modelData = state.data;
                    return Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 8,
                      ),
                      child: AnimatedGradientBorder(
                        stretchAlongAxis: true,
                        stretchAxis: Axis.horizontal,
                        glowSize: 1,
                        gradientColors: [
                          modelData != null && modelData.shouldShowSubmitButton
                              ? LDColors.mainBlue
                              : Colors.transparent,
                          modelData != null && modelData.shouldShowSubmitButton
                              ? Colors.blue[100]!
                              : Colors.transparent,
                          modelData != null && modelData.shouldShowSubmitButton
                              ? Colors.blue[200]!
                              : Colors.transparent,
                        ],
                        borderRadius: BorderRadius.circular(10),
                        borderSize: 2,
                        child: NoteWriteButtonWidget(
                          key: Key('dailyRewardButton'),
                          selectedDate: DateTime.now(),
                          onTapEnabled: () {
                            context.push(
                              RoutePaths.diaryCreatePath(),
                            );
                          },
                          onTapNeedSubscription: () {},
                          onTapLoggedOut: () {},
                        ),
                      ),
                    );
                  }),
        ],
      ),
    );
  }
}

// A widget that provides a pulsating (scaling) circle animation.
class PulsatingCircle extends StatefulWidget {
  const PulsatingCircle({Key? key}) : super(key: key);

  @override
  _PulsatingCircleState createState() => _PulsatingCircleState();
}

class _PulsatingCircleState extends State<PulsatingCircle>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: Duration(seconds: 2),
    )..repeat(reverse: true);
    _animation = Tween<double>(begin: 1.0, end: 1.2).animate(_controller);
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _animation,
      child: Container(
        width: 12,
        height: 12,
        decoration: BoxDecoration(
          color: Colors.amber,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
