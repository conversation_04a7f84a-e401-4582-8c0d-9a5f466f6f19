// Translated from daily-reward-service.ts to Dart

import 'package:langda/api/models/streak_calendar_response.dart';

import 'daily_reward_types.dart';

// Mock API fetch function (synchronous as requested)
// RewardsApiResponse fetchRewardsData() {
//   return RewardsApiResponse(
//     success: true,
//     userId: "user-uuid",
//     startDate: "2024-07-28",
//     endDate: "2024-08-27",
//     rewards: [
//       RewardDay(
//           date: "2024-07-28",
//           rewardAmount: 50,
//           isBonus: false,
//           isClaimed: true,
//           dayInStreak: 1),
//       RewardDay(
//           date: "2024-07-29",
//           rewardAmount: 50,
//           isBonus: false,
//           isClaimed: false,
//           dayInStreak: 2),
//       RewardDay(
//           date: "2024-07-30",
//           rewardAmount: 50,
//           isBonus: false,
//           isClaimed: false,
//           dayInStreak: 3),
//       RewardDay(
//           date: "2024-07-31",
//           rewardAmount: 50,
//           isBonus: false,
//           isClaimed: false,
//           dayInStreak: 4),
//       RewardDay(
//           date: "2024-08-01",
//           rewardAmount: 250,
//           isBonus: true,
//           isClaimed: false,
//           dayInStreak: 5),
//       RewardDay(
//           date: "2024-08-02",
//           rewardAmount: 50,
//           isBonus: false,
//           isClaimed: false,
//           dayInStreak: 6),
//       RewardDay(
//           date: "2024-08-03",
//           rewardAmount: 50,
//           isBonus: false,
//           isClaimed: false,
//           dayInStreak: 7),
//       RewardDay(
//           date: "2024-08-04",
//           rewardAmount: 50,
//           isBonus: false,
//           isClaimed: false,
//           dayInStreak: 8),
//       RewardDay(
//           date: "2024-08-05",
//           rewardAmount: 50,
//           isBonus: false,
//           isClaimed: false,
//           dayInStreak: 9),
//       RewardDay(
//           date: "2024-08-06",
//           rewardAmount: 250,
//           isBonus: true,
//           isClaimed: false,
//           dayInStreak: 10),
//     ],
//   );
// }

// Helper function to get day of week abbreviation
String getDayOfWeek(String dateString) {
  DateTime date = DateTime.parse(dateString);
  switch (date.weekday) {
    case DateTime.monday:
      return "Mo";
    case DateTime.tuesday:
      return "Tu";
    case DateTime.wednesday:
      return "We";
    case DateTime.thursday:
      return "Th";
    case DateTime.friday:
      return "Fr";
    case DateTime.saturday:
      return "Sa";
    case DateTime.sunday:
      return "Su";
    default:
      return "";
  }
}

// Process API data into view model
DailyRewardViewModel createRewardsViewModel(StreakCalendarResponse apiData) {
  // Sort rewards by date
  List<RewardDay> sortedRewards = apiData.rewards
      .map((day) => RewardDay(
            date: day.date,
            rewardAmount: double.parse(day.rewardAmount.toString()),
            isBonus: day.isBonus,
            isClaimed: day.isClaimed,
            dayInStreak: day.dayInStreak,
          ))
      .toList();
  sortedRewards
      .sort((a, b) => DateTime.parse(a.date).compareTo(DateTime.parse(b.date)));

  // Find the first unclaimed day (this will be our "today" for demo purposes)
  // int firstUnclaimedIndex = sortedRewards.indexWhere((day) => !day.isClaimed);
  // if (firstUnclaimedIndex == -1) {
  //   firstUnclaimedIndex = 0;
  // }
  bool isTodayClaimed = sortedRewards[0].isClaimed;
  RewardDay currentStreakDay = sortedRewards[0];
  int currentStreak = currentStreakDay.dayInStreak - 1;
  if (isTodayClaimed) {
    currentStreak = currentStreakDay.dayInStreak + 1;
  }

  // Get only today and future rewards (7 days total)
  int endIndex = 5;
  if (endIndex > sortedRewards.length) {
    endIndex = sortedRewards.length;
  }
  List<RewardDay> upcomingRewards = sortedRewards.sublist(0, endIndex);

  // Format next rewards for the view
  List<NextReward> nextRewards = upcomingRewards.map((day) {
    return NextReward(
      day: day.dayInStreak,
      reward: day.rewardAmount,
      isBigReward: day.isBonus,
      date: day.date,
      isClaimed: day.isClaimed,
      dayOfWeek: getDayOfWeek(day.date),
    );
  }).toList();

  return DailyRewardViewModel(
    currentStreak: currentStreak,
    nextRewards: nextRewards,
  );
}
