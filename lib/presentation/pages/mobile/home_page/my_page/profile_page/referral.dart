import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:share_plus/share_plus.dart';

import '../../../../../../states/app_state.dart';
import '../../../../component.dart';
import '../../../constants.dart';

class Referral extends StatelessWidget {
  final String code;
  final TextEditingController _controller;

  Referral({
    super.key,
    required this.code,
  }) : _controller = TextEditingController(text: code);

  @override
  Widget build(BuildContext context) {
    var _subtitleRowKo = Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.max,
      children: [
        Text(
          FlutterI18n.translate(
            context,
            'registration.referral.content.subtitle.0',
          ),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: LDColors.mainGrey,
          ),
        ),
        Text(
          FlutterI18n.translate(
            context,
            'registration.referral.content.subtitle.1',
          ),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: LDColors.mainGrey,
          ),
        ),
        gemIcon,
        Text(
          FlutterI18n.translate(
            context,
            'registration.referral.content.subtitle.2',
          ),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: LDColors.mainGrey,
          ),
        ),
      ],
    );
    var _subtitleRowEn = Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.max,
      children: [
        Text(
          FlutterI18n.translate(
            context,
            'registration.referral.content.subtitle.0',
          ),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: LDColors.mainGrey,
          ),
        ),
        gemIcon,
        Text(
          FlutterI18n.translate(
            context,
            'registration.referral.content.subtitle.1',
          ),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: LDColors.mainGrey,
          ),
        ),
        gemIcon,
      ],
    );
    var _descriptionRowKo = Row(
      children: [
        Text(
          FlutterI18n.translate(
            context,
            'registration.referral.content.description.list.2.description.0',
          ),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: LDColors.mainGrey,
          ),
        ),
        gemIcon,
        Text(
          FlutterI18n.translate(
            context,
            'registration.referral.content.description.list.2.description.1',
          ),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: LDColors.mainGrey,
          ),
        ),
        gemIcon,
        Text(
          FlutterI18n.translate(
            context,
            'registration.referral.content.description.list.2.description.2',
          ),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: LDColors.mainGrey,
          ),
        ),
      ],
    );
    var _descriptionRowEn = Row(
      children: [
        Text(
          FlutterI18n.translate(
            context,
            'registration.referral.content.description.list.2.description.0',
          ),
          style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w500,
            color: LDColors.mainGrey,
          ),
        ),
        gemIcon,
        Text(
          FlutterI18n.translate(
            context,
            'registration.referral.content.description.list.2.description.1',
          ),
          style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w500,
            color: LDColors.mainGrey,
          ),
        ),
        gemIcon,
      ],
    );

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(Adaptive.sp(20)),
          decoration: BoxDecoration(
            color: LDColors.mainLime,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(
                Adaptive.sp(20),
              ),
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                FlutterI18n.translate(
                  context,
                  'registration.referral.content.title',
                ),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: LDColors.mainGrey,
                ),
              ),
              LDAppState().getLocale() == 'ko' ? _subtitleRowKo : _subtitleRowEn
            ],
          ),
        ),
        const SizedBox(
          height: 20,
        ),
        Text(
          FlutterI18n.translate(
            context,
            'registration.referral.content.code',
          ),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: LDColors.mainGrey,
          ),
        ),
        const SizedBox(
          height: 10,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            Container(
              width: 120,
              decoration: BoxDecoration(
                color: LDColors.mainWhiteGrey,
                borderRadius: BorderRadius.circular(10),
              ),
              child: TextField(
                controller: _controller,
                readOnly: true,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.only(
                    left: Adaptive.w(5),
                    right: Adaptive.w(5),
                  ),
                ),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: LDColors.mainGrey,
                  fontFamily: 'Inconsolata',
                ),
                textAlign: TextAlign.center,
              ),
            ),
            IconButton(
              icon: Icon(
                FontAwesomeIcons.copy,
                color: LDColors.mainGrey,
                size: 20,
              ),
              onPressed: () {
                // Copy the referral code to clipboard
                Clipboard.setData(
                  ClipboardData(text: _controller.text),
                );
                // Show a toast message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: LDtoast(
                        FlutterI18n.translate(
                          context,
                          'registration.referral.content.share.copied',
                        ),
                        ToastType.info,
                        maxWidth: MediaQuery.of(context).size.width),
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    duration: const Duration(seconds: 2),
                  ),
                );
              },
            ),
          ],
        ),
        const SizedBox(
          height: 20,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 20,
          ),
          child: LDButton(
            label: FlutterI18n.translate(
              context,
              'registration.referral.content.share.button',
            ),
            onPressed: () {
              Share.share(
                '[Langda]\n영어로 쓴 내 일기, 원어민도 놀라는👀\n전문적이고 심화 된 첨삭📝 신개념 앱 Langda!\n친구와 함께 경험해보세요!\n${FlutterI18n.translate(
                  context,
                  'registration.referral.content.code',
                )}: ${_controller.text} \n\n${FlutterI18n.translate(
                  context,
                  'https://link.oibori.com/ApLM ',
                )}',
                subject: FlutterI18n.translate(
                  context,
                  'registration.referral.content.title',
                ),
              );
            },
          ),
        ),
        const SizedBox(
          height: 10,
        ),
        Divider(
          indent: Adaptive.w(5),
          endIndent: Adaptive.w(5),
          color: Colors.grey[300],
          height: 40,
          thickness: 1,
        ),
        const SizedBox(
          height: 10,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 30,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                FlutterI18n.translate(
                  context,
                  'registration.referral.content.description.title',
                ),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: LDColors.mainGrey,
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Container(
                    padding: EdgeInsets.all(Adaptive.sp(10)),
                    decoration: BoxDecoration(
                      color: LDColors.mainLime.withAlpha(80),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      FontAwesomeIcons.shareNodes,
                      color: LDColors.mainGrey,
                      size: 20,
                    ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        FlutterI18n.translate(
                          context,
                          'registration.referral.content.description.list.0.title',
                        ),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                          color: LDColors.mainGrey,
                        ),
                      ),
                      Text(
                        FlutterI18n.translate(
                          context,
                          'registration.referral.content.description.list.0.description',
                        ),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: LDColors.mainGrey,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(
                height: 10,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Container(
                    padding: EdgeInsets.all(Adaptive.sp(10)),
                    decoration: BoxDecoration(
                      color: LDColors.mainLime.withAlpha(80),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      FontAwesomeIcons.peopleGroup,
                      color: LDColors.mainGrey,
                      size: 20,
                    ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        FlutterI18n.translate(
                          context,
                          'registration.referral.content.description.list.1.title',
                        ),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                          color: LDColors.mainGrey,
                        ),
                      ),
                      Text(
                        FlutterI18n.translate(
                          context,
                          'registration.referral.content.description.list.1.description',
                        ),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: LDColors.mainGrey,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(
                height: 10,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Container(
                    padding: EdgeInsets.all(Adaptive.sp(10)),
                    decoration: BoxDecoration(
                      color: LDColors.mainLime.withAlpha(80),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      FontAwesomeIcons.gift,
                      color: LDColors.mainGrey,
                      size: 20,
                    ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        FlutterI18n.translate(
                          context,
                          'registration.referral.content.description.list.2.title',
                        ),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                          color: LDColors.mainGrey,
                        ),
                      ),
                      LDAppState().getLocale() == 'ko'
                          ? _descriptionRowKo
                          : _descriptionRowEn,
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 40,
        ),
      ],
    );
  }
}
