import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/presentation/nav.dart';
import 'package:langda/presentation/pages/mobile/home_page/my_page/profile_page/account_info.dart';
import 'package:langda/presentation/router_constants.dart';
import 'package:langda/utils/my_logger.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../../../backend/model/user_model.dart';
import '../../../../../../common/LDState.dart';
import '../../../../../../common/custom_app_bar.dart';
import '../../../../../../queries/auth_query.dart';
import '../../../../../../queries/card_query.dart';
import '../../../../../../queries/diary_query.dart';
import '../../../../component.dart';
import '../../../constants.dart';
import '../setting_page/qna_page.dart';
import '../subscribe_page/component/product_box.dart';
import 'daily-reward/daily_reward.dart';
import 'referral.dart';

class ProfilePageWidget extends StatefulWidget {
  final bool canBack;
  final Function? onLoading;
  final Function? offLoading;
  final Function? onCardListTapped;
  const ProfilePageWidget(
      {super.key,
      this.canBack = false,
      this.onLoading,
      this.offLoading,
      this.onCardListTapped});

  static void prefetchQueries() {
    myLog('Prefetching queries for ProfilePageWidget');
    fetchUserInfoQuery().result;
    fetchNumberOfDiaryQuery(DateTime.now()).result;
    fetchSavedCardsQuery().result;
  }

  @override
  State<ProfilePageWidget> createState() => _ProfilePageWidgetState();
}

class _ProfilePageWidgetState extends LDState<ProfilePageWidget>
    with ShellScreenStateMixin<ProfilePageWidget> {
  late final AnimationController _controller;

  @override
  void initState() {
    FocusManager.instance.primaryFocus?.unfocus();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  buildContent(BuildContext context) {
    logBuild(
      "ProfilePageWidget $hashCode",
    );

    return isMobile(context)
        ? _buildMobileVersionProfilePage(context)
        : _buildTabletVersionProfilePage(context);
  }

  Scaffold _buildTabletVersionProfilePage(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        bottom: false,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(defaultPadding + 5),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // * my info
              Row(
                children: [
                  Flexible(
                    child: QueryBuilder(
                      query: fetchUserInfoQuery(),
                      builder: (context, state) {
                        final user = state.data;
                        if (state.status == QueryStatus.error) {
                          return Text(
                            state.error.toString(),
                          );
                        } else {
                          return Skeletonizer(
                            enabled: state.status == QueryStatus.loading,
                            child: user == null
                                ? const SizedBox()
                                : AccountInfo(
                                    user: user,
                                    isLoaded: Supabase
                                            .instance.client.auth.currentUser !=
                                        null,
                                  ),
                          );
                        }
                      },
                    ),
                  ),
                  SizedBox(
                    width: Adaptive.w(5),
                  ),
                  // * daily reward
                  Flexible(
                    child: QueryBuilder(
                        query: fetchDiaryRewardQuery(),
                        builder: (context, state) {
                          final viewModel = state.data;
                          return Skeletonizer(
                            enabled: state.status == QueryStatus.loading &&
                                viewModel == null,
                            child: viewModel == null
                                ? Container()
                                : DailyReward(
                                    viewModel: viewModel,
                                  ),
                          );
                        }),
                  ),
                ],
              ),
              const SizedBox(
                height: 20,
              ),
              // * subscription management page button
              if (showSubscription)
                QueryBuilder(
                  query: fetchUserInfoQuery(),
                  builder: (context, state) {
                    final user = state.data;
                    if (state.status == QueryStatus.error) {
                      return Container();
                    } else {
                      return (user != null &&
                              user.status != SubscriptionStatus.none)
                          ? LDButton(
                              label: FlutterI18n.translate(
                                context,
                                'user.info.subscribe.manage.title',
                              ),
                              onPressed: () {
                                context.push(
                                  '/subscribe/method/push/true',
                                );
                              },
                            )
                          : Container();
                    }
                  },
                ),
              // * subscription component
              if (showSubscription)
                QueryBuilder(
                  query: fetchUserInfoQuery(),
                  builder: (context, state) {
                    final user = state.data;
                    return Container(
                      child: user != null &&
                              (user.status == SubscriptionStatus.none ||
                                  user.status == SubscriptionStatus.expired)
                          ? ProductBox(
                              onPurchaseStart: () {
                                if (widget.onLoading != null) {
                                  widget.onLoading!();
                                }
                              },
                              onPurchaseSuccess: () {
                                if (widget.offLoading != null) {
                                  widget.offLoading!();
                                }
                              },
                              onPurchaseFailed: () {
                                if (widget.offLoading != null) {
                                  widget.offLoading!();
                                }
                              },
                              userId: user.id,
                            )
                          : SizedBox(),
                    );
                  },
                ),

              // * record box
              _buildRecordBox(context),
              const SizedBox(
                height: 20,
              ),
              // * navigation buttons
              QueryBuilder(
                query: fetchUserInfoQuery(),
                builder: (context, state) {
                  final user = state.data;
                  return user == null || user.referralCode.isEmpty
                      ? SizedBox()
                      : _navigationButtonListBuilder(
                          context,
                          user.referralCode,
                        );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Scaffold _buildMobileVersionProfilePage(BuildContext context) {
    return Scaffold(
      appBar: customAppBar(
        title: FlutterI18n.translate(context, 'user.profile.title'),
        leadingWidget: widget.canBack
            ? Image.asset(
                'assets/images/icons/chevron-left_1.5.png',
                width: 48,
                fit: BoxFit.scaleDown,
                scale: 1.3,
              )
            : SizedBox(),
        leadingOnTap: () {
          widget.canBack
              ? context.canPop()
                  ? context.pop()
                  : null
              : Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const QNAPageWidget(),
                  ),
                );
        },
        trailing: GestureDetector(
          onTap: () {
            context.push(RoutePaths.settingsPath());
          },
          child: Image.asset(
            'assets/images/icons/settings-02_1.5.png',
            width: 48,
            fit: BoxFit.scaleDown,
            scale: 1.3,
            color: Colors.transparent,
          ),
        ),
        trailingLabel: 'Setting',
      ),
      body: SafeArea(
        bottom: false,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(defaultPadding + 5),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // * my info
              QueryBuilder(
                query: fetchUserInfoQuery(),
                builder: (context, state) {
                  final user = state.data;
                  if (state.status == QueryStatus.error) {
                    return Text(
                      state.error.toString(),
                    );
                  } else {
                    return Skeletonizer(
                      enabled: state.status == QueryStatus.loading,
                      child: user == null
                          ? const SizedBox()
                          : AccountInfo(
                              user: user,
                              isLoaded:
                                  Supabase.instance.client.auth.currentUser !=
                                      null,
                            ),
                    );
                  }
                },
              ),
              const SizedBox(
                height: 20,
              ),
              // * subscription management page button
              if (showSubscription)
                QueryBuilder(
                  query: fetchUserInfoQuery(),
                  builder: (context, state) {
                    final user = state.data;
                    if (state.status == QueryStatus.error) {
                      return Container();
                    } else {
                      return (user != null &&
                              user.status != SubscriptionStatus.none)
                          ? LDButton(
                              label: FlutterI18n.translate(
                                context,
                                'user.info.subscribe.manage.title',
                              ),
                              onPressed: () {
                                context.push(
                                  '/subscribe/method/push/true',
                                );
                              },
                            )
                          : Container();
                    }
                  },
                ),
              // * subscription component
              if (showSubscription)
                QueryBuilder(
                  query: fetchUserInfoQuery(),
                  builder: (context, state) {
                    final user = state.data;
                    return Container(
                      child: user != null &&
                              (user.status == SubscriptionStatus.none ||
                                  user.status == SubscriptionStatus.expired)
                          ? ProductBox(
                              onPurchaseStart: () {
                                if (widget.onLoading != null) {
                                  widget.onLoading!();
                                }
                              },
                              onPurchaseSuccess: () {
                                if (widget.offLoading != null) {
                                  widget.offLoading!();
                                }
                              },
                              onPurchaseFailed: () {
                                if (widget.offLoading != null) {
                                  widget.offLoading!();
                                }
                              },
                              userId: user.id,
                            )
                          : SizedBox(),
                    );
                  },
                ),
              // * daily reward
              QueryBuilder(
                  query: fetchDiaryRewardQuery(),
                  builder: (context, state) {
                    final viewModel = state.data;
                    return Skeletonizer(
                      enabled: state.status == QueryStatus.loading &&
                          viewModel == null,
                      child: viewModel == null
                          ? Container()
                          : DailyReward(
                              viewModel: viewModel,
                            ),
                    );
                  }),
              // * record box
              _buildRecordBox(context),
              const SizedBox(
                height: 20,
              ),
              // * navigation buttons
              QueryBuilder(
                query: fetchUserInfoQuery(),
                builder: (context, state) {
                  final user = state.data;
                  return user == null || user.referralCode.isEmpty
                      ? SizedBox()
                      : _navigationButtonListBuilder(
                          context,
                          user.referralCode,
                        );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Column _navigationButtonListBuilder(BuildContext context, String code) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [
        // referal code button
        Container(
          padding: EdgeInsets.only(
            top: Adaptive.h(2),
            bottom: Adaptive.h(2),
          ),
          decoration: BoxDecoration(
            color: LDColors.mainWhiteGrey,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(
                Adaptive.sp(20),
              ),
            ),
          ),
          child: GestureDetector(
            onTap: () {
              // Show the custom modal bottom sheet
              showCustomModalBottomSheet(
                isDismissible: true, // TODO: Not working
                context: context,
                useRootNavigator: true,
                builder: (_) {
                  return Referral(
                    code: code,
                  );
                },
                containerWidget: (context, animation, child) {
                  return Scaffold(
                    body: Align(
                      alignment: Alignment.bottomCenter,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(
                              Adaptive.sp(20),
                            ),
                          ),
                        ),
                        child: child,
                      ),
                    ),
                    backgroundColor: Colors.transparent,
                  );
                },
              );
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: [
                SizedBox(
                  width: Adaptive.w(5),
                ),
                Icon(
                  FontAwesomeIcons.userGroup,
                  color: LDColors.foundationLime,
                  size: 20,
                ),
                SizedBox(
                  width: Adaptive.w(5),
                ),
                Expanded(
                  child: Text(
                    FlutterI18n.translate(
                      context,
                      'registration.referral.content.share.button',
                    ),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: LDColors.mainGrey,
                    ),
                  ),
                ),
                SizedBox(
                  width: Adaptive.w(5),
                ),
                Icon(
                  FontAwesomeIcons.chevronRight,
                  color: LDColors.mainGrey,
                  size: 20,
                ),
                SizedBox(
                  width: Adaptive.w(5),
                ),
              ],
            ),
          ),
        ),
        // * setting button
        Container(
          padding: EdgeInsets.only(
            top: Adaptive.h(2),
            bottom: Adaptive.h(2),
          ),
          decoration: BoxDecoration(
            color: LDColors.mainWhiteGrey,
          ),
          child: GestureDetector(
            onTap: () {
              context.push(
                RoutePaths.settingsPath(),
              );
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: [
                SizedBox(
                  width: Adaptive.w(5),
                ),
                Icon(
                  FontAwesomeIcons.gear,
                  color: LDColors.foundationLime,
                  size: 20,
                ),
                SizedBox(
                  width: Adaptive.w(5),
                ),
                Expanded(
                  child: Text(
                    FlutterI18n.translate(
                      context,
                      'app.settings.title',
                    ),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: LDColors.mainGrey,
                    ),
                  ),
                ),
                SizedBox(
                  width: Adaptive.w(5),
                ),
                Icon(
                  FontAwesomeIcons.chevronRight,
                  color: LDColors.mainGrey,
                  size: 20,
                ),
                SizedBox(
                  width: Adaptive.w(5),
                ),
              ],
            ),
          ),
        ),
        // support button
        Container(
          padding: EdgeInsets.only(
            top: Adaptive.h(2),
            bottom: Adaptive.h(2),
          ),
          decoration: BoxDecoration(
            color: LDColors.mainWhiteGrey,
            borderRadius: BorderRadius.vertical(
              bottom: Radius.circular(
                Adaptive.sp(20),
              ),
            ),
          ),
          child: GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const QNAPageWidget(),
                ),
              );
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: [
                SizedBox(
                  width: Adaptive.w(5),
                ),
                Icon(
                  FontAwesomeIcons.fileCircleQuestion,
                  color: LDColors.foundationLime,
                  size: 20,
                ),
                SizedBox(
                  width: Adaptive.w(5),
                ),
                Expanded(
                  child: Text(
                    FlutterI18n.translate(
                      context,
                      'user.info.support.title',
                    ),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: LDColors.mainGrey,
                    ),
                  ),
                ),
                SizedBox(
                  width: Adaptive.w(5),
                ),
                Icon(
                  FontAwesomeIcons.chevronRight,
                  color: LDColors.mainGrey,
                  size: 20,
                ),
                SizedBox(
                  width: Adaptive.w(5),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Column _buildRecordBox(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          height: Adaptive.h(2),
        ),
        Text(
          softWrap: true,
          FlutterI18n.translate(
            context,
            'user.record.title',
          ),
          style: TextStyle(
            fontSize: isMobile(context) ? 16 : 20,
            fontWeight: FontWeight.w600,
            color: LDColors.mainGrey,
          ),
        ),
        SizedBox(
          height: Adaptive.h(2),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            // Written Diaries
            Flexible(
              child: QueryBuilder(
                query: fetchNumberOfDiaryQuery(DateTime.now()),
                builder: (context, state) {
                  final Map<String, int>? data = state.data;
                  final int totalCount = data?['total'] ?? 0;

                  return recordButton(
                    context,
                    title: FlutterI18n.translate(
                      context,
                      'user.record.days',
                    ),
                    count: FlutterI18n.plural(
                      context,
                      'user.record.daysCount',
                      totalCount,
                    ),
                    image: Image(
                      color: LDColors.foundationLime,
                      image: AssetImage(
                          'assets/images/icons/pen-tool-02_1.5-l.png'),
                      fit: BoxFit.scaleDown,
                    ),
                    onTap: () {},
                  );
                },
              ),
            ),
            SizedBox(
              width: Adaptive.w(3),
            ),
            // Bookmarked Cards
            Flexible(
              child: QueryBuilder(
                query: fetchSavedCardsQuery(),
                builder: (context, state) {
                  return Skeletonizer(
                    enabled: state.status == QueryStatus.loading,
                    child: recordButton(
                      context,
                      title: FlutterI18n.translate(
                        context,
                        'user.record.cards.title',
                      ),
                      count: FlutterI18n.translate(
                        context,
                        'user.record.cards.count',
                        translationParams: {
                          'count': '${state.data?.length ?? 0}',
                        },
                      ),
                      iconData: FontAwesomeIcons.book,
                      onTap: () {
                        widget.onCardListTapped?.call();
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  GestureDetector recordButton(
    BuildContext context, {
    required String title,
    required String count,
    IconData? iconData,
    Image? image,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(
          Adaptive.sp(16),
        ),
        decoration: BoxDecoration(
          color: LDColors.mainWhiteGrey,
          borderRadius: BorderRadius.circular(
            Adaptive.sp(20),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Flexible(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  AutoSizeText(
                    title,
                    style: TextStyle(
                      fontSize: isMobile(context) ? 16 : 20,
                      fontWeight: FontWeight.w600,
                      color: LDColors.mainGrey,
                    ),
                  ),
                  Row(
                    children: [
                      AutoSizeText(
                        count,
                        style: TextStyle(
                          fontSize: isMobile(context) ? 16 : 20,
                          fontWeight: FontWeight.w600,
                          color: LDColors.mainGrey,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(
              width: Adaptive.w(5),
            ),
            Flexible(
              flex: 1,
              child: iconData != null
                  ? Icon(
                      iconData,
                      color: LDColors.foundationLime,
                      size: 33,
                    )
                  : Container(
                      width: 33,
                      height: 33,
                      child: image ?? const SizedBox(),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Row oneLineForCardInfo({
    required Image image,
    required String title,
    required String buttonText,
  }) {
    final width = Adaptive.w(3.5);
    final height = Adaptive.w(3.5);
    final fontSize = 14.0;
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: width,
          height: height,
          child: image,
        ),
        SizedBox(
          width: Adaptive.w(2),
        ),
        Text(
          title,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.w400,
            color: LDColors.mainGrey,
          ),
        ),
        Spacer(),
        GestureDetector(
          onTap: () {},
          child: Text(
            buttonText,
            style: TextStyle(
              color: LDColors.mainGrey,
              fontSize: fontSize,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
