import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:glowy_borders/glowy_borders.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:langda/backend/model/user_model.dart';
import 'package:langda/presentation/router_constants.dart';
import 'package:langda/queries/diary_query.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../component.dart';
import '../../../constants.dart';

class CampainButton extends StatefulWidget {
  final UserModelStructure user;
  const CampainButton({super.key, required this.user});

  @override
  State<CampainButton> createState() => _CampainButtonState();
}

class _CampainButtonState extends State<CampainButton> {
  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showSubscription)
            Text(
              FlutterI18n.translate(
                context,
                'diary.campaign.empty.phrase',
                translationParams: {
                  'name': widget.user.nickname,
                },
              ),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: LDColors.mainGrey,
              ),
              textAlign: TextAlign.center,
            ),
          if (showSubscription)
            SizedBox(
              height: 20,
            ),
          AnimatedGradientBorder(
            gradientColors: [
              LDColors.mainBlue,
              Colors.blue[100]!,
              Colors.blue[200]!,
            ],
            borderRadius: BorderRadius.circular(10),
            borderSize: 3,
            child: QueryBuilder(
              query: diaryWriteButtonModelQuery(
                DateTime.now(),
              ),
              builder: (context, state) => Skeletonizer(
                enabled: state.status == QueryStatus.loading,
                child: LDButton(
                  label: FlutterI18n.translate(
                    context,
                    'diary.campaign.empty.banner.button',
                  ),
                  onPressed: () {
                    final modelData = state.data;
                    if (modelData != null) {
                      if (modelData.isLoggedIn &&
                          modelData.subscriptionStatus ==
                              SubscriptionStatus.none) {
                        context.go(
                          RoutePaths.diaryCreatePath(),
                        );
                      } else {
                        final formattedSelectedDate =
                            DateFormat('yyyy-MM-dd').format(DateTime.now());

                        if (formattedSelectedDate ==
                            modelData.currentEntryDate) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: LDtoast(
                                  FlutterI18n.translate(
                                    context,
                                    'diary.write.error.entry.time',
                                  ),
                                  ToastType.info,
                                  maxWidth: MediaQuery.of(context).size.width),
                              backgroundColor: Colors.transparent,
                              elevation: 0,
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        }
                      }
                    }
                  },
                ),
              ),
            ),
          ),
          if (showSubscription)
            SizedBox(
              height: 20,
            ),
          if (showSubscription)
            Text(
              FlutterI18n.translate(
                context,
                'diary.campaign.empty.banner.trialDescription',
              ),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.center,
            ),
        ],
      ),
    );
  }
}
