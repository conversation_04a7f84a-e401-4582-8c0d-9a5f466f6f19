import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:langda/presentation/pages/mobile/home_page/my_page/subscribe_page/explanation_form_component.dart';
import 'package:langda/utils/revenucat_helper.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../../../backend/model/user_model.dart';
import '../../../../../router_constants.dart';
import '../../../../component.dart';
import '../../../constants.dart';

class AccountInfo extends StatelessWidget {
  final UserModelStructure user;
  final bool isLoaded;
  const AccountInfo({super.key, required this.user, this.isLoaded = false});

  @override
  Widget build(BuildContext context) {
    return ExplanationFormComponent(
      isLoaded: isLoaded,
      title: FlutterI18n.translate(
        context,
        'user.info.title',
      ),
      titleTrailingWidget: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: LDColors.mainLime,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            gemIcon,
            const SizedBox(
              width: 5,
            ),
            Text(
              '${user.currencyBalance}',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w700,
                color: LDColors.mainGrey,
              ),
            ),
          ],
        ),
      ),
      infoModel: [
        InfoModel(
          // * nickname
          leading: Text(
            user.nickname,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: LDColors.mainGrey,
            ),
          ),
          icon: Icon(
            FontAwesomeIcons.user,
            size: 16,
            color: LDColors.mainGrey,
          ),
          // * edit nickname
          trailing: Semantics(
            label: FlutterI18n.translate(
              context,
              'user.info.nickname.edit.button',
            ),
            button: true,
            child: GestureDetector(
              onTap: () {
                context.push(RoutePaths.nicknameEditPath());
              },
              child: Padding(
                padding: EdgeInsets.symmetric(
                  vertical: Adaptive.sp(14.5),
                ),
                child: Text(
                  FlutterI18n.translate(
                      context, 'user.info.nickname.edit.button'),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: LDColors.mainGrey,
                  ),
                ),
              ),
            ),
          ),
        ),
        InfoModel(
          // * email
          leading: Text(
            user.email,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: LDColors.mainGrey,
            ),
          ),
          icon: Icon(
            FontAwesomeIcons.envelope,
            size: 16,
            color: LDColors.mainGrey,
          ),
        ),
        // InfoModel(
        //   // * language selection setting
        //   leading: Row(
        //     mainAxisAlignment: MainAxisAlignment.start,
        //     crossAxisAlignment: CrossAxisAlignment.center,
        //     mainAxisSize: MainAxisSize.min,
        //     children: [
        //       Text(
        //         user.userLanguage,
        //         style: TextStyle(
        //           fontSize: 14,
        //           fontWeight: FontWeight.w400,
        //           color: LDColors.mainGrey,
        //         ),
        //       ),
        //       const SizedBox(
        //         width: 5,
        //       ),
        //       Icon(
        //         FontAwesomeIcons.chevronRight,
        //         size: 13,
        //         color: LDColors.mainGrey,
        //       ),
        //       const SizedBox(
        //         width: 5,
        //       ),
        //       Text(
        //         user.diaryLanguage,
        //         style: TextStyle(
        //           fontSize: 14,
        //           fontWeight: FontWeight.w400,
        //           color: LDColors.mainGrey,
        //         ),
        //       ),
        //     ],
        //   ),
        //   icon: Icon(
        //     FontAwesomeIcons.globe,
        //     size: 16,
        //     color: LDColors.mainGrey,
        //   ),
        //   trailing: Semantics(
        //     label: FlutterI18n.translate(
        //       context,
        //       'user.info.nickname.edit.button',
        //     ),
        //     button: true,
        //     child: GestureDetector(
        //       onTap: () {
        //         // context.push(RoutePaths.nicknameEditPath());
        //       },
        //       child: Padding(
        //         padding: EdgeInsets.symmetric(
        //           vertical: Adaptive.sp(14.5),
        //         ),
        //         child: Text(
        //           FlutterI18n.translate(
        //               context, 'user.info.nickname.edit.button'),
        //           style: TextStyle(
        //             fontSize: 14,
        //             fontWeight: FontWeight.w400,
        //             color: LDColors.mainGrey,
        //           ),
        //         ),
        //       ),
        //     ),
        //   ),
        // ),
        if (showSubscription)
          if (user.status != SubscriptionStatus.none)
            // * simple subscription status
            InfoModel(
              leading: user.status == SubscriptionStatus.subscribed_annual
                  ? Text(
                      FlutterI18n.translate(
                        context,
                        'user.info.subscribe.status.subscribed.premium.annual',
                      ),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: LDColors.mainGrey,
                      ),
                    )
                  : user.status == SubscriptionStatus.subscribed_monthly
                      ? Text(
                          FlutterI18n.translate(
                            context,
                            'user.info.subscribe.status.subscribed.premium.monthly',
                          ),
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: LDColors.mainGrey,
                          ),
                        )
                      : user.status == SubscriptionStatus.trial
                          ? Text(
                              FlutterI18n.translate(
                                context,
                                'user.info.subscribe.status.trial',
                              ),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: LDColors.mainGrey,
                              ),
                            )
                          : Text(
                              FlutterI18n.translate(
                                context,
                                'user.info.subscribe.status.expired',
                              ),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: LDColors.mainGrey,
                              ),
                            ),
              image: Image(
                image: const AssetImage(
                  'assets/images/icons/lock-01_1.5-mg.png',
                ),
                fit: BoxFit.scaleDown,
              ),
              trailing: FutureBuilder(
                future: customerInfo(),
                builder: (context, state) {
                  final customerInfo = state.data;
                  final String? subscribedEndAt =
                      customerInfo?.entitlements.all['LangDa']?.expirationDate;
                  print('user state: ${user.status}');
                  final DateTime? subscribedEndAtDate = subscribedEndAt != null
                      ? DateTime.parse(subscribedEndAt)
                      : null;
                  return Text(
                    user.status == SubscriptionStatus.trial
                        ? '- ${DateFormat('yyyy.MM.dd').format(
                            user.trialEndsAt!,
                          )}'
                        : user.status == SubscriptionStatus.none
                            ? ''
                            : ((user.status ==
                                            SubscriptionStatus
                                                .subscribed_annual ||
                                        user.status ==
                                            SubscriptionStatus
                                                .subscribed_monthly) &&
                                    subscribedEndAtDate != null)
                                ? '- ${DateFormat('yyyy.MM.dd').format(
                                    subscribedEndAtDate,
                                  )}'
                                : '',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: LDColors.mainGrey,
                    ),
                  );
                },
              ),
            ),
      ],
    );
  }
}
