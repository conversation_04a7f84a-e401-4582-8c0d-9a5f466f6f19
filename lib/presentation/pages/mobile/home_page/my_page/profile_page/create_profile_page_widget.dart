import 'package:flutter/material.dart';
import 'package:flutter_stepindicator/flutter_stepindicator.dart';
import 'package:langda/presentation/pages/mobile/auth_page/nickname_setting/nickname_setting_page.dart';
import 'package:langda/presentation/pages/mobile/auth_page/purpose_page_widget.dart';
import 'package:langda/presentation/pages/mobile/constants.dart';
import 'package:langda/providers/create_profile_provider.dart';
import 'package:provider/provider.dart';

import '../../../auth_page/language_option_page_widget.dart';

class CreateProfilePageWidget extends StatefulWidget {
  const CreateProfilePageWidget({super.key});

  @override
  State<CreateProfilePageWidget> createState() =>
      _CreateProfilePageWidgetState();
}

class _CreateProfilePageWidgetState extends State<CreateProfilePageWidget> {
  late final PageController _pageController;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(
      initialPage: _currentPage,
      keepPage: true,
    );
    _pageController.addListener(() {
      if (_pageController.page?.round() != _currentPage) {
        setState(() {
          _currentPage = _pageController.page!.round();
        });
      }
    });
  }

  @override
  void dispose() {
    _pageController.removeListener(_updatePage); // Clean up listener
    _pageController.dispose();
    super.dispose();
  }

  void _updatePage() {
    if (_pageController.page?.round() != _currentPage) {
      setState(() {
        _currentPage = _pageController.page!.round();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final profileProvider =
        Provider.of<CreateProfileProvider>(context, listen: false);

    final List<Widget> pages = [
      LanguageSelectionPage(
        onTapNext: (String? explanationLanguage, String? nativeLanguage) {
          profileProvider.updateLanguages(
            explanation: explanationLanguage,
            native: nativeLanguage,
          );
          _pageController.nextPage(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        },
      ),
      PurposePageWidget(
        onTapNext: (String? selectedPurpose) {
          profileProvider.updatePurpose(selectedPurpose);
          profileProvider.finalizeStep();
          _pageController.nextPage(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        },
      ),
      const NicknameSettingPage(
        isFromSignUp: true,
      ),
    ];

    return PopScope(
      canPop: false,
      child: Scaffold(
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                    vertical: 16.0, horizontal: 20.0),
                child: FlutterStepIndicator(
                  onChange: (int index) {
                    _pageController.animateToPage(
                      index,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  page: _currentPage,
                  list: const [
                    'Language',
                    'Purpose',
                    'Nickname',
                  ],
                  height: 30,
                  positiveColor: Theme.of(context).colorScheme.primary,
                  negativeColor: LDColors.lightGrey,
                  progressColor: Theme.of(context).colorScheme.primary,
                  division: pages.length,
                ),
              ),
              Expanded(
                child: GestureDetector(
                  child: PageView(
                    controller: _pageController,
                    children: pages,
                    physics: const NeverScrollableScrollPhysics(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
