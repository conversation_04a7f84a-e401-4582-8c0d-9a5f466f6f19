import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../constants.dart';

class InfoModel {
  final Widget? leading;
  final Image? image;
  final Widget? trailing;
  final Icon? icon;

  InfoModel({
    this.leading,
    this.image,
    this.trailing,
    this.icon,
  });
}

class ExplanationFormComponent extends StatelessWidget {
  final String title;
  final Widget? titleTrailingWidget;
  final List<InfoModel> infoModel;
  final double? gap;
  final isLoaded;

  ExplanationFormComponent({
    super.key,
    required this.title,
    this.titleTrailingWidget,
    required this.infoModel,
    this.gap = 5,
    this.isLoaded = false,
  });

  Skeletonizer oneLineForCardInfo(
    InfoModel infoModel,
  ) {
    final width = Adaptive.w(4.5);
    final height = Adaptive.w(4.5);
    return Skeletonizer(
      enabled: !isLoaded,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (infoModel.icon != null)
            Container(
              margin: EdgeInsets.only(right: Adaptive.w(2)),
              width: width,
              height: height,
              child: infoModel.icon,
            ),
          if (infoModel.image != null)
            Container(
              width: width,
              height: height,
              child: infoModel.image,
            ),
          if (infoModel.image != null)
            SizedBox(
              width: Adaptive.w(2),
            ),
          if (infoModel.leading != null) infoModel.leading!,
          Spacer(),
          if (infoModel.trailing != null) infoModel.trailing!,
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
                color: LDColors.mainGrey,
              ),
              textAlign: TextAlign.center,
            ),
            if (titleTrailingWidget != null) titleTrailingWidget!,
          ],
        ),
        SizedBox(
          height: 10,
        ),
        Container(
          padding: EdgeInsets.all(
            Adaptive.sp(18),
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: LDColors.mainWhiteGrey,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              for (var info in infoModel) ...[
                oneLineForCardInfo(info),
                if (gap != null &&
                    (infoModel.indexOf(info) == 0 ||
                        infoModel.indexOf(info) != infoModel.length - 1))
                  SizedBox(
                    height: gap,
                  )
              ],
            ],
          ),
        ),
      ],
    );
  }
}
