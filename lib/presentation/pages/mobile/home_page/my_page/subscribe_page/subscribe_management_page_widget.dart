import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:glowy_borders/glowy_borders.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../../../backend/model/user_model.dart';
import '../../../../../../common/LDState.dart';
import '../../../../../../common/custom_app_bar.dart';
import '../../../../../../queries/auth_query.dart';
import '../../../../../../queries/diary_query.dart';
import '../../../../component.dart';
import '../../../constants.dart';
import '../../../web_view_widget.dart';
import 'component/product_box.dart';
import 'explanation_form_component.dart';

class SubscribeManagementPageWidget extends StatefulWidget {
  const SubscribeManagementPageWidget({
    super.key,
  });

  @override
  State<SubscribeManagementPageWidget> createState() =>
      _SubscribeManagementPageWidgetState();
}

class _SubscribeManagementPageWidgetState
    extends LDState<SubscribeManagementPageWidget> {
  String? selectedValue;
  bool showDotMenu = false;

  @override
  void initState() {
    pageName = 'Subscribe';
    super.initState();
  }

  @override
  buildContent(BuildContext context) {
    return Scaffold(
      appBar: customAppBar(
        title:
            FlutterI18n.translate(context, 'user.info.subscribe.manage.title'),
        leadingWidget: Image.asset(
          'assets/images/icons/chevron-left_1.5.png',
          width: 48,
          fit: BoxFit.scaleDown,
          scale: 1.3,
        ),
        leadingOnTap: () {
          context.canPop() ? context.pop() : null;
        },
        trailing: PopupMenuButton(
          offset: Offset(0, 44),
          child: Image.asset(
            'assets/images/icons/dots-vertical_1.5.png',
            width: 48,
            fit: BoxFit.scaleDown,
            scale: 1.3,
          ),
          itemBuilder: (BuildContext context) => [
            PopupMenuItem(
              value: showDotMenu,
              child: Center(
                child: Text(
                  FlutterI18n.translate(
                      context, 'product.subscription.guide.title'),
                  textAlign: TextAlign.center,
                ),
              ),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => WebViewPageWidget(
                        url:
                            'https://chlorinated-carp-1da.notion.site/12bcac1fd92f80679ea1dfb02a342d15?pvs=4'),
                  ),
                );
              },
            ),
          ],
        ),
      ),
      body: SafeArea(
        bottom: false,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(
            Adaptive.sp(20),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              QueryBuilder(
                query: fetchUserInfoQuery(),
                builder: (context, state) {
                  if (state.status == QueryStatus.error) {
                    return Center(child: Text('error'));
                  } else {
                    final UserModelStructure? user = state.data;
                    return ExplanationFormComponent(
                      isLoaded: true,
                      title: FlutterI18n.translate(
                        context,
                        'user.info.subscribe.period.my',
                      ),
                      infoModel: [
                        InfoModel(
                          leading: Row(
                            children: [
                              Text(
                                FlutterI18n.translate(
                                  context,
                                  'user.info.subscribe.period.product',
                                ),
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  color: LDColors.mainGrey,
                                ),
                              ),
                            ],
                          ),
                          trailing: Text(
                            user != null &&
                                    user.status == SubscriptionStatus.none
                                ? FlutterI18n.translate(
                                    context,
                                    'user.info.subscribe.status.none',
                                  )
                                : user != null &&
                                        user.status == SubscriptionStatus.trial
                                    ? FlutterI18n.translate(
                                        context,
                                        'user.info.subscribe.status.trial',
                                      )
                                    : user != null &&
                                            user.status ==
                                                SubscriptionStatus.expired
                                        ? FlutterI18n.translate(
                                            context,
                                            'user.info.subscribe.status.expired',
                                          )
                                        : user != null &&
                                                user.status ==
                                                    SubscriptionStatus
                                                        .subscribed_annual
                                            ? FlutterI18n.translate(
                                                context,
                                                'user.info.subscribe.status.subscribed.premium.annual',
                                              )
                                            : FlutterI18n.translate(
                                                context,
                                                'user.info.subscribe.status.subscribed.premium.monthly',
                                              ),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: LDColors.mainGrey,
                            ),
                          ),
                        ),
                        if (user != null &&
                            (user.status == SubscriptionStatus.trial ||
                                user.status ==
                                    SubscriptionStatus.subscribed_annual ||
                                user.status ==
                                    SubscriptionStatus.subscribed_monthly))
                          InfoModel(
                            leading: Text(
                              FlutterI18n.translate(
                                context,
                                'user.info.subscribe.period.my',
                              ),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: LDColors.mainGrey,
                              ),
                            ),
                            trailing: Text(
                              (user.status ==
                                          SubscriptionStatus
                                              .subscribed_annual ||
                                      user.status ==
                                          SubscriptionStatus.subscribed_monthly)
                                  ? '- ${DateFormat('yyyy.MM.dd').format(
                                      user.expirationDate,
                                    )}'
                                  : '- ${DateFormat('yyyy.MM.dd').format(
                                      user.trialEndsAt ?? DateTime.now(),
                                    )}',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: LDColors.mainGrey,
                              ),
                            ),
                          ),
                        if (user?.status ==
                                SubscriptionStatus.subscribed_annual ||
                            user?.status ==
                                SubscriptionStatus.subscribed_monthly)
                          InfoModel(
                            leading: Text(
                              FlutterI18n.translate(
                                context,
                                'user.info.subscribe.manage.payment.next',
                              ),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: LDColors.mainGrey,
                              ),
                            ),
                            trailing: Text(
                              DateFormat('yyyy.MM.dd').format(
                                  user?.nextBillingDate ?? DateTime.now()),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: LDColors.mainGrey,
                              ),
                            ),
                          ),
                      ],
                    );
                  }
                },
              ),
              SizedBox(
                height: Adaptive.h(2),
              ),
              QueryBuilder(
                query: fetchNumberOfDiaryQuery(DateTime.now()),
                builder: (context, state) => Visibility(
                  visible: state.data != null && state.data!['total'] == 0,
                  child: QueryBuilder(
                    query: fetchUserInfoQuery(),
                    builder: (context, state) {
                      final user = state.data;
                      if (state.status == QueryStatus.error) {
                        return Container();
                      } else {
                        return (user != null &&
                                user.status == SubscriptionStatus.none)
                            ? Container(
                                alignment: Alignment.center,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 20),
                                child: Column(
                                  children: [
                                    Text(
                                      FlutterI18n.translate(
                                        context,
                                        'diary.campaign.empty.phrase',
                                        translationParams: {
                                          'name': user.nickname,
                                        },
                                      ),
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: LDColors.mainGrey,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    SizedBox(
                                      height: 20,
                                    ),
                                    AnimatedGradientBorder(
                                      gradientColors: [
                                        LDColors.mainBlue,
                                        Colors.blue[100]!,
                                        Colors.blue[200]!,
                                      ],
                                      borderRadius: BorderRadius.circular(10),
                                      borderSize: 3,
                                      child: LDButton(
                                        label: FlutterI18n.translate(
                                          context,
                                          'diary.campaign.empty.banner.button',
                                        ),
                                        onPressed: () {
                                          context.push(
                                            '/write/method/go/true',
                                            extra: {
                                              'content': '',
                                              'date': DateTime.now()
                                                  .toIso8601String(),
                                              'weather': '',
                                            },
                                          );
                                        },
                                      ),
                                    ),
                                    SizedBox(
                                      height: 20,
                                    ),
                                    Text(
                                      FlutterI18n.translate(
                                        context,
                                        'diary.campaign.empty.banner.trialDescription',
                                      ),
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              )
                            : Container();
                      }
                    },
                  ),
                ),
              ),

              // * subscription component
              QueryBuilder(
                query: fetchUserInfoQuery(),
                builder: (context, state) {
                  final user = state.data;
                  return Container(
                    child: user != null &&
                            user.status == SubscriptionStatus.expired
                        ? ProductBox(
                            onPurchaseStart: () {
                              // onLoading(); // 복구 시 재작성 필요
                            },
                            onPurchaseSuccess: () {
                              // offLoading();
                            },
                            onPurchaseFailed: () {
                              // offLoading();

                              showLDToaster(
                                  context,
                                  FlutterI18n.translate(context,
                                      'product.subscription.failed.description'),
                                  ToastType.error);
                            },
                            userId: user.id,
                          )
                        : SizedBox(),
                  );
                },
              ),
              SizedBox(
                height: Adaptive.h(2),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
