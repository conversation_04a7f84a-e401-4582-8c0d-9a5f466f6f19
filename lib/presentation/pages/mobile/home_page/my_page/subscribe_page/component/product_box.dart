import 'dart:io';

import 'package:async_button_builder/async_button_builder.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

import '../../../../../../../queries/product_query.dart';
import '../../../../../../../states/app_state.dart';
import '../../../../../../../utils/my_logger.dart';
import '../../../../../../../utils/revenucat_helper.dart';
import '../../../../constants.dart';

class ProductBoxModel {
  List<Package?> offerings;
  CustomerInfo? customerInfo;

  ProductBoxModel({
    required this.offerings,
    required this.customerInfo,
  });
}

class ProductBox extends StatelessWidget {
  final Function onPurchaseStart;
  final Function onPurchaseSuccess;
  final Function onPurchaseFailed;
  final String userId;

  static void prefetchQueries() {
    myLog('Prefetching queries for ProductBox');
    fetchProductBoxModelQuery().result;
  }

  const ProductBox({
    super.key,
    required this.onPurchaseStart,
    required this.onPurchaseSuccess,
    required this.onPurchaseFailed,
    required this.userId,
  });

  @override
  Widget build(BuildContext context) {
    return QueryBuilder(
      query: fetchProductBoxModelQuery(),
      builder: (context, state) {
        final modelData = state.data;

        if (state.status == QueryStatus.loading ||
            state.status == QueryStatus.error ||
            modelData == null ||
            modelData.offerings.isEmpty) {
          return Container();
        }

        return _boxBuilder(context, modelData, onPurchaseStart,
            onPurchaseSuccess, onPurchaseFailed);
      },
    );
  }

  Padding _boxBuilder(
      BuildContext context,
      ProductBoxModel model,
      Function onPurchaseStart,
      Function onPurchaseSuccess,
      Function onPurchaseFailed) {
    var benefits = Container(
      padding: EdgeInsets.all(15.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: Column(
        children: [
          benefitRow(
            FlutterI18n.translate(
              context,
              'product.subscription.premium.description.body.phrase.0',
            ),
          ),
          SizedBox(
            height: 10,
          ),
          benefitRow(
            FlutterI18n.translate(
              context,
              'product.subscription.premium.description.body.phrase.1',
            ),
          ),
          SizedBox(
            height: 10,
          ),
          benefitRow(
            FlutterI18n.translate(
              context,
              'product.subscription.premium.description.body.phrase.2',
            ),
          ),
          SizedBox(
            height: 10,
          ),
          benefitRow(
            FlutterI18n.translate(
              context,
              'product.subscription.premium.description.body.phrase.3',
            ),
          ),
          SizedBox(
            height: 10,
          ),
          benefitRow(
            FlutterI18n.translate(
              context,
              'product.subscription.premium.description.body.phrase.4',
            ),
          ),
        ],
      ),
    );
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 10.0,
      ),
      child: Container(
        padding: EdgeInsets.all(15.0),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(10.0),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _boxTitleBuilder(context),
            Padding(
              padding: const EdgeInsets.all(2.0),
              child: Text(
                FlutterI18n.translate(
                  context,
                  'product.subscription.premium.description.header',
                ),
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            SizedBox(
              height: 10,
            ),
            benefits,
            SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.all(2.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Wrap(
                    children: [
                      Text(
                        FlutterI18n.translate(
                          context,
                          'product.subscription.premium.description.footer.phrase.0.0',
                        ),
                      ),
                      Text(
                        FlutterI18n.translate(
                          context,
                          'product.subscription.premium.description.footer.phrase.0.1',
                        ),
                        style: TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.w900,
                        ),
                      ),
                    ],
                  ),
                  Wrap(
                    children: [
                      Text(
                        FlutterI18n.translate(
                          context,
                          'product.subscription.premium.description.footer.phrase.1.0',
                        ),
                      ),
                      if (LDAppState().getLocale() == Locale('ko', 'KR'))
                        Text(
                          FlutterI18n.translate(
                            context,
                            'product.subscription.premium.description.footer.phrase.1.1',
                          ),
                          style: TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.w900,
                          ),
                        ),
                      if (LDAppState().getLocale() == Locale('ko', 'KR'))
                        Text(
                          FlutterI18n.translate(
                            context,
                            'product.subscription.premium.description.footer.phrase.1.2',
                          ),
                        ),
                    ],
                  ),
                  Text(
                    FlutterI18n.translate(
                      context,
                      'product.subscription.premium.description.footer.phrase.2.0',
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 10,
            ),
            SizedBox(
              height: 5,
            ),
            // 월간 구독 박스
            GestureDetector(
              onTap: () async {
                final monthlyOffering = model.offerings
                    .where(
                      (element) => element!.packageType == PackageType.monthly,
                    )
                    .firstOrNull;

                if (monthlyOffering == null) {
                  return;
                }
                onPurchaseStart();
                final Map<bool, String?> result =
                    await makePurchase(monthlyOffering);

                if (result.keys.first) {
                  myLog('purchase success');
                  onPurchaseSuccess();
                } else {
                  myLog('purchase failed');
                  onPurchaseFailed();
                }
              },
              child: Container(
                width: double.infinity,
                constraints: BoxConstraints(
                  minHeight: 100,
                ),
                padding: EdgeInsets.all(10.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.0),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Flexible(
                      flex: 3,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          ImageIcon(
                            AssetImage('assets/images/icons/30-512-g.png'),
                            color: Colors.black,
                            size: 20,
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Flexible(
                                  child: AutoSizeText(
                                    FlutterI18n.translate(
                                      context,
                                      'product.subscription.premium.title.monthly',
                                    ),
                                    style: TextStyle(
                                      // fontSize: 15,
                                      fontWeight: FontWeight.w700,
                                    ),
                                    maxLines: 2,
                                  ),
                                ),
                                SizedBox(
                                  height: 5,
                                ),
                                Flexible(
                                  child: AutoSizeText(
                                    FlutterI18n.translate(
                                      context,
                                      'product.subscription.premium.description.purchase.description.monthly.0',
                                    ),
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: LDColors.mainGrey,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Flexible(
                      flex: 1,
                      child: Text(
                        '${model.offerings.isEmpty ? '' : model.offerings.where(
                              (element) =>
                                  element!.packageType == PackageType.monthly,
                            ).first!.storeProduct.priceString}',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 10,
            ),
            // 연간 구독 박스
            GestureDetector(
              onTap: () async {
                final annualOffering = model.offerings
                    .where(
                      (element) => element!.packageType == PackageType.annual,
                    )
                    .firstOrNull;

                if (annualOffering == null) {
                  return;
                }

                onPurchaseStart();
                final Map<bool, String?> result =
                    await makePurchase(annualOffering);

                if (result.keys.first) {
                  myLog('purchase success');
                  onPurchaseSuccess();
                } else {
                  myLog('purchase failed');
                  onPurchaseFailed();
                }
              },
              child: Container(
                width: double.infinity,
                constraints: BoxConstraints(
                  minHeight: 100,
                ),
                padding: EdgeInsets.all(10.0),
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(10.0),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Flexible(
                      flex: 3,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          ImageIcon(
                            AssetImage('assets/images/icons/1-512-w.png'),
                            color: Colors.white,
                            size: 20,
                          ),
                          SizedBox(
                            width: 13,
                          ),
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Flexible(
                                  child: AutoSizeText(
                                    FlutterI18n.translate(
                                      context,
                                      'product.subscription.premium.title.annual',
                                    ),
                                    style: TextStyle(
                                      fontSize: 15,
                                      fontWeight: FontWeight.w700,
                                      color: Colors.white,
                                    ),
                                    maxLines: 1,
                                  ),
                                ),
                                SizedBox(
                                  height: 5,
                                ),
                                Flexible(
                                  child: Text(
                                    FlutterI18n.translate(
                                      context,
                                      'product.subscription.premium.description.purchase.description.annual.0',
                                    ),
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: LDColors.lightGrey,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Flexible(
                      flex: 1,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Container(
                            constraints: BoxConstraints(
                              minWidth: 65,
                            ),
                            padding: EdgeInsets.all(5.0),
                            decoration: BoxDecoration(
                              color: LDColors.mainRed,
                              borderRadius: BorderRadius.circular(5.0),
                            ),
                            child: Text(
                              '30% OFF',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w700,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 5,
                          ),
                          Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Text(
                                FlutterI18n.translate(context,
                                    'product.subscription.premium.price.annual.original'), // 45.26 달러
                                style: TextStyle(
                                  fontSize: 12,
                                  color: LDColors.lightGrey,
                                  decoration: TextDecoration.lineThrough,
                                  decorationColor: LDColors.lightGrey,
                                ),
                              ),
                              Text(
                                '${model.offerings.isEmpty ? '' : model.offerings.where(
                                      (element) =>
                                          element!.packageType ==
                                          PackageType.annual,
                                    ).first!.storeProduct.priceString}',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (Platform.isIOS)
              const SizedBox(
                height: 10,
              ),
            if (Platform.isIOS)
              Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    FlutterI18n.translate(
                      context,
                      'user.info.subscribe.restore.description',
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  SizedBox(
                    height: 60,
                    child: AsyncButtonBuilder(
                      child: Container(
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.white,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Text(
                              FlutterI18n.translate(
                                context,
                                'user.info.subscribe.restore.button',
                              ),
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      onPressed: () async {
                        await restorePurchases();
                      },
                      builder: (context, child, callback, _) {
                        return Container(
                          alignment: Alignment.center,
                          child: GestureDetector(
                            onTap: callback,
                            child: child,
                          ),
                        );
                      },
                      loadingWidget: CircularProgressIndicator(
                        color: Colors.white,
                      ),
                      showSuccess: false,
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Padding _boxTitleBuilder(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 10.0,
      ),
      child: Row(
        children: [
          Image(
            image: AssetImage('assets/images/icons/award-03-512.png'),
            fit: BoxFit.scaleDown,
            width: 20,
            height: 20,
          ),
          SizedBox(
            width: 10,
          ),
          Text(
            FlutterI18n.translate(
              context,
              'product.subscription.premium.title.title',
            ),
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w900,
            ),
          ),
        ],
      ),
    );
  }
}

Row benefitRow(String content) {
  return Row(
    children: [
      Icon(
        FontAwesomeIcons.check,
        color: Colors.black,
        size: 15,
      ),
      SizedBox(
        width: 10,
      ),
      Expanded(
        child: Text(
          content,
          softWrap: true,
        ),
      ),
    ],
  );
}
