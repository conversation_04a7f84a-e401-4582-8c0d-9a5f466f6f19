import 'package:async_button_builder/async_button_builder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/backend/service/common_service.dart';
import 'package:moon_design/moon_design.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../../../common/LDState.dart';
import '../../../../../../common/custom_app_bar.dart';
import '../../../../../../states/app_state.dart';
import '../../../constants.dart';
import '../../../web_view_widget.dart';

class QNAPageWidget extends StatefulWidget {
  const QNAPageWidget({super.key});

  @override
  State<QNAPageWidget> createState() => _QNAPageWidgetState();
}

class _QNAPageWidgetState extends LDState<QNAPageWidget> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _contentController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  int _contentLength = 0;
  bool _isLoading = false;

  @override
  void initState() {
    pageName = 'Q&A';
    super.initState();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _formKey.currentState?.dispose();
    super.dispose();
  }

  @override
  buildContent(BuildContext context) {
    return Scaffold(
      appBar: customAppBar(
        title: FlutterI18n.translate(context, 'app.service.qna.title'),
        leadingWidget: Image.asset(
          'assets/images/icons/chevron-left_1.5.png',
          width: 48,
          fit: BoxFit.scaleDown,
          scale: 1.3,
        ),
        leadingLabel: 'Back',
        leadingOnTap: () {
          context.canPop() ? context.pop() : null;
        },
      ),
      body: SafeArea(
        bottom: false,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(Adaptive.sp(20)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    GestureDetector(
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => WebViewPageWidget(
                            url: LDAppState().getLocale().languageCode == 'ko'
                                ? 'https://chlorinated-carp-1da.notion.site/FAQ-131cac1fd92f802d9ee6de5517f85d4c?pvs=74' // ko
                                : 'https://chlorinated-carp-1da.notion.site/131cac1fd92f807d9cd2d346dfb9352e?pvs=74', // en
                          ),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Text(
                            FlutterI18n.translate(
                                context, 'app.service.qna.faq_button'),
                            style: TextStyle(
                              fontSize: Adaptive.sp(15),
                              color: LDColors.mainGrey,
                            ),
                          ),
                          SizedBox(
                            width: Adaptive.w(2),
                          ),
                          Icon(
                            FontAwesomeIcons.chevronRight,
                            color: LDColors.mainGrey,
                            size: Adaptive.sp(13),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: Adaptive.h(2)),
                    Text(
                      FlutterI18n.translate(
                          context, 'app.service.qna.form.title'),
                      style: TextStyle(fontSize: Adaptive.sp(16)),
                    ),
                    SizedBox(height: Adaptive.h(1)),
                    _buildTitleInput(),
                    SizedBox(height: Adaptive.h(2)),
                    Text(
                      FlutterI18n.translate(
                          context, 'app.service.qna.form.content'),
                      style: TextStyle(fontSize: Adaptive.sp(16)),
                    ),
                    SizedBox(height: Adaptive.h(1)),
                    _buildContentInput(),
                  ],
                ),
              ),
              SizedBox(height: Adaptive.h(2)),
              Container(
                padding: EdgeInsets.all(Adaptive.sp(20)),
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: LDColors.lightGrey,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Icon(
                          FontAwesomeIcons.circleInfo,
                          color: LDColors.mainGrey,
                          size: Adaptive.sp(15),
                        ),
                        SizedBox(width: Adaptive.w(2)),
                        Text(
                          FlutterI18n.translate(
                              context, 'app.service.qna.alert.title'),
                          style: TextStyle(
                            fontSize: Adaptive.sp(14),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: Adaptive.h(1)),
                    Text(
                      FlutterI18n.translate(
                          context, 'app.service.qna.alert.description.0'),
                      style: TextStyle(
                        fontSize: Adaptive.sp(13),
                        color: LDColors.mainGrey,
                      ),
                    ),
                    SizedBox(height: Adaptive.h(1)),
                    Text(
                      FlutterI18n.translate(
                          context, 'app.service.qna.alert.description.1'),
                      style: TextStyle(
                        fontSize: Adaptive.sp(13),
                        color: LDColors.mainGrey,
                      ),
                    ),
                    SizedBox(height: Adaptive.h(1)),
                    Text(
                      FlutterI18n.translate(
                          context, 'app.service.qna.alert.description.2'),
                      style: TextStyle(
                        fontSize: Adaptive.sp(13),
                        color: LDColors.mainGrey,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: Adaptive.h(2)),
              // Done button
              SizedBox(
                height: 70,
                width: double.infinity,
                child: AsyncButtonBuilder(
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: _isLoading
                          ? Theme.of(context).disabledColor
                          : Theme.of(context).primaryColor,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Text(
                          FlutterI18n.translate(context, 'button.label.submit'),
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  onPressed: () async {
                    if (_formKey.currentState!.validate()) {
                      setState(() => _isLoading = true);
                      try {
                        Future.delayed(const Duration(seconds: 1));
                        await CommonService.instance.insertQuestion(
                          _titleController.text,
                          _contentController.text,
                        );

                        _showSuccessDialog();
                      } catch (e) {
                        _showErrorDialog();
                      } finally {
                        setState(() => _isLoading = false);
                      }
                    }
                  },
                  builder: (context, child, callback, _) {
                    return Container(
                      child: GestureDetector(
                        onTap: callback,
                        child: child,
                      ),
                    );
                  },
                  loadingWidget: CircularProgressIndicator(),
                  showSuccess: false,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTitleInput() {
    return MoonFormTextInput(
      activeBorderColor: Theme.of(context).colorScheme.primary,
      controller: _titleController,
      validator: (value) => value != null && value.length < 5
          ? FlutterI18n.translate(context, 'app.service.qna.error.title')
          : null,
      trailing: _titleController.text.isEmpty
          ? null
          : Semantics(
              label: FlutterI18n.translate(context, 'button.label.clear'),
              child: GestureDetector(
                onTap: () => setState(() => _titleController.clear()),
                child: const Icon(MoonIcons.controls_close_small_24_light),
              ),
            ),
    );
  }

// cotent input
  Widget _buildContentInput() {
    return Column(
      children: [
        MoonFormTextInput(
          textAlignVertical: TextAlignVertical.top,
          padding: EdgeInsets.all(Adaptive.sp(14)),
          maxLines: 10,
          activeBorderColor: Theme.of(context).colorScheme.primary,
          height: Adaptive.h(25),
          controller: _contentController,
          validator: (value) => value != null && value.length < 10
              ? FlutterI18n.translate(context, 'app.service.qna.error.content')
              : null,
          trailing: _contentController.text.isEmpty
              ? null
              : Align(
                  alignment: Alignment.topRight + Alignment(0, 0.1),
                  child: Semantics(
                    label: FlutterI18n.translate(context, 'button.label.clear'),
                    child: GestureDetector(
                      onTap: () => _contentController.clear(),
                      child: Padding(
                        padding: EdgeInsets.only(top: Adaptive.sp(10)),
                        child:
                            const Icon(MoonIcons.controls_close_small_24_light),
                      ),
                    ),
                  ),
                ),
          maxLength: 300,
          onChanged: (value) => setState(() => _contentLength = value.length),
        ),
        SizedBox(height: Adaptive.h(1)),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              '$_contentLength/300',
              style: TextStyle(
                fontSize: Adaptive.sp(12),
                color: LDColors.mainGrey,
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showSuccessDialog() {
    showLDDialog(
      context,
      FlutterI18n.translate(context, 'app.service.qna.success'),
      onConfirmed: () {
        _titleController.clear();
        _contentController.clear();
        setState(() => _contentLength = 0);
        Navigator.pop(context);
      },
      onCanceled: null,
    );
  }

  void _showErrorDialog() {
    showLDDialog(
      context,
      FlutterI18n.translate(context, 'app.service.qna.error.general'),
      onConfirmed: () {},
      onCanceled: null,
    );
  }
}
