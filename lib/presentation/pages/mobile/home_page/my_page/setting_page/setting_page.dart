import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/common/LDState.dart';
import 'package:langda/presentation/router_constants.dart';
import 'package:langda/presentation/services/app_state_service.dart';
import 'package:langda/presentation/services/auth_service.dart';
import 'package:moon_design/moon_design.dart';
import 'package:provider/provider.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../../../common/custom_app_bar.dart';
import '../../../../../../states/app_state.dart';
import '../../../../component.dart';
import '../../../auth_page/delete_user_pagae_widget.dart';
import '../../../constants.dart';
import '../../../web_view_widget.dart';

class SettingPageWidget extends StatefulWidget {
  SettingPageWidget({super.key});

  @override
  State<SettingPageWidget> createState() => _SettingPageWidgetState();
}

class _SettingPageWidgetState extends LDState<SettingPageWidget> {
  bool showItems = false;

  double _gap = Adaptive.sp(14.5);

  @override
  void initState() {
    pageName = 'Setting';
    super.initState();
  }

  buildContent(BuildContext context) {
    final AuthService authService = context.read<AuthService>();
    final AppStateService appStateService = context.read<AppStateService>();

    return Scaffold(
      appBar: customAppBar(
        title: FlutterI18n.translate(context, 'app.settings.title'),
        leadingWidget: Image.asset(
          'assets/images/icons/chevron-left_1.5.png',
          width: 48,
          fit: BoxFit.scaleDown,
          scale: 1.3,
        ),
        leadingLabel: 'Back',
        leadingOnTap: () {
          context.canPop() ? context.pop() : null;
        },
      ),
      body: SafeArea(
        bottom: false,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(
            Adaptive.sp(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Text(
                    FlutterI18n.translate(
                        context, 'app.settings.language.title'),
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: LDColors.mainGrey,
                    ),
                  ),
                  SizedBox(
                    height: Adaptive.sp(17),
                  ),

                  // * Language Dropdown
                  MoonDropdown(
                    dropdownShadows: [
                      BoxShadow(
                        color: Colors.transparent,
                      ),
                    ],
                    show: showItems,
                    onTapOutside: () {
                      setState(() {
                        showItems = false;
                      });
                    },
                    child: Semantics(
                      label: FlutterI18n.translate(
                        context,
                        'app.settings.language.hint',
                      ),
                      button: true,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            showItems = !showItems;
                          });
                        },
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          height: Adaptive.sp(28),
                          padding: EdgeInsets.all(
                            Adaptive.sp(13),
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: LDColors.subGrey,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(5),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                FlutterI18n.translate(
                                  context,
                                  'app.settings.language.hint',
                                ),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: LDColors.mainGrey,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              Icon(
                                showItems
                                    ? FontAwesomeIcons.chevronUp
                                    : FontAwesomeIcons.chevronDown,
                                size: Adaptive.sp(14),
                                color: LDColors.mainGrey,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    content: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                      ),
                      child: contentContainer(
                        // * Language Dropdown Items
                        items: [
                          FlutterI18n.translate(
                              context, 'app.settings.language.ko'),
                          FlutterI18n.translate(
                              context, 'app.settings.language.en'),
                          FlutterI18n.translate(
                              context, 'app.settings.language.ja'),
                        ],
                        provider: appStateService,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: Adaptive.sp(17),
                  ),
                  // App info
                  Text(
                    FlutterI18n.translate(context, 'app.info.title'),
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: LDColors.mainGrey,
                    ),
                  ),
                  SizedBox(
                    height: Adaptive.sp(10),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(
                      vertical: Adaptive.sp(10),
                    ),
                    child: Text(
                      'ver ${LDAppState().appVersion}',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: LDColors.mainGrey,
                      ),
                    ),
                  ),

                  Semantics(
                    label: FlutterI18n.translate(
                      context,
                      'app.service.guide.title',
                    ),
                    button: true,
                    child: GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => WebViewPageWidget(
                              url: LDAppState().getLocale().languageCode == 'ja'
                                  ? 'https://chlorinated-carp-1da.notion.site/213cac1fd92f8035a010f42cb0a4ef03?source=copy_link' // ja
                                  : 'https://chlorinated-carp-1da.notion.site/131cac1fd92f807d9cd2d346dfb9352e?pvs=74', // ko, en
                            ),
                          ),
                        );
                      },
                      child: plainButtonText(
                        context,
                        text: FlutterI18n.translate(
                          context,
                          'app.service.guide.title',
                        ),
                      ),
                    ),
                  ),

                  SizedBox(
                    height: Adaptive.sp(10),
                  ),

                  Text(
                    FlutterI18n.translate(context, 'app.info.etc'),
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: LDColors.mainGrey,
                    ),
                  ),
                  SizedBox(
                    height: Adaptive.sp(10),
                  ),

                  Semantics(
                    label: FlutterI18n.translate(
                        context, 'app.privacyPolicy.title'),
                    button: true,
                    child: GestureDetector(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: Text(
                              FlutterI18n.translate(
                                context,
                                'app.privacyPolicy.title',
                              ),
                            ),
                            content: Container(
                              height: 300,
                              child: SingleChildScrollView(
                                child: Text(
                                  FlutterI18n.translate(
                                    context,
                                    'app.privacyPolicy.content',
                                  ),
                                ),
                              ),
                            ),
                            actions: [
                              LDButton(
                                label: FlutterI18n.translate(
                                  context,
                                  'button.label.confirm',
                                ),
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                              )
                            ],
                          ),
                        );
                      },
                      child: plainButtonText(
                        context,
                        text: FlutterI18n.translate(
                          context,
                          'app.privacyPolicy.title',
                        ),
                      ),
                    ),
                  ),

                  Semantics(
                    label: FlutterI18n.translate(
                        context, 'app.termsOfService.title'),
                    button: true,
                    child: GestureDetector(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: Text(
                              FlutterI18n.translate(
                                context,
                                'app.termsOfService.title',
                              ),
                            ),
                            content: Container(
                              height: 300,
                              child: SingleChildScrollView(
                                child: Text(
                                  FlutterI18n.translate(
                                    context,
                                    'app.termsOfService.content',
                                  ),
                                ),
                              ),
                            ),
                            actions: [
                              LDButton(
                                label: FlutterI18n.translate(
                                  context,
                                  'button.label.confirm',
                                ),
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                              )
                            ],
                          ),
                        );
                      },
                      child: plainButtonText(
                        context,
                        text: FlutterI18n.translate(
                          context,
                          'app.termsOfService.title',
                        ),
                      ),
                    ),
                  ),

                  Semantics(
                    label: 'FAQ',
                    button: true,
                    child: GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => WebViewPageWidget(
                              url: LDAppState().getLocale().languageCode == 'ja'
                                  ? 'https://chlorinated-carp-1da.notion.site/FAQ-213cac1fd92f8069b4e9c50416e1fe14?source=copy_link' // ko
                                  : 'https://chlorinated-carp-1da.notion.site/FAQ-131cac1fd92f802d9ee6de5517f85d4c?pvs=74', // ko, en
                            ),
                          ),
                        );
                      },
                      child: plainButtonText(
                        context,
                        text: 'FAQ',
                      ),
                    ),
                  ),
                  Semantics(
                    label:
                        FlutterI18n.translate(context, 'app.service.qna.title'),
                    button: true,
                    child: GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => DeleteUserPagaeWidget(),
                          ),
                        );
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: _gap),
                        child: Text(
                          FlutterI18n.translate(context, 'delete.title'),
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: LDColors
                                .mainGrey, // Consider a different color for destructive actions
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: Adaptive.sp(20),
                  ),
                  LDButton(
                    label: FlutterI18n.translate(context, 'logout.title'),
                    textColor: LDColors.mainGrey,
                    onPressed: () async {
                      await showLDDialog(
                        context,
                        FlutterI18n.translate(
                          context,
                          'logout.description',
                        ),
                        onConfirmed: () async {
                          // Consider showing loading indicator during logout
                          await authService.logout().then((value) {
                            // Ensure context is still valid before navigating
                            if (mounted) {
                              context.go(RoutePaths.logoutPath());
                            }
                          });
                        },
                        onCanceled: () {},
                      );
                    },
                    backgroundColor: LDColors.subGrey,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Padding plainButtonText(
    BuildContext context, {
    required String text,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: _gap,
      ),
      child: Row(
        children: [
          Text(
            text,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: LDColors.mainGrey,
            ),
          ),
          SizedBox(
            width: 10,
          ),
          Icon(
            FontAwesomeIcons.chevronRight,
            size: 13,
            color: LDColors.mainGrey,
          ),
        ],
      ),
    );
  }

  Container contentContainer({
    required List<String> items,
    required AppStateService provider,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: LDColors.subGrey,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          for (var i = 0; i < items.length; i++)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Semantics(
                  label: items[i],
                  button: true,
                  child: GestureDetector(
                    onTap: () async {
                      provider.setLoading(true);
                      showItems = false;
                      if (!mounted) return;
                      setState(() {});

                      var languageCode = 'ko';
                      final String countryCode =
                          Platform.localeName.split('_').last;

                      if (i == 1) {
                        languageCode = 'en';
                      } else if (i == 2) {
                        languageCode = 'ja';
                      }

                      try {
                        await Future.wait([
                          Future.delayed(
                            const Duration(milliseconds: 2000),
                          ),
                          FlutterI18n.refresh(
                              context, Locale(languageCode, countryCode)),
                          LDAppState().setLocale(languageCode, countryCode),
                        ]);

                        if (mounted) {
                          provider.setLoading(false);
                          setState(() {});
                          if (mounted) {
                            context.go(RoutePaths.home);
                          }
                        }
                      } catch (e) {
                        print("Error changing language: $e");
                        if (mounted) {
                          provider.setLoading(false);
                          setState(() {});
                        }
                      }
                    },
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      padding: const EdgeInsets.all(10.0),
                      child: Text(
                        items[i],
                        style: TextStyle(
                          fontSize: Adaptive.sp(15),
                          color: LDColors.mainGrey,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                ),
                if (i != items.length - 1)
                  Divider(
                    color: LDColors.subGrey,
                    height: 1,
                  ),
              ],
            ),
        ],
      ),
    );
  }
}
