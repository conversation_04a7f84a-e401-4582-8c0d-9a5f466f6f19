import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/presentation/router_constants.dart';
import 'package:langda/providers/process_diary_provider.dart';
import 'package:lottie/lottie.dart';
import 'package:moon_design/moon_design.dart';
import 'package:overlay_tooltip/overlay_tooltip.dart';
import 'package:provider/provider.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../../../backend/model/diary_model.dart';
import '../../../../../../models/generated_classes.dart';
import '../../../../../../queries/diary_query.dart';
import '../../../../../../states/app_state.dart';
import '../../../constants.dart';
import '../component/calendar_widget.dart';
import '../component/ld_note_cell_component.dart';
import '../component/streak_component.dart';

class CalendarScreen extends StatefulWidget {
  final Function(DateTime) onDaySelected;
  final DateTime focusedDay;
  final TooltipController tooltipController;
  final bool showTooltip;
  const CalendarScreen({
    super.key,
    required this.onDaySelected,
    required this.focusedDay,
    required this.tooltipController,
    required this.showTooltip,
  });

  @override
  State<CalendarScreen> createState() => _CalendarScreenState();
}

class _CalendarScreenState extends State<CalendarScreen> {
  @override
  build(BuildContext context) {
    final processingProvider = context.watch<DiaryProcessingProvider>();

    print('Building CalendarScreen $hashCode');
    return SingleChildScrollView(
      padding: EdgeInsets.all(
        10.sp,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 10.sp,
          ),
          AuthWrapper(
            child: QueryBuilder(
              query: fetchNumberOfDiaryQuery(
                DateTime.now(),
              ),
              builder: (context, state) {
                return Skeletonizer(
                  enabled: state.status == QueryStatus.loading,
                  child: _streakButtonsBuilder(
                    context,
                    state.data?['streak'] ?? 0,
                    state.data?['total'] ?? 0,
                  ),
                );
              },
            ),
          ),
          SizedBox(
            height: 12.sp,
          ),
          // * calendar
          CalendarWidget(
            onDaySelected: (p0) async => {
              await widget.onDaySelected(p0),
            },
            focusedDay: widget.focusedDay,
            languageCode: LDAppState().getLocale().languageCode.toLowerCase(),
          ),
          SizedBox(
            height: 12.sp,
          ),

          if (widget.showTooltip)
            OverlayTooltipItem(
              displayIndex: 1,
              tooltipVerticalPosition: TooltipVerticalPosition.TOP,
              tooltipHorizontalPosition: TooltipHorizontalPosition.CENTER,
              tooltip: (controller) {
                return Container(
                  width: MediaQuery.of(context).size.width,
                  padding: EdgeInsets.all(10.sp),
                  child: Text(
                    FlutterI18n.translate(
                      context,
                      'tooltip.write.1',
                    ),
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: Colors.white,
                    ),
                  ),
                );
              },
              child: Container(
                width: MediaQuery.of(context).size.width,
                child: LDNoteCellComponent(
                  noteListModelStruct: DiaryListModelStruct(
                    id: '1',
                    date: DateTime.now(),
                    weather: 'sunny',
                    originalVersion: DiaryVersions(
                      id: '',
                      diaryEntryId: '',
                      content: 'Today is a good day! I feel happy!',
                      versionType: DIARY_VERSION_TYPE.original,
                      createdAt: DateTime.now(),
                      updatedAt: DateTime.now(),
                    ),
                    correctedVersion: null,
                  ),
                  onTap: () {},
                  weather: 'sunny',
                ),
              ),
            ),

          if (Supabase.instance.client.auth.currentUser != null)
            // Replace by:
            processingProvider.status == ProcessingStatus.processing
                // appStateService.diarySlot.state == DiarySlotState.processing
                ? _pendingContainerBuilder(context)
                : Stack(
                    children: [
                      QueryBuilder(
                        query: fetchDiaryListWithDateQuery(
                          widget.focusedDay.year,
                          widget.focusedDay.month,
                        ),
                        builder: (context, state) {
                          if (state.status == QueryStatus.error) {
                            return Center(child: Text('error'));
                          } else {
                            final List<DiaryListModelStruct>? noteList =
                                state.data;
                            final DiaryListModelStruct? note = noteList
                                ?.where((note) => DateUtils.isSameDay(
                                    note.date!, widget.focusedDay))
                                .firstOrNull;
                            return note == null
                                ? const SizedBox()
                                : LDNoteCellComponent(
                                    noteListModelStruct: note,
                                    onTap: () {
                                      // Navigate using the new relative path under the diaryTab route
                                      // Using push to keep the calendar screen in the stack
                                      context.push(RoutePaths.diaryDetailPath(
                                        note.id,
                                      ));
                                    },
                                    weather: note.weather ?? 'sunny',
                                  );
                          }
                        },
                      ),
                    ],
                  ),
        ],
      ),
    );
  }

  Container _pendingContainerBuilder(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 47.sp,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: LDColors.lightGrey,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 30.sp,
            height: 30.sp,
            padding: const EdgeInsets.all(8.0),
            child: Lottie.asset('assets/lottie/edit.json'),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 8.0,
            ),
            child: Text(
              FlutterI18n.translate(
                context,
                'diary.write.pending.time.prediction',
              ),
              style: TextStyle(
                fontSize: 14.sp,
              ),
            ),
          ),
          AnimatedTextKit(
            pause: Duration(milliseconds: 2000),
            repeatForever: true,
            animatedTexts: [
              TypewriterAnimatedText(
                FlutterI18n.translate(
                  context,
                  'diary.write.pending.loading.typeWriterAnimationText.0',
                ),
                speed: Duration(milliseconds: 250),
                textStyle: TextStyle(
                  fontSize: 13.sp,
                ),
              ),
              TypewriterAnimatedText(
                FlutterI18n.translate(
                  context,
                  'diary.write.pending.loading.typeWriterAnimationText.1',
                ),
                speed: Duration(milliseconds: 250),
                textStyle: TextStyle(
                  fontSize: 13.sp,
                ),
              ),
              TypewriterAnimatedText(
                FlutterI18n.translate(
                  context,
                  'diary.write.pending.loading.typeWriterAnimationText.2',
                ),
                speed: Duration(milliseconds: 250),
                textStyle: TextStyle(
                  fontSize: 13.sp,
                ),
              ),
              TypewriterAnimatedText(
                FlutterI18n.translate(
                  context,
                  'diary.write.pending.loading.typeWriterAnimationText.3',
                ),
                speed: Duration(milliseconds: 250),
                textStyle: TextStyle(
                  fontSize: 13.sp,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Row _streakButtonsBuilder(
      BuildContext context, int streakCount, int totalCount) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.max,
      children: [
        Flexible(
          child: MoonButton(
            isFullWidth: true,
            height: 40,
            label: Text(
              FlutterI18n.translate(
                context,
                'user.record.streak.count',
                translationParams: {
                  'count': '${streakCount}',
                },
              ),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            onTap: () {
              if (totalCount != 0)
                showMoonModal(
                  context: context,
                  builder: (context) => StreakComponent(
                    streakCount: streakCount,
                    totalCount: totalCount,
                  ),
                  anchorPoint: Offset(
                    MediaQuery.of(context).size.width / 2,
                    MediaQuery.of(context).size.height / 2,
                  ),
                );
            },
            backgroundColor: LDColors.foundationLimeLight,
          ),
        ),
        SizedBox(
          width: Adaptive.sp(17),
        ),
        Flexible(
          child: MoonButton(
            isFullWidth: true,
            height: 40,
            label: Padding(
              padding: const EdgeInsets.all(8.0),
              child: AutoSizeText(
                FlutterI18n.plural(
                  context,
                  'user.record.streak.all',
                  totalCount,
                ),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                minFontSize: 10,
                maxLines: 2,
              ),
            ),
            onTap: () {
              showMoonModal(
                context: context,
                builder: (context) => StreakComponent(
                  streakCount: streakCount,
                  totalCount: totalCount,
                ),
              );
            },
            backgroundColor: LDColors.lightGrey,
          ),
        ),
      ],
    );
  }
}

class AuthWrapper extends StatelessWidget {
  final Widget child;
  const AuthWrapper({super.key, required this.child});
  @override
  Widget build(BuildContext context) {
    return Supabase.instance.client.auth.currentUser != null
        ? child
        : const SizedBox();
  }
}
