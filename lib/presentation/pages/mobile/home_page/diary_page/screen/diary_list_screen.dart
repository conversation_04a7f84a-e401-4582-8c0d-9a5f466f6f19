import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/presentation/router_constants.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../../../backend/model/diary_model.dart';
import '../../../../../../queries/diary_query.dart';
import '../../../../../../utils/my_logger.dart';
import '../../../constants.dart';
import '../../../empty_list_widget.dart';
import '../component/ld_note_cell_component.dart';

class NoteListScreen extends StatefulWidget {
  final DateTime focusedDay;
  const NoteListScreen({super.key, required this.focusedDay});

  static void prefetchQueries() {
    myLog('Prefetching queries for NoteListScreen');
    final _today = DateTime.now();
    fetchDiaryListWithDateQuery(_today.year, _today.month).result;
  }

  @override
  State<NoteListScreen> createState() => _NoteListScreenState();
}

class _NoteListScreenState extends State<NoteListScreen> {
  final DateTime _today = DateTime.now();
  List<String> get _years => List.generate(5, (index) {
        return (_today.year - 2 + index).toString();
      });
  List<String> get _months => List.generate(12, (index) {
        return (index + 1).toString();
      });
  bool _orderByLatest = true;
  late DateTime _selectedDate;

  late List<DiaryListModelStruct> _noteList = [];
  List<DiaryListModelStruct> get _filteredNoteList => _noteList
      .where((element) =>
          element.date!.year == _selectedDate.year &&
          element.date!.month == _selectedDate.month)
      .toList()
    ..sort((a, b) => _orderByLatest
        ? b.date!.compareTo(a.date!)
        : a.date!.compareTo(b.date!));

  void toggleOrderByLatest(bool value) async {
    _orderByLatest = value;

    setState(() {});
  }

  @override
  void initState() {
    _selectedDate = widget.focusedDay;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        await Future.delayed(const Duration(milliseconds: 500));
        CachedQuery.instance.invalidateCache(
            key: 'note_list${_selectedDate.year}${_selectedDate.month}');

        setState(() {});
      },
      child: SingleChildScrollView(
        clipBehavior: Clip.none,
        padding: EdgeInsets.all(
          10.sp,
        ),
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            GestureDetector(
              onTap: () => toggleOrderByLatest(!_orderByLatest),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Flexible(
                    flex: 6,
                    child: Padding(
                      padding: EdgeInsets.only(
                        right: 15.sp,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Flexible(
                            // * Dropdown for year
                            child: CustomDropdown<String>(
                              hintText: FlutterI18n.translate(
                                  context, 'diary.list.filter.date.year.hint'),
                              headerBuilder: (context, selectedItem, enabled) =>
                                  Text(
                                selectedItem,
                                style: TextStyle(
                                  color: LDColors.mainGrey,
                                  fontSize: 13.sp,
                                ),
                              ),
                              listItemBuilder: (context, item, selected, _) {
                                return Text(
                                  item,
                                  style: TextStyle(
                                    color: LDColors.mainGrey,
                                    fontSize: 13.sp,
                                  ),
                                );
                              },
                              items: _years,
                              initialItem: _selectedDate.year.toString(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedDate = DateTime(
                                    int.parse(value!),
                                    _selectedDate.month,
                                    _selectedDate.day,
                                  );
                                });
                              },
                              decoration: CustomDropdownDecoration(
                                expandedBorder: Border.all(
                                  color: Colors.transparent,
                                ),
                                expandedBorderRadius: BorderRadius.circular(
                                  Adaptive.sp(15),
                                ),
                                closedSuffixIcon: Icon(
                                  FontAwesomeIcons.chevronDown,
                                  size: 14.sp,
                                  color: LDColors.mainGrey,
                                ),
                                expandedSuffixIcon: Icon(
                                  FontAwesomeIcons.chevronUp,
                                  size: 14.sp,
                                  color: LDColors.mainGrey,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 10.sp,
                          ),
                          Container(
                            constraints: BoxConstraints(
                              maxWidth: 90,
                              minWidth: 50,
                            ),
                            child: CustomDropdown<String>(
                              hintText: FlutterI18n.translate(
                                  // 월 선택 필터 힌트
                                  context,
                                  'diary.list.filter.date.month.hint'),
                              headerBuilder: (context, selectedItem, enabled) =>
                                  Text(
                                selectedItem.length == 1
                                    ? '0$selectedItem'
                                    : selectedItem,
                                style: TextStyle(
                                  color: LDColors.mainGrey,
                                  fontSize: 13.sp,
                                ),
                              ),
                              listItemBuilder: (context, item, selected, _) {
                                return Text(
                                  item.length == 1 ? '0$item' : item,
                                  style: TextStyle(
                                    color: LDColors.mainGrey,
                                    fontSize: 13.sp,
                                  ),
                                );
                              },
                              items: _months,
                              initialItem: _selectedDate.month.toString(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedDate = DateTime(
                                    _selectedDate.year,
                                    int.parse(value!),
                                    _selectedDate.day,
                                  );
                                });
                              },
                              decoration: CustomDropdownDecoration(
                                expandedBorder: Border.all(
                                  color: Colors.transparent,
                                ),
                                expandedBorderRadius: BorderRadius.circular(
                                  Adaptive.sp(15),
                                ),
                                closedSuffixIcon: Icon(
                                  FontAwesomeIcons.chevronDown,
                                  size: 14.sp,
                                  color: LDColors.mainGrey,
                                ),
                                expandedSuffixIcon: Icon(
                                  FontAwesomeIcons.chevronUp,
                                  size: 14.sp,
                                  color: LDColors.mainGrey,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 10.sp,
                  ),
                  Flexible(
                    fit: FlexFit.tight,
                    flex: 4,
                    // * Dropdown for sort option
                    child: CustomDropdown<String>(
                      hintText: FlutterI18n.translate(
                          // 정렬 선택 필터 힌트
                          context,
                          'diary.list.filter.sort.hint'),
                      headerBuilder: (context, selectedItem, enabled) => Text(
                        selectedItem,
                        style: TextStyle(
                          color: LDColors.mainGrey,
                          fontSize: 13.sp,
                        ),
                      ),
                      listItemBuilder: (context, item, selected, _) {
                        return AutoSizeText(
                          item,
                          style: TextStyle(
                            color: LDColors.mainGrey,
                            fontSize: 13.sp,
                          ),
                        );
                      },
                      items: [
                        FlutterI18n.translate(
                          context,
                          'diary.list.filter.sort.up',
                        ),
                        FlutterI18n.translate(
                          context,
                          'diary.list.filter.sort.down',
                        ),
                      ],
                      initialItem: FlutterI18n.translate(
                        context,
                        'diary.list.filter.sort.up',
                      ),
                      onChanged: (value) {
                        toggleOrderByLatest(value ==
                            FlutterI18n.translate(
                              context,
                              'diary.list.filter.sort.down',
                            ));
                      },
                      decoration: CustomDropdownDecoration(
                        expandedBorder: Border.all(
                          color: Colors.transparent,
                        ),
                        expandedBorderRadius: BorderRadius.circular(
                          Adaptive.sp(15),
                        ),
                        closedSuffixIcon: Icon(
                          FontAwesomeIcons.chevronDown,
                          size: 14.sp,
                          color: LDColors.mainGrey,
                        ),
                        expandedSuffixIcon: Icon(
                          FontAwesomeIcons.chevronUp,
                          size: 14.sp,
                          color: LDColors.mainGrey,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            QueryBuilder<List<DiaryListModelStruct>>(
              query: fetchDiaryListWithDateQuery(
                _selectedDate.year,
                _selectedDate.month,
              ),
              builder: (context, state) {
                _noteList = state.data ?? [];
                return Skeletonizer(
                  enabled: state.status == QueryStatus.loading,
                  child: _noteListBuilder(context),
                );
              },
            ),
            isMobile(context)
                ? SizedBox(
                    height: 45.sp,
                  )
                : const SizedBox.shrink(),
          ],
        ),
      ),
    );
  }

  Column _noteListBuilder(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.max,
      children: [
        SizedBox(
          height: 10.sp,
        ),
        _filteredNoteList.isNotEmpty
            ? Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: AnimateList(
                  interval: 300.ms,
                  effects: [FadeEffect(duration: 300.ms)],
                  children: [
                    for (int i = 0; i < _filteredNoteList.length; i++)
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 10,
                        ),
                        child: LDNoteCellComponent(
                          onTap: () {
                            context.push(
                              RoutePaths.diaryDetailPath(
                                _filteredNoteList[i].id,
                              ),
                            );
                          },
                          key: ValueKey(i),
                          noteListModelStruct: _filteredNoteList[i],
                          weather: _filteredNoteList[i].weather ?? 'Sunny',
                        ),
                      ),
                  ],
                ),
              )
            : EmptyListWidget(
                title: FlutterI18n.translate(
                  context,
                  'diary.list.empty.title',
                ),
                description: FlutterI18n.translate(
                  context,
                  'diary.list.empty.description',
                ),
                icon: Icon(
                  FontAwesomeIcons.penToSquare,
                  size: 20.sp,
                  color: LDColors.mainGrey,
                ),
              )
      ],
    );
  }
}
