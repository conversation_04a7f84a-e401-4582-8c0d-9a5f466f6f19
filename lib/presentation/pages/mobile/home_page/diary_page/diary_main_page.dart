import 'dart:io';

import 'package:animated_toggle_switch/animated_toggle_switch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:go_router/go_router.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:langda/presentation/nav.dart';
import 'package:langda/providers/process_diary_provider.dart';
import 'package:langda/queries/diary_query.dart';
import 'package:overlay_tooltip/overlay_tooltip.dart';
import 'package:provider/provider.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../../common/LDState.dart';
import '../../../../../states/app_state.dart';
import '../../../../router_constants.dart';
import '../../constants.dart';
import 'component/diary_write_button.dart';
import 'screen/calendar_screen.dart';
import 'screen/diary_list_screen.dart';

class DiaryMainPage extends StatefulWidget {
  final Function changePage;

  const DiaryMainPage({
    super.key,
    required this.changePage,
  });

  @override
  State<DiaryMainPage> createState() => _DiaryMainPageState();
}

class _DiaryMainPageState extends LDState<DiaryMainPage>
    with ShellScreenStateMixin<DiaryMainPage> {
  final InAppReview _inAppReview = InAppReview.instance;

  bool _calendarSwitch = true;

  late TooltipController _tooltipController;
  late AnimationController _animationController;

  late DateTime _focusedDay = DateTime.now();
  late ValueKey<DateTime> _focusedDayKey = ValueKey(_focusedDay);

  Future<void> _requestReview() async {
    try {
      final numberOfDiary = await fetchNumberOfDiaryQuery(
        DateTime.now(),
      ).result;
      if (numberOfDiary.data != null &&
          numberOfDiary.data!.isNotEmpty &&
          numberOfDiary.data!['total'] != null &&
          numberOfDiary.data!['total']! >= 7) {
        if (await _inAppReview.isAvailable()) {
          _inAppReview.openStoreListing(appStoreId: '6695738311');
          await _inAppReview.requestReview();
        }
      }
    } catch (e) {
      print('Error requesting review: $e');
    }
  }

  void toggleCalendar(bool value) async {
    _calendarSwitch = value;
    setState(() {});

    _animationController.animateTo(value ? 1 : 0);
  }

  onDaySelected(DateTime selectedDay) {
    _focusedDay = selectedDay;
    setState(() {});
  }

  _handleTooltipTap() {
    _tooltipController.next();
  }

  @override
  void initState() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _animationController.value = _calendarSwitch ? 1 : 0;
    _tooltipController = TooltipController();

    if (mounted) {
      onDaySelected(_focusedDay);
    }

    _tooltipController.onDone(() async {
      if (!mounted) return;
      try {
        await LDAppState().setTooltipShown('home');
        if (mounted) {
          setState(() {});
        }
      } catch (e) {
        print('Error handling tooltip done: $e');
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      FocusScope.of(context).unfocus();

      if (!LDAppState().isPageToolTipShown('home')) {
        Future.delayed(Duration.zero, () {
          if (mounted && !LDAppState().isPageToolTipShown('home')) {
            _tooltipController.start();
          }
        });
      }
    });

    super.initState();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tooltipController.dispose();
    super.dispose();
  }

  @override
  buildContent(BuildContext context) {
    final DiaryProcessingProvider diaryProcessingProvider =
        context.watch<DiaryProcessingProvider>();
    logBuild('HomeScreen $hashCode');
    return GestureDetector(
      onTap: () => _handleTooltipTap(),
      child: OverlayTooltipScaffold(
        controller: _tooltipController,
        builder: (context) => Scaffold(
            body: SafeArea(
              bottom: false,
              child: Stack(
                children: [
                  SingleChildScrollView(
                    padding: EdgeInsets.all(
                      defaultPadding,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        isMobile(context)
                            ? Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisSize: MainAxisSize.max,
                                //  home - header
                                children: [
                                  SizedBox(
                                    width: Adaptive.sp(15),
                                  ),
                                  Image.asset(
                                    'assets/images/logos/langda.png',
                                    width: 29.sp,
                                  ),
                                  Spacer(),
                                  _customToggleSwitchBuilder(context),
                                  SizedBox(
                                    width: Adaptive.sp(15),
                                  ),
                                ],
                              )
                            : SizedBox.shrink(),
                        isMobile(context)
                            ? Stack(
                                children: [
                                  // * home - calendar
                                  AnimatedBuilder(
                                    animation: _animationController,
                                    builder: (context, child) {
                                      return Opacity(
                                        opacity: _animationController.value,
                                        child: SlideTransition(
                                          position: Tween<Offset>(
                                            begin: Offset(1, 0),
                                            end: Offset(0, 0),
                                          ).animate(_animationController),
                                          child: child,
                                        ),
                                      );
                                    },
                                    child: CalendarScreen(
                                      onDaySelected: onDaySelected,
                                      focusedDay: _focusedDay,
                                      tooltipController: _tooltipController,
                                      showTooltip: !LDAppState()
                                          .isPageToolTipShown('home'),
                                    ),
                                  ),
                                  AnimatedBuilder(
                                    animation: _animationController,
                                    builder: (context, child) {
                                      return Opacity(
                                        opacity: _animationController.value == 1
                                            ? 0
                                            : 1,
                                        child: SlideTransition(
                                          position: Tween<Offset>(
                                            begin: Offset(0, 0),
                                            end: Offset(-1, 0),
                                          ).animate(_animationController),
                                          child: child,
                                        ),
                                      );
                                    },
                                    // * home - note list
                                    child: NoteListScreen(
                                      focusedDay: _focusedDay,
                                    ),
                                  ),
                                ],
                              )
                            : Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Flexible(
                                    child: CalendarScreen(
                                      onDaySelected: onDaySelected,
                                      focusedDay: _focusedDay,
                                      tooltipController: _tooltipController,
                                      showTooltip: !LDAppState()
                                          .isPageToolTipShown('home'),
                                    ),
                                  ),
                                  Flexible(
                                    child: NoteListScreen(
                                      focusedDay: _focusedDay,
                                    ),
                                  ),
                                ],
                              ),
                      ],
                    ),
                  ),
                  if (showInAppReview)
                    FutureBuilder(
                      future: _requestReview(),
                      builder: (_, snapshot) {
                        return Container();
                      },
                    ),
                ],
              ),
            ),
            // * end of body

            // * floating action button
            floatingActionButtonLocation: Platform.isIOS
                ? FloatingActionButtonLocation.centerDocked
                : FloatingActionButtonLocation.centerFloat,
            floatingActionButtonAnimator:
                FloatingActionButtonAnimator.noAnimation,
            floatingActionButton: diaryProcessingProvider.status ==
                    ProcessingStatus.processing
                ? Container()
                : (LDAppState().isPageToolTipShown('home'))
                    ? Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: 17.sp,
                          vertical: 15.sp,
                        ),
                        child: NoteWriteButtonWidget(
                          key: _focusedDayKey,
                          selectedDate: _focusedDay,
                          onTapEnabled: () {
                            context.push('${RoutePaths.diaryCreatePath()}');
                          },
                          onTapNeedSubscription: () {
                            showLDDialog(
                              context,
                              FlutterI18n.translate(
                                context,
                                'diary.write.error.subscribe.expired.description',
                              ),
                              onConfirmed: () => widget.changePage(),
                            );
                          },
                          onTapLoggedOut: () {
                            showLDDialog(
                              context,
                              FlutterI18n.translate(
                                context,
                                'diary.write.error.user.description',
                              ),
                              onConfirmed: () =>
                                  context.go(RoutePaths.loginPath()),
                              onCanceled: () {},
                            );
                          },
                        ),
                      )
                    : OverlayTooltipItem(
                        displayIndex: 0,
                        tooltipVerticalPosition: TooltipVerticalPosition.TOP,
                        tooltipHorizontalPosition:
                            TooltipHorizontalPosition.CENTER,
                        tooltip: (_tooltipController) {
                          return Container(
                            padding: EdgeInsets.all(10.sp),
                            child: Text(
                              FlutterI18n.translate(
                                context,
                                'tooltip.write.0',
                              ),
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 15.sp,
                              ),
                            ),
                          );
                        },
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: 17.sp,
                            vertical: 15.sp,
                          ),
                          child: SizedBox(
                            height: 50,
                            child: NoteWriteButtonWidget(
                              key: _focusedDayKey,
                              selectedDate: _focusedDay,
                              onTapEnabled: () {},
                              onTapNeedSubscription: () {},
                              onTapLoggedOut: () {},
                            ),
                          ),
                        ),
                      )),
      ),
    );
  }

  Padding _customToggleSwitchBuilder(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(0.0),
      child: AnimatedToggleSwitch<bool>.size(
        current: !_calendarSwitch,
        values: [true, false],
        height: 48,
        borderWidth: 1,
        style: ToggleStyle(
          backgroundColor: Colors.white,
          borderRadius: BorderRadius.circular(4),
        ),
        iconOpacity: 1,
        iconBuilder: (value) {
          return Image.asset(
              value
                  ? 'assets/images/icons/1-512-g.png'
                  : 'assets/images/icons/30-512-g.png',
              width: 19.sp,
              semanticLabel: value
                  ? FlutterI18n.translate(
                      context,
                      'switch.page.toNoteList',
                    )
                  : FlutterI18n.translate(
                      context,
                      'switch.page.toHome',
                    ));
        },
        foregroundIndicatorIconBuilder: (context, value) {
          return Image.asset(
            value.current
                ? 'assets/images/icons/1-512-w.png'
                : 'assets/images/icons/30-512-w.png',
            width: 19.sp,
            semanticLabel: value.current
                ? FlutterI18n.translate(
                    context,
                    'switch.page.toNoteList',
                  )
                : FlutterI18n.translate(
                    context,
                    'switch.page.home',
                  ),
          );
        },
        spacing: 0,
        indicatorAnimationType: AnimationType.none,
        iconAnimationType: AnimationType.none,
        styleAnimationType: AnimationType.none,
        onChanged: (value) {
          toggleCalendar(!value);
        },
      ),
    );
  }
}
