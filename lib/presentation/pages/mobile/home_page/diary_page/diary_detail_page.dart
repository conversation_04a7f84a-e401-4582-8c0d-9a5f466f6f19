import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:clipboard/clipboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import 'package:langda/common/LDState.dart';
import 'package:langda/queries/auth_query.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../../api/models/explanation_language_preference.dart';
import '../../../../../common/phrases/phrases_component.dart';
import '../../../../../queries/diary_query.dart';
import '../../../../../utils/weather_handler.dart';
import '../../../component.dart';
import '../../constants.dart';
import 'component/diary_switch_control.dart';

class DiaryDetailPageWidget extends StatefulWidget {
  final String entryId;

  const DiaryDetailPageWidget({
    super.key,
    required this.entryId,
  });

  @override
  State<DiaryDetailPageWidget> createState() => _DiaryDetailPageWidgetState();
}

class _DiaryDetailPageWidgetState extends LDState<DiaryDetailPageWidget> {
  bool _showCorrection = true;

  ValueChanged<bool> switchShowCorrection() {
    return (bool value) {
      setState(() {
        _showCorrection = value;
      });
    };
  }

  @override
  void initState() {
    pageName = 'Diary Detail';
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget buildContent(BuildContext context) {
    return QueryBuilder(
      query: getDiaryQuery(
        widget.entryId,
      ),
      builder: (context, state) {
        final diary = state.data;
        return Skeletonizer(
          enabled: state.status == QueryStatus.loading,
          child: Scaffold(
            appBar: PreferredSize(
              preferredSize: const Size.fromHeight(50),
              child: appBarWrapper(
                  '',
                  null,
                  NoteSwitchControl(
                    value: _showCorrection,
                    onChanged: switchShowCorrection(),
                  )),
            ),
            body: SafeArea(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(
                  Adaptive.sp(20),
                ),
                child: diary != null
                    ? Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                              bottom: Adaptive.sp(10),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Image.asset(
                                      'assets/images/icons/30-512-g.png',
                                      width: Adaptive.w(3),
                                    ),
                                    SizedBox(
                                      width: Adaptive.w(2),
                                    ),
                                    Text(
                                      DateFormat(
                                        'dd MMM yyyy',
                                        'en_US',
                                      ).format(
                                        diary.date!,
                                      ),
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        color: LDColors.mainGrey,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  width: Adaptive.w(4),
                                ),
                                Padding(
                                  padding: EdgeInsets.all(
                                    Adaptive.sp(10),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      WeatherIcon.getWeatherIcon(
                                          diary.weather!),
                                      SizedBox(
                                        width: Adaptive.w(2),
                                      ),
                                      Text(
                                        FlutterI18n.translate(
                                          context,
                                          "weather.${diary.weather!}",
                                        ),
                                        style: TextStyle(
                                          fontSize: 14.sp,
                                          color: LDColors.mainGrey,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const Spacer(),
                                if (diary.correctedVersion != null &&
                                    diary.correctedVersion!.content.isNotEmpty)
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      InkWell(
                                        child: Icon(
                                          FontAwesomeIcons.copy,
                                          size: 20,
                                          color: LDColors.darkHoverGrey,
                                        ),
                                        onTap: () {
                                          FlutterClipboard.copy(diary
                                                  .correctedVersion!.content)
                                              .then(
                                            (value) {
                                              // show success message
                                              showLDToaster(
                                                context,
                                                FlutterI18n.translate(
                                                  context,
                                                  'diary.list.detail.copy.success',
                                                ),
                                                ToastType.success,
                                              );
                                            },
                                          );
                                        },
                                      )
                                    ],
                                  ),
                              ],
                            ),
                          ),

                          // * content
                          QueryBuilder(
                            query: fetchUserInfoQuery(),
                            builder: (context, state) => Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                PhrasesComponent(
                                  entryId: diary.id,
                                  originalVersionId: diary.originalVersion!.id,
                                  correctedVersionId:
                                      diary.correctedVersion?.id,
                                  originalContent:
                                      diary.originalVersion!.content,
                                  correctedContent:
                                      diary.correctedVersion?.content,
                                  showCorrection: _showCorrection,
                                  explanationLanguagePreference: state.data
                                          ?.explanationLanguagePreference ??
                                      ExplanationLanguagePreference
                                          .nativeLanguage,
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: Adaptive.sp(50),
                          ),
                        ],
                      )
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          const Text(
                            'No diary entry found.',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SizedBox(
                            height: Adaptive.sp(20),
                          ),
                          const Icon(
                            Icons.error_outline,
                            size: 50,
                            color: Colors.red,
                          ),
                        ],
                      ),
              ),
            ),
          ),
        );
      },
    );
  }
}
