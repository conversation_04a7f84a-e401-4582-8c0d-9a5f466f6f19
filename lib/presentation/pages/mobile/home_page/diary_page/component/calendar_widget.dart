import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import 'package:langda/states/app_state.dart';
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../../../../../backend/model/diary_model.dart';
import '../../../../../../queries/diary_query.dart';
import '../../../constants.dart';

class CalendarWidget extends StatefulWidget {
  CalendarWidget({
    super.key,
    required this.onDaySelected,
    required this.focusedDay,
    required this.languageCode,
  });

  final Future Function(DateTime) onDaySelected;
  final DateTime focusedDay;
  final String languageCode;

  @override
  State<CalendarWidget> createState() => _CalendarWidgetState();
}

class _CalendarWidgetState extends State<CalendarWidget> {
  late DateTime _focusedDay;
  final DateTime today = DateTime.now();
  List<String> _days = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];

  @override
  void initState() {
    super.initState();

    _focusedDay = widget.focusedDay;
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    if (selectedDay.isAfter(
      DateTime(
        focusedDay.year,
        focusedDay.month + 1,
        0,
      ),
    )) {
      _focusedDay = DateTime(focusedDay.year, focusedDay.month + 1, 1);
    } else {
      _focusedDay = selectedDay;
    }
    widget.onDaySelected(selectedDay);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final LDAppState appState = context.watch<LDAppState>();
    DateFormat daysFormater = DateFormat.E(widget.languageCode);
    return Column(
      children: [
        // * Calendar Header
        _customCalendarHeaderBuilder(context),
        SizedBox(
          height: 13,
        ),
        _customCalendarDateNameBuilder(
          appState.getLocale(),
        ),
        SizedBox(
          height: 10,
        ),
        Container(
          padding: EdgeInsets.only(
            top: 14,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              // 달력 밑 그림자
              BoxShadow(
                color: LDColors.lightGrey.withValues(alpha: 0.6),
                spreadRadius: 8,
                blurRadius: 8,
                offset: const Offset(0, 0),
              ),
            ],
            borderRadius: BorderRadius.circular(10),
          ),
          child: QueryBuilder(
            query: fetchDiaryListWithDateQuery(
              _focusedDay.year,
              _focusedDay.month,
            ),
            builder: (context, state) {
              final List<DiaryListModelStruct> noteList = state.data ?? [];
              return TableCalendar(
                availableGestures: AvailableGestures.none,
                locale: FlutterI18n.currentLocale(context)?.languageCode,
                headerVisible: false,
                daysOfWeekVisible: false,
                calendarFormat: CalendarFormat.month,
                focusedDay: _focusedDay,
                selectedDayPredicate: (day) => isSameDay(day, _focusedDay),
                firstDay: DateTime.utc(today.year - 2, today.month, today.day),
                lastDay: DateTime.utc(today.year + 2, today.month, today.day),
                daysOfWeekHeight: 40,
                onDaySelected: _onDaySelected,
                calendarBuilders: CalendarBuilders(
                  dowBuilder: (context, day) => Container(
                    alignment: Alignment.center,
                    child: Text(
                      daysFormater.format(day),
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w400,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  outsideBuilder: (context, day, _focusedDay) =>
                      dateCellBuilder(day, null,
                          showBackground: false, isOutside: true),
                  defaultBuilder: (context, day, _focusedDay) {
                    if (_focusedDay.month == DateTime.now().month) {
                      return dateCellBuilder(
                        day,
                        null,
                        showBackground: false,
                        isOutside: false,
                      );
                    } else {
                      return dateCellBuilder(
                        day,
                        null,
                        showBackground: false,
                        isOutside: true,
                      );
                    }
                  },
                  todayBuilder: (context, day, _focusedDay) =>
                      dateCellBuilder(day, null),
                  selectedBuilder: (context, day, _focusedDay) =>
                      dateCellBuilder(
                          day, Theme.of(context).colorScheme.primary),
                  markerBuilder: (context, day, events) {
                    if (noteList.isEmpty) {
                      return const SizedBox();
                    }

                    return noteList
                            .any((element) => isSameDay(element.date!, day))
                        ? dateCellBuilder(day, null)
                        : const SizedBox();
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Row _customCalendarDateNameBuilder(Locale locale) {
    return Row(
      children: [
        for (int i = 0; i < _days.length; i++)
          Expanded(
            child: Container(
              alignment: Alignment.center,
              child: Text(
                FlutterI18n.translate(
                  context,
                  'days.${_days[i]}',
                ),
                locale: locale,
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Row _customCalendarHeaderBuilder(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        IconButton(
          onPressed: () {
            final newFocusedDay = DateTime(
              _focusedDay.year,
              _focusedDay.month - 1,
              _focusedDay.day,
            );
            if (newFocusedDay.year <= today.year - 2) {
              return;
            } else {
              setState(() {
                _focusedDay = newFocusedDay;
                widget.onDaySelected(newFocusedDay);
              });
            }
          },
          icon: Icon(
            semanticLabel: FlutterI18n.translate(
              context,
              'calendar.button.prev',
            ),
            FontAwesomeIcons.chevronLeft,
            size: 16,
          ),
        ),
        const Spacer(),
        Text(
          textAlign: TextAlign.center,
          DateFormat('yyyy. MM').format(_focusedDay),
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () {
            final newFocusedDay = _focusedDay.add(const Duration(days: 30));
            setState(() {
              if (newFocusedDay.year <= today.year - 2) {
                return;
              } else {
                _focusedDay = newFocusedDay;
                widget.onDaySelected(newFocusedDay);
              }
            });
          },
          icon: Icon(
            semanticLabel: FlutterI18n.translate(
              context,
              'calendar.button.next',
            ),
            FontAwesomeIcons.chevronRight,
            size: 16,
          ),
        ),
      ],
    );
  }

  Column dateCellBuilder(DateTime day, Color? selectedBackgroundColor,
      {bool showBackground = true, bool isOutside = false}) {
    final double containerWidth = 28;
    final double containerHeight = 28;
    final double dayFontSize = 12;

    bool isSelect = day.day == _focusedDay.day;
    return Column(
      children: [
        Container(
          alignment: Alignment.center,
          width: containerWidth,
          height: containerHeight,
          decoration: BoxDecoration(
            color: isSelect
                ? selectedBackgroundColor
                : showBackground
                    ? DateUtils.isSameDay(day, DateTime.now())
                        ? LDColors.mainGrey
                        : LDColors.lightGrey
                    : Colors.transparent,
            shape: BoxShape.circle,
          ),
          child: Text(
            day.day.toString(),
            style: TextStyle(
              fontFamily: 'Pretendard',
              fontWeight: FontWeight.w600,
              fontSize: dayFontSize,
              color: isOutside
                  ? LDColors.subGrey
                  : (DateUtils.isSameDay(day, DateTime.now()) && !isSelect)
                      ? Colors.white
                      : day.weekday == DateTime.sunday
                          ? LDColors.mainRed
                          : day.weekday == DateTime.saturday
                              ? LDColors.mainBlue
                              : Colors.black,
            ),
          ),
        ),
        Text(
          DateUtils.isSameDay(day, DateTime.now()) ? 'Today' : '',
          style: TextStyle(
            fontFamily: 'Pretendard',
            fontWeight: FontWeight.w700,
            fontSize: 11,
            color: LDColors.mainGrey,
          ),
        )
      ],
    );
  }
}
