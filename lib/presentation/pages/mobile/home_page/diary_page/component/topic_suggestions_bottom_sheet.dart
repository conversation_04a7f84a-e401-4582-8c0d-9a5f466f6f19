import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';

import '../../../constants.dart';

class TopicSuggestionsBottomSheet extends StatefulWidget {
  final void Function(String, BuildContext) onSelected;
  TopicSuggestionsBottomSheet({super.key, required this.onSelected});

  @override
  State<TopicSuggestionsBottomSheet> createState() =>
      _TopicSuggestionsBottomSheetState();
}

class _TopicSuggestionsBottomSheetState
    extends State<TopicSuggestionsBottomSheet> {
  String _selectedTopic = 'daily_life';
  final Map<String, Color> topics = {
    'daily_life': Colors.blue[100] ?? Colors.blue,
    'emotions&reflections': Colors.green[100] ?? Colors.green,
    'relationships': Colors.red[100] ?? Colors.red,
    'goals&growth': Colors.yellow[100] ?? Colors.yellow,
    'createivity&hobbies': Colors.purple[100] ?? Colors.purple,
  };

  String _selectedTopicDetail = '';

  List<String> topicsList = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      for (var i = 0; i < 5; i++) {
        final String content = FlutterI18n.translate(
          context,
          'diary.write.helper.topic.list.daily_life.$i',
        );
        if (content != 'diary.write.helper.topic.list.daily_life.$i') {
          topicsList.add(content);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              FlutterI18n.translate(
                context,
                'diary.write.helper.topic.title',
              ),
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              FlutterI18n.translate(
                context,
                'diary.write.helper.topic.description',
              ),
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            // 카테고리
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: topics.length,
                itemBuilder: (context, index) {
                  String key = topics.keys.elementAt(index);
                  final String translatedKey = FlutterI18n.translate(
                    context,
                    'diary.write.helper.topic.list.$key.title',
                  );
                  return GestureDetector(
                    onTap: () {
                      topicsList.clear();
                      for (var i = 0; i < 5; i++) {
                        final String content = FlutterI18n.translate(
                          context,
                          'diary.write.helper.topic.list.$key.$i',
                        );
                        if (content !=
                            'diary.write.helper.topic.list.$key.$i') {
                          topicsList.add(content);
                        }
                      }
                      _selectedTopic = key;
                      setState(() {});
                    },
                    child: _topicCatagoryBuilder(translatedKey, key),
                  );
                },
              ),
            ),
            Expanded(
              child: ListView.builder(
                itemCount: topicsList.length, // Replace with actual topic count
                itemBuilder: (context, index) {
                  final bool isSelected =
                      _selectedTopicDetail == index.toString();
                  final String content = topicsList[index];
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 2,
                    ),
                    margin: const EdgeInsets.only(bottom: 8),
                    decoration: BoxDecoration(
                      color:
                          isSelected ? LDColors.mainLime : LDColors.lightGrey,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: ListTile(
                      title: Text('$content'),
                      onTap: () {
                        setState(() {
                          _selectedTopicDetail = index.toString();
                          widget.onSelected(content, context);
                        });
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Container _topicCatagoryBuilder(String translatedKey, String key) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 2,
      ),
      margin: const EdgeInsets.only(right: 8),
      decoration: BoxDecoration(
        color: _selectedTopic == key ? LDColors.mainLime : Colors.transparent,
        border: Border.all(
          color: _selectedTopic == key ? LDColors.mainLime : LDColors.mainGrey,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Center(
        child: Text(
          translatedKey,
          style: TextStyle(
            color: _selectedTopic == key ? Colors.black : Colors.black,
            fontSize: 16,
          ),
        ),
      ),
    );
  }
}
