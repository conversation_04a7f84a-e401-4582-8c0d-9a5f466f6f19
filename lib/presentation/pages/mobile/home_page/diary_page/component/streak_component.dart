import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:langda/presentation/pages/mobile/empty_list_widget.dart';
import 'package:langda/states/app_state.dart';

import '../../../../../../backend/model/diary_model.dart';
import '../../../../../../queries/diary_query.dart';
import '../../../../component.dart';
import '../../../constants.dart';

// 스트릭 모델
class StreakModel {
  final bool isStreak;
  final DateTime? date;
  bool isStartOfStreak;
  bool isEndOfStreak;

  StreakModel({
    required this.isStreak,
    this.date,
    this.isStartOfStreak = false,
    this.isEndOfStreak = false,
  });
}

// recorded날짜 리스트를 받아 streakModelList를 반환해주는 함수
List<StreakModel?> Function(List<DateTime> recordedDates, DateTime today)
    generateStreakModelList = (recordedDates, today) {
  List<StreakModel?> streakModelList = [];

  // 기록이 없으면 빈 리스트를 반환
  if (recordedDates.isEmpty) {
    return streakModelList;
  }

  // 해당 월의 첫 날과 마지막 날을 구함
  final DateTime firstDayOfMonth = DateTime(today.year, today.month, 1);
  final DateTime lastDayOfMonth = DateTime(today.year, today.month + 1, 0);

  // 첫 날이 무슨 요일인지 구함
  int dayOfWeek = firstDayOfMonth.weekday % 7;
  final int daysInMonth = lastDayOfMonth.day;

  // 해당 월의 첫 월요일 날짜를 구함
  final DateTime firstMonday = firstDayOfMonth.subtract(
    Duration(days: dayOfWeek - 1),
  );
  // 해당 월의 첫 일요일 날짜를 구함
  final DateTime firstSunday = firstDayOfMonth.subtract(
    Duration(days: dayOfWeek),
  );

  for (int i = 0; i < 35; i++) {
    if (i < dayOfWeek) {
      streakModelList.add(null);
      continue;
    } else if (i >= daysInMonth + dayOfWeek) {
      streakModelList.add(null);
      continue;
    } else {
      // i에 1을 더해주면 해당 날짜가 나옴
      final int newIndex = i + 1 - dayOfWeek;
      // 해당 날짜에 기록이 있는지 확인
      final bool _isStreak =
          recordedDates.any((element) => element.day == newIndex);
      final StreakModel streakModel = StreakModel(
        isStreak: _isStreak,
        date: DateTime(
          today.year,
          today.month,
          newIndex,
        ),
        isStartOfStreak: recordedDates.first.day <= newIndex &&
                recordedDates.last.day >= newIndex
            ? recordedDates.any((element) => element.day == newIndex - 1) ==
                    false ||
                newIndex == firstMonday.day
            : false,
        isEndOfStreak: recordedDates.first.day <= newIndex &&
                recordedDates.last.day >= newIndex
            ? recordedDates.any((element) => element.day == newIndex + 1) ==
                    false ||
                newIndex % 7 == firstSunday.day
            : false,
      );
      streakModelList.add(streakModel);
    }
  }

  return streakModelList;
};

class StreakComponent extends StatelessWidget {
  const StreakComponent(
      {super.key, required this.streakCount, required this.totalCount});
  final int streakCount;
  final int totalCount;

  @override
  Widget build(BuildContext context) {
    const List<String> _days = [
      'sun',
      'mon',
      'tue',
      'wed',
      'thu',
      'fri',
      'sat'
    ];
    final today = DateTime.now();

    Container _emptyStreakBuilder({
      required String month,
    }) =>
        Container(
          height: 300,
          child: EmptyListWidget(
            title: FlutterI18n.translate(
              context,
              'user.record.streak.message.empty',
              translationParams: {
                'month': month,
              },
            ),
            description: FlutterI18n.translate(
              context,
              'diary.list.empty.description',
            ),
          ),
        );

    var daysToWordHeader = Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 20,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          for (int i = 0; i < _days.length; i++)
            Expanded(
              child: Container(
                alignment: Alignment.center,
                child: Text(
                  FlutterI18n.translate(
                    context,
                    'days.${_days[i]}',
                  ),
                  locale: LDAppState().getLocale(),
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w400,
                    color: Colors.black,
                    decoration: TextDecoration.none,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.all(20),
        constraints: const BoxConstraints(
          minWidth: 300,
          minHeight: 300,
          maxWidth: 500,
          maxHeight: 800,
        ),
        child: Container(
          padding: const EdgeInsets.only(
            left: 20,
            right: 20,
            top: 10,
            bottom: 20,
          ),
          constraints: const BoxConstraints(
            minWidth: 100,
            minHeight: 100,
            maxWidth: double.infinity,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(13),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: LDSpinIconButton(
                      semanticLabel: FlutterI18n.translate(context, 'close'),
                      icon: ImageIcon(
                        AssetImage('assets/images/icons/x-close_1.5.png'),
                        color: LDColors.mainGrey,
                      ),
                      onPressed: () => Navigator.of(context).canPop()
                          ? Navigator.of(context).pop()
                          : null,
                      backgroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
              Text(
                FlutterI18n.translate(
                  context,
                  'user.record.streak.title',
                ),
                style: TextStyle(
                  fontSize: 25,
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                ),
              ),
              SizedBox(
                height: 20,
              ),
              daysToWordHeader,
              QueryBuilder(
                query: fetchDiaryListWithDateQuery(
                  today.year,
                  today.month,
                ),
                builder: (context, state) {
                  final List<DiaryListModelStruct> noteList = state.data ?? [];
                  if (noteList.isEmpty) {
                    return _emptyStreakBuilder(
                      month: today.month.toString(),
                    );
                  }

                  final List<DateTime> recordedDates = noteList
                      .map((e) => e.date!)
                      .where((e) => e.month == today.month)
                      .toList();

                  if (recordedDates.isNotEmpty) {
                    recordedDates.sort((a, b) => a.compareTo(b));

                    final List<StreakModel?> streakModelList =
                        generateStreakModelList(recordedDates, today);
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        GridView.builder(
                          shrinkWrap: true,
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 7,
                            childAspectRatio: 1.0,
                            mainAxisSpacing: 0,
                            crossAxisSpacing: 0,
                          ),
                          physics: NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            if (streakModelList.length > index &&
                                streakModelList[index] != null)
                              return streakModelList[index]!.isStreak
                                  ? _streakCellBuilder(
                                      streakModel: streakModelList[index]!,
                                      color: Theme.of(context).primaryColor,
                                      backgroundColor:
                                          Theme.of(context).primaryColor,
                                    )
                                  : _streakCellBuilder(
                                      streakModel: streakModelList[index]!,
                                      color: null,
                                      backgroundColor: null,
                                    );

                            return Container();
                          },
                          itemCount: 35,
                        ),
                      ],
                    );
                  } else {
                    return _emptyStreakBuilder(
                      month: today.month.toString(),
                    );
                  }
                },
              ),
              SizedBox(
                height: 20,
              ),
              Text(
                FlutterI18n.translate(
                  context,
                  'user.record.streak.count',
                  translationParams: {
                    'count': streakCount.toString(),
                  },
                ),
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                  decoration: TextDecoration.none,
                ),
              ),
              SizedBox(
                height: 8,
              ),
              if (streakCount > 0)
                Text(
                  FlutterI18n.translate(
                    context,
                    'user.record.streak.message.0',
                  ),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Colors.black,
                    decoration: TextDecoration.none,
                  ),
                )
              else
                Text(
                  FlutterI18n.translate(
                    context,
                    'user.record.streak.message.empty',
                  ),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Colors.black,
                    decoration: TextDecoration.none,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // streak 한 칸을 만들어주는 함수
  Container _streakCellBuilder({
    required StreakModel streakModel,
    required Color? color,
    required Color? backgroundColor,
  }) {
    final bool isLastOfStreak = streakModel.isEndOfStreak;
    final bool isFirstOfStreak = streakModel.isStartOfStreak;
    final Color streakColor = color ?? LDColors.lightGrey;
    final Color streakBackgroundColor = backgroundColor ?? LDColors.lightGrey;
    return Container(
      alignment: Alignment.center,
      width: 30,
      height: 30,
      decoration: BoxDecoration(
          color: streakBackgroundColor.withValues(alpha: 0.2),
          borderRadius: BorderRadius.only(
            topRight: isLastOfStreak ? Radius.circular(22) : Radius.zero,
            bottomRight: isLastOfStreak ? Radius.circular(22) : Radius.zero,
            topLeft: isFirstOfStreak ? Radius.circular(22) : Radius.zero,
            bottomLeft: isFirstOfStreak ? Radius.circular(22) : Radius.zero,
          )),
      child: Container(
        width: 25,
        height: 25,
        decoration: BoxDecoration(
          color: streakColor,
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}
