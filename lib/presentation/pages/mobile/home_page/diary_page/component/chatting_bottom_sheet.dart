import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:langda/backend/langda_api_client.dart';
import 'package:langda/utils/my_logger.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:uuid/uuid.dart';

import '../../../../../../api/models/phrase_help_request_body.dart';
import '../../../constants.dart';

class ChattingBottomSheet extends StatefulWidget {
  final List<types.Message> messages;
  const ChattingBottomSheet({super.key, required this.messages});

  @override
  State<ChattingBottomSheet> createState() => _ChattingBottomSheetState();
}

class _ChattingBottomSheetState extends State<ChattingBottomSheet> {
  final _user = const types.User(id: 'user_id');
  final _langda = const types.User(id: 'langda_id', firstName: 'Langda');
  final _uuid = const Uuid();

  late final TextEditingController _textEditingController;

  List<types.User> typingUsers = [];

  @override
  void initState() {
    super.initState();
    _textEditingController = TextEditingController();

    _textEditingController.addListener(() {
      final oldText = _textEditingController.text;

      if (_textEditingController.text.length > 200) {
        _textEditingController.text = oldText;
      }
    });
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    super.dispose();
  }

  void _addMessage(types.Message message, [bool simulateResponse = true]) {
    setState(() {
      widget.messages.insert(0, message);
    });
    if (simulateResponse && message.author.id == _user.id) {
      _simulateLangdaResponse(message);
    }
  }

  Future<void> _handleSendPressed(types.PartialText message) async {
    final textMessage = types.TextMessage(
      author: _user,
      createdAt: null,
      id: _uuid.v4(),
      text: message.text,
    );
    if (kDebugMode) {
      _addMessage(textMessage);
    } else {
      _addMessage(textMessage, false);
    }

    try {
      setState(() {
        typingUsers.add(_langda);
      });
      final PhraseHelpRequestBody body = PhraseHelpRequestBody(
        query: message.text,
      );

      final response =
          await LangdaApiClient.client.diary.postDiaryPhraseHelp(body: body);

      final resTextMessage = types.TextMessage(
        author: _langda,
        createdAt: null,
        id: _uuid.v4(),
        text: response.response,
      );

      _addMessage(resTextMessage, false);
    } catch (e) {
      myLog('Error sending message: $e', LogLevel.error);
    }

    setState(() {
      typingUsers.remove(_langda);
    });

    myLog('User message sent: ${message.text}', LogLevel.info);
  }

  void _simulateLangdaResponse(types.Message userMessage) {
    Future.delayed(const Duration(milliseconds: 3000), () {
      if (mounted) {
        final langdaResponse = types.TextMessage(
          author: _langda,
          createdAt: null,
          id: _uuid.v4(),
          text: userMessage is types.TextMessage
              ? 'Okay, let me check "${userMessage.text.characters.take(20)}"...' // Example response
              : 'Thinking...',
        );

        _addMessage(langdaResponse, false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SafeArea(
        top: false,
        bottom: true,
        child: Container(
          height: 85.h,
          padding: EdgeInsets.only(top: 18.sp),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(Adaptive.sp(20)),
              topRight: Radius.circular(Adaptive.sp(20)),
            ),
          ),
          child: Column(
            children: [
              // Handle
              Container(
                width: 40.sp,
                height: 5.sp,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(10.sp),
                ),
              ),
              const SizedBox(height: 15),
              // Title
              Text(
                FlutterI18n.translate(
                  context,
                  'diary.write.helper.chat.title',
                ),
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 10),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: Adaptive.sp(10),
                    vertical: Adaptive.sp(10),
                  ),
                  child: Chat(
                    l10n: ChatL10nEn(
                      inputPlaceholder: FlutterI18n.translate(
                        context,
                        'diary.write.helper.chat.placeholder',
                      ),
                    ),
                    messages: widget.messages,
                    onSendPressed: _handleSendPressed,
                    user: _user,
                    systemMessageBuilder: (p0) => const SizedBox(),
                    typingIndicatorOptions: TypingIndicatorOptions(
                      typingUsers: typingUsers,
                    ),

                    theme: DefaultChatTheme(
                      backgroundColor: Colors.white,
                      primaryColor: LDColors.mainLime,
                      secondaryColor: LDColors.subGrey,
                      messageInsetsVertical: 5,
                      messageInsetsHorizontal: 14,

                      // Input area theme
                      inputBackgroundColor: Colors.grey[100]!,
                      inputTextColor: Colors.black,
                      inputBorderRadius: BorderRadius.circular(Adaptive.sp(30)),
                      inputPadding: EdgeInsets.symmetric(
                        horizontal: Adaptive.sp(8),
                      ),
                      inputTextStyle: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),

                      // Send Button theme
                      sendButtonIcon: Container(
                        width: 26.sp,
                        height: 26.sp,
                        decoration: BoxDecoration(
                          color: LDColors.mainLime,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.arrow_upward,
                          color: LDColors.mainGrey,
                          size: 20.sp,
                        ),
                      ),
                      sendButtonMargin: EdgeInsets.only(
                        left: Adaptive.sp(10),
                        bottom: Adaptive.sp(10),
                        top: Adaptive.sp(10),
                      ),
                      // sent message
                      sentMessageBodyTextStyle: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                      receivedMessageBodyTextStyle: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),

                    inputOptions: InputOptions(
                      sendButtonVisibilityMode: SendButtonVisibilityMode.always,
                    ),

                    // Empty state builder
                    emptyState: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 30.sp),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            FlutterI18n.translate(
                              context,
                              'diary.write.helper.chat.description',
                            ),
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.grey[600],
                              height: 1.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Hide avatars and names as they are not in the image
                    showUserAvatars: false,
                    showUserNames: false,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
