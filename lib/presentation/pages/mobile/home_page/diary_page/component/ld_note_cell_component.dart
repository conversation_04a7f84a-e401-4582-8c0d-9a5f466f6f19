import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:langda/backend/model/diary_model.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../../../utils/weather_handler.dart';
import '../../../constants.dart';

class LDNoteCellComponent extends StatelessWidget {
  LDNoteCellComponent({
    super.key,
    required this.noteListModelStruct,
    required this.onTap,
    required this.weather,
  });

  final DiaryListModelStruct? noteListModelStruct;
  final Function() onTap;
  final String weather;

  @override
  Widget build(BuildContext context) {
    return noteListModelStruct == null
        ? Container()
        : GestureDetector(
            onTap: onTap,
            child: Container(
              padding: EdgeInsets.all(
                Adaptive.sp(20),
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  // 노트 카드 그림자
                  BoxShadow(
                    color: LDColors.lightGrey.withValues(alpha: 0.6),
                    spreadRadius: 8,
                    blurRadius: 8,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // * month
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: Adaptive.w(1),
                    ),
                    child: Column(
                      children: [
                        Text(
                          DateFormat('dd')
                              .format(noteListModelStruct!.date!.toLocal()),
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 19.sp,
                            color: noteListModelStruct!.date?.weekday ==
                                    DateTime.sunday
                                ? LDColors.mainRed
                                : noteListModelStruct!.date?.weekday ==
                                        DateTime.saturday
                                    ? LDColors.mainBlue
                                    : Colors.black,
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Text(
                          DateFormat('MMM').format(
                              noteListModelStruct!.date?.toLocal() ??
                                  DateTime.now()),
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: LDColors.mainGrey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (noteListModelStruct!.originalVersion != null)
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                        ),
                        child: Text(
                          softWrap: true,
                          maxLines: 3,
                          noteListModelStruct!.originalVersion!.content!,
                          style: TextStyle(
                            height: 1.5,
                            fontSize: 14,
                            color: LDColors.mainGrey,
                            overflow: TextOverflow.ellipsis,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    ),

                  WeatherIcon.getWeatherIcon(weather),
                ],
              ),
            ),
          );
  }
}
