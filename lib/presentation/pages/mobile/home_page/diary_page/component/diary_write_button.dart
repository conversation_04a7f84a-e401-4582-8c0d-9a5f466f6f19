import 'dart:convert';

import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../../../../../backend/model/user_model.dart';
import '../../../../../../backend/service/auth_service.dart';
import '../../../../../../providers/process_diary_provider.dart';
import '../../../../../../queries/diary_query.dart';
import '../../../../component.dart';
import '../../../constants.dart';

class NoteWriteButtonModel {
  final String currentEntryDate;
  final DateTime selectedDate;
  final bool isLoggedIn;
  final SubscriptionStatus subscriptionStatus;

  NoteWriteButtonModel({
    required this.currentEntryDate,
    required this.selectedDate,
    required this.subscriptionStatus,
  }) : isLoggedIn = AuthService().isLoggedIn();

  bool get shouldShowDoneButton =>
      isLoggedIn && _isSelectedDateCurrentEntryDate;

  bool get shouldShowNoButton => isLoggedIn && !_isSelectedDateCurrentEntryDate;

  bool get shouldShowSubmitButton =>
      !shouldShowDoneButton && !shouldShowNoButton;

  bool get _isSelectedDateCurrentEntryDate {
    final formattedSelectedDate = DateFormat('yyyy-MM-dd').format(selectedDate);
    return formattedSelectedDate.allMatches(currentEntryDate).isNotEmpty;
  }

  @override
  String toString() {
    return jsonEncode({
      'currentEntryDate': currentEntryDate,
      'selectedDate': selectedDate.toString(),
      'isLoggedIn': isLoggedIn,
      'subscriptionStatus': subscriptionStatus.toString(),
    });
  }
}

class NoteWriteButtonWidget extends StatelessWidget {
  final Function() onTapEnabled;
  final Function() onTapNeedSubscription;
  final Function() onTapLoggedOut;
  final DateTime selectedDate;

  const NoteWriteButtonWidget({
    Key? key,
    required this.onTapEnabled,
    required this.onTapNeedSubscription,
    required this.onTapLoggedOut,
    required this.selectedDate,
  }) : super(key: key);

  void _handleTap(NoteWriteButtonModel model) {
    if (!model.isLoggedIn) {
      onTapLoggedOut();
    } else if (model.subscriptionStatus == SubscriptionStatus.expired &&
        showSubscription) {
      onTapNeedSubscription();
    } else {
      onTapEnabled();
    }
  }

  @override
  Widget build(BuildContext context) {
    final DiaryProcessingProvider diaryProcessingProvider =
        context.watch<DiaryProcessingProvider>();
    return QueryBuilder(
        query: hasEntryForEntryDateQuery(
            DateFormat('yyyy-MM-dd').format(DateTime.now())),
        builder: (context, state) {
          final hasEntryForEntryDate = state.data;

          return QueryBuilder<NoteWriteButtonModel>(
              query: diaryWriteButtonModelQuery(selectedDate),
              builder: (context, state) {
                final modelData = state.data;

                if (state.status == QueryStatus.loading ||
                    state.status == QueryStatus.error ||
                    modelData == null) {
                  if (AuthService().isLoggedIn()) {
                    // If the user is logged in, most likely is no button
                    return Container();
                  } else {
                    // If the user is not logged in, most likely is submit button

                    return _submitButtonBuilder(
                      context,
                      () => {},
                    );
                  }
                }

                if (modelData.shouldShowSubmitButton &&
                        diaryProcessingProvider.status !=
                            ProcessingStatus.processing &&
                        diaryProcessingProvider.status !=
                            ProcessingStatus.completed ||
                    hasEntryForEntryDate == null ||
                    !hasEntryForEntryDate) {
                  return _submitButtonBuilder(
                    context,
                    () => _handleTap(modelData),
                  );
                }

                if (modelData.shouldShowDoneButton ||
                    diaryProcessingProvider.status ==
                        ProcessingStatus.completed ||
                    hasEntryForEntryDate) {
                  return _doneButtonBuilder(context);
                }

                if (modelData.shouldShowNoButton) {
                  return Container();
                }

                return Container();
              });
        });
  }

  Padding _submitButtonBuilder(BuildContext context, Function() onTap) {
    return Padding(
      padding: EdgeInsets.symmetric(),
      child: LDButton(
        label: FlutterI18n.translate(
          context,
          'diary.write.submit.title',
        ),
        onPressed: onTap,
      ),
    );
  }

  Padding _doneButtonBuilder(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(),
      child: LDButton(
        backgroundColor: LDColors.lightGrey,
        textColor: LDColors.mainGrey,
        label: FlutterI18n.translate(
          context,
          'diary.write.submit.done',
        ),
        onPressed: () {},
      ),
    );
  }
}
