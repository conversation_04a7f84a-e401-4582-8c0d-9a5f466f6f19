import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:moon_design/moon_design.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class NoteSwitchControl extends StatelessWidget {
  final bool value;
  final ValueChanged<bool> onChanged;

  const NoteSwitchControl({
    Key? key,
    required this.value,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          FlutterI18n.translate(
              context, 'diary.list.detail.switch.showCorrection'),
          style: TextStyle(
            fontSize: 14.sp,
          ),
        ),
        SizedBox(
          width: Adaptive.sp(10),
        ),
        MoonSwitch(
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          value: value,
          onChanged: onChanged,
          switchSize: MoonSwitchSize.x2s,
          activeTrackColor: Colors.black,
          semanticLabel: FlutterI18n.translate(
            context,
            'diary.list.detail.switch.label',
          ),
        ),
        SizedBox(
          width: Adaptive.sp(20),
        ),
      ],
    );
  }
}
