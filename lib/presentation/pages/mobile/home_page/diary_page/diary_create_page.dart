import 'dart:async';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:langda/presentation/router_constants.dart';
import 'package:langda/providers/process_diary_provider.dart';
import 'package:langda/states/app_state.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:posthog_flutter/posthog_flutter.dart';
import 'package:provider/provider.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../../common/LDState.dart';
import '../../../../../utils/weather_handler.dart';
import '../../../component.dart';
import '../../constants.dart';
import 'component/chatting_bottom_sheet.dart' show ChattingBottomSheet;
import 'component/topic_suggestions_bottom_sheet.dart';

class DiaryCreatePageWidget extends StatefulWidget {
  DiaryCreatePageWidget({
    super.key,
  });

  @override
  State<DiaryCreatePageWidget> createState() => _DiaryCreatePageWidgetState();
}

class _DiaryCreatePageWidgetState extends LDState<DiaryCreatePageWidget> {
  late TextEditingController _contentController;
  late WidgetStatesController _widgetStatesController;
  late FocusNode _focusNode;

  List<types.Message> messages = <types.Message>[];

  late Widget? selectedValue = null;
  late DateTime _selectedDay;
  late DateTime? temporarySavedTime = null;

  int selectedIndexOfToolbar = 0;

  String selctedTopicSuggestion = '';

  void setSelectedTopicSuggestion(String topic, BuildContext context) {
    selctedTopicSuggestion = topic;
    context.pop();

    setState(() {});
  }

  bool emptyFieldsCheck() {
    return _contentController.text.trim().isEmpty;
  }

  bool isLeadSurrogate(int codeUnit) {
    return codeUnit >= 0xD800 && codeUnit <= 0xDBFF;
  }

  bool isTrailSurrogate(int codeUnit) {
    return codeUnit >= 0xDC00 && codeUnit <= 0xDFFF;
  }

  bool userHasInputKorean(String text) {
    // Korean Unicode range: AC00-D7AF (Hangul Syllables)
    // Korean Unicode range: 1100-11FF (Hangul Jamo)
    // Korean Unicode range: 3130-318F (Hangul Compatibility Jamo)
    RegExp koreanRegex = RegExp(r'[\u1100-\u11FF\u3130-\u318F\uAC00-\uD7AF]');
    return koreanRegex.hasMatch(_contentController.text);
  }

  Future<void> _validateTextFields(BuildContext preContext) async {
    final DiaryProcessingProvider diaryProcessingProvider =
        Provider.of<DiaryProcessingProvider>(context, listen: false);
    if (emptyFieldsCheck()) {
      showLDToaster(
          context,
          FlutterI18n.translate(
              context, 'diary.write.content.validation.request'),
          ToastType.error);

      return;
    } else {
      if (_contentController.text.length <= 10) {
        showLDToaster(
            preContext,
            FlutterI18n.translate(
                context, 'diary.write.content.validation.minLength'),
            ToastType.error);
        return;
      } else {
        Posthog().capture(eventName: 'submit_diary', properties: {
          "login_time_stemp": DateTime.now().toIso8601String(),
        });
        diaryProcessingProvider.processDiary(_contentController.text,
            WeatherIcon.getWeatherIconName(selectedValue!), _selectedDay, () {
          LDAppState().bufferNoteContent = '';
        });
        if (preContext.mounted) {
          preContext.go(RoutePaths.home);
        }
      }
    }
  }

  void _temporarySave() {
    if (_contentController.text.trim().isNotEmpty) {
      LDAppState().bufferNoteContent = _contentController.text;
      temporarySavedTime = DateTime.now();
    }
  }

  void _clearAllFields() {
    _contentController.clear();
    setState(() {});
  }

  @override
  void initState() {
    pageName = 'WriteNote';
    Posthog().capture(eventName: 'enter_diary_create_page', properties: {
      "login_time_stemp": DateTime.now().toIso8601String(),
    });
    super.initState();

    _focusNode = FocusNode();

    _contentController =
        TextEditingController(text: LDAppState().bufferContent);
    _widgetStatesController = WidgetStatesController();
    selectedValue = WeatherIcon.getWeatherIcon('sunny');
    _selectedDay = DateTime.now();

    if (userHasInputKorean(_contentController.text)) {
      _widgetStatesController.update(WidgetState.error, true);
    } else {
      _widgetStatesController.update(WidgetState.error, false);
    }

    Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted) {
        _temporarySave();
      }
    });
  }

  @override
  void dispose() {
    Posthog().capture(eventName: 'leave_diary_create_page', properties: {
      "login_time_stemp": DateTime.now().toIso8601String(),
    });
    _contentController.dispose();
    _widgetStatesController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  buildContent(BuildContext context) {
    return Scaffold(
      appBar: appBarWrapper(
          FlutterI18n.translate(context, 'diary.write.title'),
          LDSpinIconButton(
            semanticLabel: FlutterI18n.translate(
              context,
              'button.label.close',
            ),
            icon: ImageIcon(
              AssetImage('assets/images/icons/x-close_1.5.png'),
              color: LDColors.mainGrey,
            ),
            onPressed: () {
              if (emptyFieldsCheck()) {
                LDAppState().bufferNoteContent = '';
              }
              context.go(RoutePaths.home);
            },
            backgroundColor: Colors.transparent,
          ),
          null),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(
            Adaptive.sp(17),
          ),
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Image.asset(
                            'assets/images/icons/30-512-g.png',
                            width: 24,
                          ),
                          SizedBox(
                            width: Adaptive.w(1.5),
                          ),
                          Text(
                            DateFormat(
                              'dd MMM yyyy',
                              'en_US',
                            ).format(_selectedDay),
                            style: TextStyle(
                              fontSize: 15.sp,
                              color: LDColors.mainGrey,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      Container(
                        constraints: BoxConstraints(
                          maxWidth: 150,
                        ),
                        child: CustomDropdown<Widget>(
                          decoration: CustomDropdownDecoration(
                            closedBorder: Border.all(
                              color: LDColors.foundationGrey,
                            ),
                            closedBorderRadius: BorderRadius.circular(
                              Adaptive.sp(15),
                            ),
                            expandedBorder: Border.all(
                              color: LDColors.foundationGrey,
                            ),
                            expandedBorderRadius: BorderRadius.circular(
                              Adaptive.sp(15),
                            ),
                          ),
                          initialItem:
                              WeatherIcon.getWeatherIconList().values.first,
                          headerBuilder: (context, selectedItem, enabled) =>
                              Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              selectedItem,
                              SizedBox(
                                width: 10,
                              ),
                              Text(
                                FlutterI18n.translate(
                                  context,
                                  'weather.${WeatherIcon.getWeatherIconName(selectedItem)}',
                                ),
                                style: TextStyle(
                                  color: LDColors.mainGrey,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.start,
                              ),
                            ],
                          ),
                          listItemBuilder: (context, item, selected, _) {
                            return Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                item,
                                SizedBox(
                                  width: 10,
                                ),
                                Text(
                                  FlutterI18n.translate(
                                    context,
                                    'weather.${WeatherIcon.getWeatherIconName(item)}',
                                  ),
                                  style: TextStyle(
                                    color: LDColors.mainGrey,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            );
                          },
                          items:
                              WeatherIcon.getWeatherIconList().values.toList(),
                          onChanged: (value) {
                            setState(() {
                              selectedValue = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: Adaptive.sp(17),
                  ),
                  if (selctedTopicSuggestion.isNotEmpty)
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Adaptive.sp(15),
                            vertical: Adaptive.sp(10),
                          ),
                          decoration: BoxDecoration(
                            color: LDColors.mainLime,
                            borderRadius:
                                BorderRadius.circular(Adaptive.sp(10)),
                          ),
                          margin: EdgeInsets.only(
                            right: Adaptive.sp(15),
                          ),
                          child: Text(
                            FlutterI18n.translate(
                              context,
                              'diary.write.helper.topic.title',
                            ),
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        Flexible(
                          child: Text(
                            selctedTopicSuggestion,
                          ),
                        ),
                        SizedBox(
                          width: Adaptive.w(2),
                        ),
                        GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            selctedTopicSuggestion = '';
                            setState(() {});
                          },
                          child: SizedBox(
                            width: 30,
                            height: 30,
                            child: Icon(
                              FontAwesomeIcons.circleXmark,
                              size: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                  if (selctedTopicSuggestion.isNotEmpty)
                    SizedBox(
                      height: Adaptive.sp(17),
                    ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Text(
                        FlutterI18n.translate(
                          context,
                          'diary.write.content.validation.request',
                        ),
                        style: TextStyle(
                          fontSize: 15.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      LDButton(
                        width: 70,
                        label: FlutterI18n.translate(
                          context,
                          'diary.write.clear.title',
                        ),
                        onPressed: () {
                          showLDDialog(
                            context,
                            FlutterI18n.translate(
                              context,
                              'diary.write.clear.question',
                            ),
                            onConfirmed: () {
                              _clearAllFields();
                              _widgetStatesController.update(
                                  WidgetState.error, false);
                            },
                            onCanceled: () {},
                          );
                        },
                        textColor: _contentController.text.trim().isNotEmpty
                            ? LDColors.mainGrey
                            : Colors.transparent,
                        backgroundColor:
                            _contentController.text.trim().isNotEmpty
                                ? LDColors.mainLime
                                : Colors.transparent,
                      ),
                    ],
                  ),
                  SizedBox(
                    height: Adaptive.sp(17),
                  ),
                  // * content text field
                  TextField(
                    controller: _contentController,
                    focusNode: _focusNode,
                    statesController: _widgetStatesController,
                    onTapOutside: (event) {
                      FocusScope.of(context).unfocus();
                    },
                    autofocus: true,
                    autocorrect: false,
                    keyboardType: TextInputType.multiline,
                    textCapitalization: TextCapitalization.sentences,
                    decoration: InputDecoration(
                      counter: Text(
                        '${_contentController.text.length}/1000',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: LDColors.subGrey,
                        ),
                      ),
                      helper: _widgetStatesController.value
                              .any((e) => e == WidgetState.error)
                          ? null
                          : Text(
                              FlutterI18n.translate(
                                context,
                                'diary.write.constraints.description',
                              ),
                              style: TextStyle(
                                color: LDColors.mainGrey,
                                fontWeight: FontWeight.w400,
                                fontSize: 14.sp,
                              ),
                            ),
                      error: _widgetStatesController.value
                              .any((e) => e == WidgetState.error)
                          ? Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                      right: 5,
                                    ),
                                    child: Icon(
                                      FontAwesomeIcons.circleExclamation,
                                      color: LDColors.mainRed,
                                      size: 15.sp,
                                    ),
                                  ),
                                  Text(
                                    FlutterI18n.translate(
                                      context,
                                      'diary.write.content.validation.ko',
                                    ),
                                    style: TextStyle(
                                      color: LDColors.mainRed,
                                      fontWeight: FontWeight.w400,
                                      fontSize: 14.sp,
                                    ),
                                  ),
                                ])
                          : null,
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: LDColors.foundationGrey,
                        ),
                        borderRadius: const BorderRadius.all(
                          Radius.circular(8),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: LDColors.mainLime,
                        ),
                        borderRadius: const BorderRadius.all(
                          Radius.circular(8),
                        ),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: LDColors.mainRed,
                        ),
                        borderRadius: BorderRadius.all(
                          Radius.circular(8),
                        ),
                      ),
                      focusedErrorBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: LDColors.mainRed,
                        ),
                        borderRadius: BorderRadius.all(
                          Radius.circular(8),
                        ),
                      ),
                    ),
                    minLines: 10,
                    maxLines: 50,
                    maxLength: 1000,
                    onChanged: (text) {
                      if (userHasInputKorean(text)) {
                        _widgetStatesController.update(WidgetState.error, true);
                      } else {
                        _widgetStatesController.update(
                            WidgetState.error, false);
                      }
                      setState(() {});
                    },
                  ),
                  SizedBox(
                    height: Adaptive.sp(17),
                  ),
                  LDButton(
                    isActivated: _contentController.text.trim().isNotEmpty &&
                        _widgetStatesController.value
                                .any((e) => e == WidgetState.error) ==
                            false,
                    label: FlutterI18n.translate(
                      context,
                      'diary.write.submit.title',
                    ),
                    onPressed: () async {
                      await showLDDialog(
                        context,
                        FlutterI18n.translate(
                          context,
                          'diary.write.submit.description',
                        ),
                        onConfirmed: () async {
                          await _validateTextFields(
                            context,
                          );
                        },
                        onCanceled: () {},
                      );
                    },
                  ),
                ],
              ),
              SizedBox(
                height: Adaptive.sp(100),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            decoration: BoxDecoration(
              color: LDColors.darkGrey,
              borderRadius: BorderRadius.circular(Adaptive.sp(30)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    showCustomModalBottomSheet(
                      context: context,
                      builder: (context) {
                        return TopicSuggestionsBottomSheet(
                          onSelected: setSelectedTopicSuggestion,
                        );
                      },
                      containerWidget: (BuildContext context,
                          Animation<double> animation, Widget child) {
                        return Container(
                          height: 80.h,
                          padding: EdgeInsets.only(top: 12.sp),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(Adaptive.sp(20)),
                              topRight: Radius.circular(Adaptive.sp(20)),
                            ),
                          ),
                          child: child,
                        );
                      },
                    );
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: Adaptive.sp(16),
                      vertical: Adaptive.sp(8),
                    ),
                    child: SizedBox(
                      width: 50,
                      height: 50,
                      child: Icon(
                        FontAwesomeIcons.solidLightbulb,
                        color: LDColors.mainLime,
                        size: 20,
                      ),
                    ),
                  ),
                ),
                // show bottom chatting sheet button
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    Posthog().capture(eventName: 'use_chatting', properties: {
                      "login_time_stemp": DateTime.now().toIso8601String(),
                    });
                    showCustomModalBottomSheet(
                      context: context,
                      builder: (context) {
                        return ChattingBottomSheet(
                          messages: messages,
                        );
                      },
                      containerWidget: (BuildContext context,
                          Animation<double> animation, Widget child) {
                        return Container(
                          height: 80.h,
                          padding: EdgeInsets.only(top: 12.sp),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(Adaptive.sp(20)),
                              topRight: Radius.circular(Adaptive.sp(20)),
                            ),
                          ),
                          child: child,
                        );
                      },
                    );
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: Adaptive.sp(16),
                      vertical: Adaptive.sp(8),
                    ),
                    child: SizedBox(
                      width: 50,
                      height: 50,
                      child: Center(
                        child: ImageIcon(
                          AssetImage('assets/images/icons/thinking.png'),
                          color: LDColors.mainLime,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButtonAnimator: FloatingActionButtonAnimator.noAnimation,
    );
  }
}
