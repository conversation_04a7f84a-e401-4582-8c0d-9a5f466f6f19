import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/api/models/explanation_language_preference.dart';
import 'package:langda/backend/model/card_model.dart';
import 'package:langda/presentation/nav.dart';
import 'package:moon_design/moon_design.dart';
import 'package:multi_dropdown/multi_dropdown.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../../common/custom_app_bar.dart';
import '../../../../../queries/auth_query.dart';
import '../../../../../queries/card_query.dart';
import '../../../../../queries/product_query.dart';
import '../../../../../utils/my_logger.dart';
import '../../../../../utils/supabase_auth_helper.dart';
import '../../constants.dart';
import '../../empty_list_widget.dart';
import 'component/card_tag.dart';
import 'component/correction_card.dart';

class CardListPageWidget extends StatefulWidget {
  final bool canBack;
  const CardListPageWidget({super.key, this.canBack = false});

  static void prefetchQueries() {
    myLog('Prefetching queries for CardListPageWidget');
    fetchAllCardsQuery(bookmarkedOnly: false).result;
  }

  @override
  State<CardListPageWidget> createState() => _CardListPageWidgetState();
}

class _CardListPageWidgetState extends State<CardListPageWidget>
    with ShellScreenStateMixin<CardListPageWidget> {
  bool _showBookmarkCardsOnly = false;
  final controller = MultiSelectController<Tag>();
  final _tagFilterFormKey = GlobalKey<FormState>();
  List<DropdownItem<Tag>> items = [];
  Set<Tag> selectedItems = <Tag>{};

  @override
  void initState() {
    items = Tag.values
        .map(
          (e) => DropdownItem(
            label: e.name,
            value: e,
          ),
        )
        .toList();

    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    _tagFilterFormKey.currentState?.dispose();
    super.dispose();
  }

  @override
  build(BuildContext context) {
    logBuild("CardListPageWidget $hashCode");
    final cardListEmptyWidget = EmptyListWidget(
      title: FlutterI18n.translate(
        context,
        'card.list.empty.title',
      ),
      description: FlutterI18n.translate(
        context,
        'card.list.empty.description',
      ),
      icon: Icon(
        FontAwesomeIcons.square,
      ),
    );

    return Scaffold(
      appBar: customAppBar(
        title: FlutterI18n.translate(
          context,
          'card.list.title',
        ),
        preferredSize: 85,
        leadingWidget: Image.asset(
          'assets/images/icons/chevron-left_1.5.png',
          width: 48,
          fit: BoxFit.scaleDown,
          scale: 1.3,
          color: widget.canBack ? Colors.black : Colors.transparent,
        ),
        leadingLabel: FlutterI18n.translate(
          context,
          'card.list.back',
        ),
        leadingOnTap: () {
          if (widget.canBack) {
            context.canPop() ? context.pop() : null;
          }
        },
        bottom: Container(
          decoration: BoxDecoration(
            color: Colors.white,
          ),
          child: Column(
            children: [
              Container(
                height: 26,
                padding: const EdgeInsets.only(
                  top: 10,
                  right: 20,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Text(
                      FlutterI18n.translate(
                        context,
                        'card.list.filter.bookmark.title',
                      ),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    MoonSwitch(
                      semanticLabel: FlutterI18n.translate(
                        context,
                        'card.list.filter.bookmark.hint',
                      ),
                      value: _showBookmarkCardsOnly,
                      onChanged: (value) {
                        setState(() {
                          _showBookmarkCardsOnly = value;
                        });
                      },
                      switchSize: MoonSwitchSize.x2s,
                      activeTrackColor: Theme.of(context).colorScheme.primary,
                    ),
                  ],
                ),
              ),
              // ? tag filter
              Container(
                height: 60,
                padding: const EdgeInsets.only(
                  top: 10,
                  left: 20,
                  right: 20,
                  bottom: 5,
                ),
                child: Form(
                  key: _tagFilterFormKey,
                  // Dropdown
                  child: MultiDropdown<Tag>(
                    controller: controller,
                    items: items.map((e) {
                      return DropdownItem(
                        label: FlutterI18n.translate(
                          context,
                          'tags.${e.label.toLowerCase()}',
                        ),
                        value: e.value,
                      );
                    }).toList(),
                    searchEnabled: false,
                    fieldDecoration: FieldDecoration(
                      labelText: FlutterI18n.translate(
                        context,
                        'card.list.filter.tag.all',
                      ),
                      hintText: FlutterI18n.translate(
                        context,
                        'card.list.filter.tag.hint',
                      ),
                      border: OutlineInputBorder(),
                    ),
                    selectedItemBuilder: (selectedItem) => GestureDetector(
                      onTap: () {
                        setState(() {
                          if (selectedItems.isNotEmpty) {
                            if (selectedItems.contains(selectedItem.value)) {
                              selectedItems.removeWhere((element) {
                                return element == selectedItem.value;
                              });
                            }
                          }
                        });

                        controller.toggleWhere((element) {
                          return element == selectedItem.value;
                        });
                      },
                      child: Stack(
                        children: [
                          CardTag(tag: selectedItem.value),
                        ],
                      ),
                    ),
                    onSelectionChange: (selected) {
                      if (selected == selectedItems) {
                        return;
                      } else {
                        setState(() {
                          selectedItems = selected.toSet();
                        });
                      }
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      body: SafeArea(
        top: false,
        bottom: false,
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(
              defaultPadding,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                const SizedBox(
                  height: 15,
                ),
                // Correction Cards List
                QueryBuilder(
                    query: fetchUserInfoQuery(),
                    builder: (conext, state) {
                      final ExplanationLanguagePreference?
                          explanationLanguagePreference =
                          state.data?.explanationLanguagePreference;
                      return state.status == QueryStatus.loading ||
                              state.data == null
                          ? Expanded(child: cardListEmptyWidget)
                          : Expanded(
                              child: QueryBuilder(
                                  enabled: currentUserId() != null,
                                  query: fetchAllCardsQuery(
                                    tags: selectedItems.toList(),
                                  ),
                                  builder: (context, state) {
                                    myLog(
                                        "[CardListPageWidget].QueryBuilder - Rebuilding");
                                    final List<CardModel> cards =
                                        state.data ?? [];
                                    if (state.status == QueryStatus.loading) {
                                      return Center(
                                        child: CircularProgressIndicator(),
                                      );
                                    }
                                    if (cards.isEmpty) {
                                      return cardListEmptyWidget;
                                    }
                                    cards.sort((a, b) {
                                      return a.createdAt.compareTo(b.createdAt);
                                    });

                                    return QueryBuilder(
                                      query: fetchDeepExplanationCostQuery(),
                                      builder: (context, state) => isMobile(
                                              context)
                                          ? ListView.builder(
                                              clipBehavior: Clip.none,
                                              shrinkWrap: true,
                                              itemCount: cards.length,
                                              itemBuilder: (context, index) {
                                                final card = cards[index];

                                                if (_showBookmarkCardsOnly) {
                                                  if (card.isSaved == false) {
                                                    return const SizedBox
                                                        .shrink();
                                                  }
                                                }

                                                return Padding(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                    horizontal: 10,
                                                    vertical: 5,
                                                  ),
                                                  child: Skeletonizer(
                                                    enabled: state.status ==
                                                        QueryStatus.loading,
                                                    child: CorrectionCard(
                                                      card: card,
                                                      deepExplanationCost:
                                                          state.data ?? 5,
                                                      explanationLanguagePreference:
                                                          explanationLanguagePreference ??
                                                              ExplanationLanguagePreference
                                                                  .nativeLanguage,
                                                    ),
                                                  ),
                                                );
                                              })
                                          : GridView.builder(
                                              gridDelegate:
                                                  SliverGridDelegateWithFixedCrossAxisCount(
                                                crossAxisCount: 2,
                                                childAspectRatio: 1.0,
                                                mainAxisSpacing: 10,
                                                crossAxisSpacing: 10,
                                              ),
                                              itemBuilder: (context, index) {
                                                final card = cards[index];

                                                if (_showBookmarkCardsOnly) {
                                                  if (card.isSaved == false) {
                                                    return const SizedBox
                                                        .shrink();
                                                  }
                                                }

                                                return Padding(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                    horizontal: 10,
                                                    vertical: 5,
                                                  ),
                                                  child: Skeletonizer(
                                                    enabled: state.status ==
                                                        QueryStatus.loading,
                                                    child: CorrectionCard(
                                                      card: card,
                                                      deepExplanationCost:
                                                          state.data ?? 5,
                                                      explanationLanguagePreference:
                                                          explanationLanguagePreference ??
                                                              ExplanationLanguagePreference
                                                                  .nativeLanguage,
                                                    ),
                                                  ),
                                                );
                                              }),
                                    );
                                  }),
                            );
                    }),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
