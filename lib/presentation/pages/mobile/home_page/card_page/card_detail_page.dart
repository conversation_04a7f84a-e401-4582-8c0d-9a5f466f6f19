import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:diff_match_patch/diff_match_patch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:langda/api/models/deep_explanation_response.dart';
import 'package:langda/backend/model/card_model.dart';
import 'package:langda/presentation/pages/component.dart';
import 'package:langda/presentation/pages/mobile/constants.dart';
import 'package:langda/presentation/services/app_state_service.dart';
import 'package:langda/queries/auth_query.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../../common/LDState.dart';
import '../../../../../queries/card_query.dart';
import '../../../../../queries/product_query.dart';
import 'component/correction_card.dart';

class CardDetailPage extends StatefulWidget {
  final String correctionId;
  final int deepExplanationCost;

  const CardDetailPage({
    super.key,
    required this.correctionId,
    required this.deepExplanationCost,
  });

  @override
  State<CardDetailPage> createState() => _CardDetailPageState();
}

class _CardDetailPageState extends LDState<CardDetailPage> {
  bool _isLoadingDeepExplanation = false;
  final dmp = DiffMatchPatch();

  late List<Diff> asIsDiff = [];
  late List<Diff> toBeDiff = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget buildContent(BuildContext context) {
    final AppStateService appStateService = context.watch<AppStateService>();
    return QueryBuilder(
        query: fetchCardQuery(widget.correctionId),
        builder: (context, state) {
          final card = state.data;

          if (card == null) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          asIsDiff = dmp.diff(card.toBe.content, card.asIs.content);
          toBeDiff = dmp.diff(card.asIs.content, card.toBe.content);

          return Skeletonizer(
            enabled: state.status == QueryStatus.loading,
            child: Scaffold(
              backgroundColor: Colors.transparent,
              appBar: AppBar(
                actions: [
                  QueryBuilder(
                    query: fetchUserInfoQuery(),
                    builder: (context, state) => Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          state.data != null
                              ? state.data!.currencyBalance.toString()
                              : '0',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: LDColors.mainGrey,
                          ),
                        ),
                        const SizedBox(width: 5),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Image.asset(
                              'assets/images/gems.png',
                              width: 20,
                              height: 20,
                            ),
                          ],
                        ),
                        const SizedBox(width: 15),
                      ],
                    ),
                  ),
                ],
              ),
              body: SafeArea(
                bottom: false,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _contentBoxBuilder(
                            title: FlutterI18n.translate(
                                context, 'card.detail.original'),
                            content: CardHeaderWrapper(
                              diffList: asIsDiff,
                              stringList: card.asIs.content.split(' '),
                              color: LDColors.mainRed,
                            ),
                          ),
                          const SizedBox(height: 20),
                          _contentBoxBuilder(
                            title: FlutterI18n.translate(
                                context, 'card.detail.corrected'),
                            content: CardHeaderWrapper(
                              diffList: toBeDiff,
                              stringList: card.toBe.content.split(' '),
                              color: LDColors.mainBlue,
                            ),
                          ),
                          const SizedBox(height: 20),
                        ],
                      ),
                      // deep explanation box
                      (card.deepExplanation != null &&
                              card.deepExplanation!.isNotEmpty)
                          ? Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withValues(alpha: 0.3),
                                    spreadRadius: 1,
                                    blurRadius: 5,
                                    offset: const Offset(0, 3),
                                  ),
                                ],
                                color: Colors.white,
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        FontAwesomeIcons.wandMagicSparkles,
                                        size: 20,
                                        color: LDColors.mainGrey,
                                      ),
                                      const SizedBox(width: 10),
                                      Text(
                                        FlutterI18n.translate(context,
                                            'card.detail.explanation.deepExplain.title'),
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w500,
                                          color: LDColors.mainGrey,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 10),
                                  Markdown(
                                    padding: const EdgeInsets.all(0),
                                    data: card.deepExplanation!,
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                  ),
                                ],
                              ),
                            )
                          : _isLoadingDeepExplanation
                              ? Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(20),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    boxShadow: [
                                      BoxShadow(
                                        color:
                                            Colors.grey.withValues(alpha: 0.3),
                                        spreadRadius: 1,
                                        blurRadius: 5,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                    color: Colors.white,
                                  ),
                                  child: Center(
                                    child: Column(
                                      children: [
                                        Image.asset(
                                          'assets/lottie/document.gif',
                                          width: 50,
                                          height: 50,
                                        ),
                                        const SizedBox(height: 10),
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8.0,
                                          ),
                                          child: Text(
                                            FlutterI18n.translate(
                                              context,
                                              'card.detail.explanation.deepExplain.loading.description',
                                            ),
                                            style: TextStyle(
                                              fontSize: 15,
                                            ),
                                          ),
                                        ),
                                        AnimatedTextKit(
                                          pause: Duration(milliseconds: 2000),
                                          repeatForever: true,
                                          animatedTexts: [
                                            TypewriterAnimatedText(
                                              FlutterI18n.translate(
                                                context,
                                                'card.detail.explanation.deepExplain.loading.typeWriterAnimationText.0',
                                              ),
                                              speed:
                                                  Duration(milliseconds: 250),
                                              textStyle: TextStyle(
                                                fontSize: 15,
                                              ),
                                            ),
                                            TypewriterAnimatedText(
                                              FlutterI18n.translate(
                                                context,
                                                'card.detail.explanation.deepExplain.loading.typeWriterAnimationText.1',
                                              ),
                                              speed:
                                                  Duration(milliseconds: 250),
                                              textStyle: TextStyle(
                                                fontSize: 15,
                                              ),
                                            ),
                                            TypewriterAnimatedText(
                                              FlutterI18n.translate(
                                                context,
                                                'card.detail.explanation.deepExplain.loading.typeWriterAnimationText.2',
                                              ),
                                              speed:
                                                  Duration(milliseconds: 250),
                                              textStyle: TextStyle(
                                                fontSize: 15,
                                              ),
                                            ),
                                            TypewriterAnimatedText(
                                              FlutterI18n.translate(
                                                context,
                                                'card.detail.explanation.deepExplain.loading.typeWriterAnimationText.3',
                                              ),
                                              speed:
                                                  Duration(milliseconds: 250),
                                              textStyle: TextStyle(
                                                fontSize: 15,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                              : QueryBuilder(
                                  query: fetchDeepExplanationCostQuery(),
                                  builder: (context, state) =>
                                      _deepExplanationBoxBuilder(
                                    state.data,
                                    appStateService,
                                    card,
                                  ),
                                ),
                      const SizedBox(height: 20),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withValues(alpha: 0.3),
                              spreadRadius: 1,
                              blurRadius: 5,
                              offset: const Offset(0, 3),
                            ),
                          ],
                          color: Colors.white,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  FontAwesomeIcons.bookOpen,
                                  size: 20,
                                  color: LDColors.mainGrey,
                                ),
                                const SizedBox(width: 10),
                                Text(
                                  FlutterI18n.translate(
                                      context, 'card.detail.explanation.title'),
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w500,
                                    color: LDColors.mainGrey,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 10),
                            Text(
                              card.explanation,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 10),
                          ],
                        ),
                      ),

                      const SizedBox(height: 20),

                      // categories
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                FontAwesomeIcons.tag,
                                color: LDColors.mainGrey,
                                size: 18,
                              ),
                              const SizedBox(width: 10),
                              Text(
                                FlutterI18n.translate(
                                  context,
                                  'card.detail.category.title',
                                ),
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                  color: LDColors.mainGrey,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          Wrap(
                            spacing: 5,
                            runSpacing: 5,
                            children: [
                              ...card.categories.map((e) => e),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        });
  }

  Container _deepExplanationBoxBuilder(
      int? cost, AppStateService appStateService, CardModel card) {
    return Container(
      padding: const EdgeInsets.all(20),
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
        color: Colors.white,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: EdgeInsets.only(
                  left: 6,
                  right: 5,
                  top: 8,
                  bottom: 5,
                ),
                decoration: BoxDecoration(
                  color: LDColors.mainLime,
                  shape: BoxShape.circle,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(3.0),
                  child: Image.asset(
                    'assets/images/gems.png',
                    width: 20,
                    height: 20,
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Text(
                FlutterI18n.translate(
                    context, 'card.detail.explanation.deepExplain.title'),
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                '${cost ?? 0}',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(width: 5),
              Image.asset(
                'assets/images/gems.png',
                width: 20,
                height: 20,
              ),
            ],
          ),
          const SizedBox(height: 10),
          Text(
            FlutterI18n.translate(
              context,
              'card.detail.explanation.deepExplain.box.description',
              translationParams: {
                'cost': cost.toString(),
              },
            ),
          ),
          const SizedBox(height: 10),
          QueryBuilder(
            query: fetchUserInfoQuery(),
            builder: (context, state) {
              final user = state.data;

              final bool isAbleToUnlock = user != null &&
                  cost != null &&
                  user.currencyBalance >= cost &&
                  user.currencyBalance > 0;

              return Skeletonizer(
                enabled: state.status == QueryStatus.loading,
                child: SizedBox(
                  height: 50,
                  width: double.infinity,
                  child: GestureDetector(
                    onTap: () async {
                      if (isAbleToUnlock) {
                        setState(() {
                          _isLoadingDeepExplanation = true;
                        });

                        void onFinishedCallback(DeepExplanationResponse res) {
                          if (mounted) {
                            setState(() {
                              _isLoadingDeepExplanation = false;
                            });
                          }
                        }

                        void onErrorCallback(dynamic error) {
                          if (mounted) {
                            setState(() {
                              _isLoadingDeepExplanation = false;
                            });
                          }
                        }

                        final mutation = getDeepExplanationMutation(
                          widget.correctionId,
                          onFinishedCallback,
                          onErrorCallback,
                        );
                        await mutation.mutate();
                      } else {
                        if (user != null && cost != null) {
                          final gems = user.currencyBalance;
                          if (gems < cost) {
                            showLDToaster(
                              context,
                              FlutterI18n.translate(
                                context,
                                'card.detail.explanation.deepExplain.error.gems',
                              ),
                              ToastType.info,
                            );
                          }
                        }
                        setState(() {});
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color:
                            isAbleToUnlock ? Colors.black : LDColors.lightGrey,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Text(
                            isAbleToUnlock
                                ? FlutterI18n.translate(
                                    context,
                                    'card.detail.explanation.deepExplain.box.button.enable',
                                    translationParams: {
                                      'cost': cost.toString(),
                                    },
                                  )
                                : FlutterI18n.translate(
                                    context,
                                    'card.detail.explanation.deepExplain.box.button.disable',
                                  ),
                            style: TextStyle(
                              color:
                                  isAbleToUnlock ? Colors.white : Colors.black,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          isAbleToUnlock
                              ? const SizedBox(width: 5)
                              : const SizedBox(),
                          isAbleToUnlock
                              ? Image.asset(
                                  'assets/images/gems.png',
                                  width: 20,
                                  height: 20,
                                )
                              : const SizedBox(),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Container _contentBoxBuilder({
    String title = 'Original',
    Widget? content,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: LDColors.mainGrey,
            ),
          ),
          const SizedBox(height: 10),
          content ?? Container(),
        ],
      ),
    );
  }
}
