import 'dart:async';

import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:diff_match_patch/diff_match_patch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animated_icon_button/tap_fill_icon.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/api/models/explanation_language_preference.dart';
import 'package:langda/presentation/router_constants.dart';
import 'package:langda/utils/my_logger.dart';
import 'package:like_button/like_button.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../../../backend/model/card_model.dart';
import '../../../../../../queries/card_query.dart';
import '../../../constants.dart';
import 'card_tag.dart';

class CorrectionCard extends StatefulWidget {
  final CardModel card;
  final int deepExplanationCost;
  final ExplanationLanguagePreference explanationLanguagePreference;

  const CorrectionCard({
    super.key,
    required this.card,
    required this.deepExplanationCost,
    required this.explanationLanguagePreference,
  });

  @override
  State<CorrectionCard> createState() => _CorrectionCardState();
}

class _CorrectionCardState extends State<CorrectionCard>
    with TickerProviderStateMixin {
  late final String asIs;
  late final String toBe;
  late final String explanation;
  late final List<CardTag> tags;
  late bool isSaved;
  bool justVoted = false;

  late AnimationController _voteContainerController;
  late AnimationController _upVoteController;
  late AnimationController _downVoteController;

  final textStyle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: LDColors.mainGrey,
  );

  @override
  void initState() {
    asIs = widget.card.asIs.content;
    toBe = widget.card.toBe.content;
    explanation = widget.card.explanation;
    tags = widget.card.categories;

    _voteContainerController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _upVoteController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    )..addListener(() {
        setState(() {});
      });
    _downVoteController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    )..addListener(() {
        setState(() {});
      });
    isSaved = widget.card.isSaved;
    super.initState();
    if (mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _voteContainerController.forward();
          setState(() {});
        }
      });
    }
  }

  @override
  void dispose() {
    _upVoteController.dispose();
    _downVoteController.dispose();
    _voteContainerController.dispose();
    super.dispose();
  }

  build(BuildContext context) {
    myLog('Building CorrectionCard, justVoted is $justVoted');
    var _voteContainer = Container(
      constraints: BoxConstraints(
        maxWidth: Adaptive.w(25),
      ),
      padding: const EdgeInsets.only(left: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(right: 5),
            child: TapFillIcon(
              animationController: _upVoteController,
              borderIcon: Icon(
                FontAwesomeIcons.thumbsUp,
                color: LDColors.mainGrey,
                size: 18,
              ),
              fillIcon: Icon(
                FontAwesomeIcons.solidThumbsUp,
                color: LDColors.mainGrey,
                size: 18,
              ),
              initialPushed: false,
              onTap: () async {
                final correctionId = widget.card.correctionId;
                final mutation = upvoteCorrectionMutation(correctionId);
                mutation.mutate();
                if (mounted) {
                  Future.delayed(const Duration(milliseconds: 300), () {
                    if (mounted &&
                        _voteContainerController.isAnimating != true) {
                      _voteContainerController.reverse().then((_) {
                        if (mounted) {
                          setState(() {
                            justVoted = true;
                          });
                        }
                      });
                    }
                  });
                }
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              left: 5,
            ),
            child: TapFillIcon(
              animationController: _downVoteController,
              borderIcon: Icon(
                FontAwesomeIcons.thumbsDown,
                color: LDColors.mainGrey,
                size: 18,
              ),
              fillIcon: Icon(
                FontAwesomeIcons.solidThumbsDown,
                color: LDColors.mainGrey,
                size: 18,
              ),
              initialPushed: false,
              onTap: () async {
                final correctionId = widget.card.correctionId;
                final mutation = downvoteCorrectionMutation(correctionId);
                mutation.mutate();
                if (mounted) {
                  Future.delayed(const Duration(milliseconds: 300), () {
                    if (mounted &&
                        _voteContainerController.isAnimating != true) {
                      _voteContainerController.reverse().then((_) {
                        if (mounted) {
                          setState(() {
                            justVoted = true;
                          });
                        }
                      });
                    }
                  });
                }
              },
            ),
          ),
        ],
      ),
    );
    return Container(
      alignment: Alignment.topLeft,
      margin: const EdgeInsets.symmetric(vertical: 5),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            constraints: BoxConstraints(
              minWidth: 300,
              maxWidth: 500,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 0),
                  spreadRadius: 10,
                ),
              ],
            ),
            child: Container(
              padding: const EdgeInsets.all(15.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // * tag wrapper
                      Expanded(
                        child: Wrap(
                            direction: Axis.horizontal,
                            verticalDirection: VerticalDirection.down,
                            alignment: WrapAlignment.start,
                            crossAxisAlignment: WrapCrossAlignment.center,
                            spacing: 5,
                            runSpacing: 5,
                            children: widget.card.categories),
                      ),
                      // bookmark button
                      Semantics(
                        label: FlutterI18n.translate(
                          context,
                          'card.update.bookmark.title',
                        ),
                        child: LikeButton(
                          onTap: (isLiked) async {
                            isSaved = !isSaved;
                            final mutation = cardBookmarkingMutation(
                              widget.card.correctionId,
                            );

                            final delay =
                                Duration(milliseconds: isSaved ? 50 : 0);
                            if (mounted) {
                              unawaited(
                                Future.delayed(delay, () async {
                                  if (mounted) {
                                    await mutation.mutate(isSaved);
                                    if (mutation.state.status ==
                                        QueryStatus.error) {
                                      myLog(
                                        '[cardBookmarkingMutation] Failed to mutate: ${mutation.state.error}',
                                      );
                                      if (mounted) {
                                        setState(() {
                                          isSaved = !isSaved;
                                        });
                                      }
                                    }
                                  }
                                }),
                              );
                            }

                            return isSaved;
                          },
                          size: Adaptive.sp(25),
                          circleColor: CircleColor(
                            start: Color.fromARGB(255, 184, 220, 133),
                            end: Color.fromARGB(255, 131, 218, 102),
                          ),
                          bubblesColor: BubblesColor(
                            dotPrimaryColor: Color.fromARGB(255, 114, 217, 186),
                            dotSecondaryColor:
                                Theme.of(context).colorScheme.primary,
                          ),
                          isLiked: isSaved,
                          likeBuilder: (bool isLiked) {
                            return Icon(
                              isLiked
                                  ? FontAwesomeIcons.solidBookmark
                                  : FontAwesomeIcons.bookmark,
                              color: Theme.of(context).colorScheme.primary,
                              size: Adaptive.sp(18),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  Wrap(
                    direction: Axis.horizontal,
                    verticalDirection: VerticalDirection.down,
                    alignment: WrapAlignment.start,
                    crossAxisAlignment: WrapCrossAlignment.center,
                    spacing: 10,
                    children: [
                      _cardHeaderBuilder(),
                    ],
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 15,
                    ),
                    child: Text(
                      explanation,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: LDColors.mainGrey,
                      ),
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      if (widget.card.isVoted == false && justVoted == false)
                        _voteContainer,
                      Spacer(),
                      GestureDetector(
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            vertical: 10,
                            horizontal: 20,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            color: LDColors.mainLime
                                .withValues(alpha: 0.2), // Use withOpacity
                          ),
                          child: Text(
                            FlutterI18n.translate(
                              context,
                              'card.detail.button',
                            ),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                              color: LDColors.foundationLimeDark,
                            ),
                          ),
                        ),
                        onTap: () {
                          Future.delayed(
                            const Duration(milliseconds: 100),
                          );
                          context.push(
                            RoutePaths.cardDetail,
                            extra: {
                              'card': widget.card,
                              'deepExplanationCost': widget.deepExplanationCost,
                            },
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _cardHeaderBuilder() {
    final dmp = DiffMatchPatch();

    final List<Diff> asIsDiff = dmp.diff(toBe, asIs);
    final List<Diff> toBeDiff = dmp.diff(asIs, toBe);
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Wrap(
            direction: Axis.horizontal,
            verticalDirection: VerticalDirection.down,
            alignment: WrapAlignment.start,
            crossAxisAlignment: WrapCrossAlignment.center,
            children: [
              Stack(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: Adaptive.sp(18),
                      ),
                      Container(
                        padding: const EdgeInsets.all(10),
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: LDColors.mainRed,
                            width: 1,
                          ),
                        ),
                        child: CardHeaderWrapper(
                          diffList: asIsDiff,
                          stringList: asIs.split(' '),
                          color: LDColors.mainRed,
                        ),
                      ),
                    ],
                  ),
                  Align(
                    alignment: Alignment.topLeft + Alignment(0.1, 0.3),
                    child: Container(
                      padding: const EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        color: Colors.white,
                      ),
                      child: Text(
                        FlutterI18n.translate(
                          context,
                          'card.header.original',
                        ),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Stack(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: Adaptive.sp(18),
                      ),
                      Container(
                        padding: const EdgeInsets.all(10),
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: LDColors.mainBlue,
                            width: 1,
                          ),
                        ),
                        child: CardHeaderWrapper(
                          diffList: toBeDiff,
                          stringList: toBe.split(' '),
                          color: LDColors.mainBlue,
                        ),
                      ),
                    ],
                  ),
                  Align(
                    alignment: Alignment.topLeft + Alignment(0.1, 0.3),
                    child: Container(
                      padding: const EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        color: Colors.white,
                      ),
                      child: Text(
                        FlutterI18n.translate(
                          context,
                          'card.header.corrected',
                        ),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class CardHeaderWrapper extends StatelessWidget {
  const CardHeaderWrapper({
    super.key,
    required this.diffList,
    required this.stringList,
    required this.color,
  });

  final List<Diff> diffList;
  final List<String> stringList;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 5,
      direction: Axis.horizontal,
      verticalDirection: VerticalDirection.down,
      alignment: WrapAlignment.start,
      crossAxisAlignment: WrapCrossAlignment.center,
      children: () {
        final List<Widget> _widgets = [];
        Color _color = Colors.black;
        StringBuffer _buffer = StringBuffer();
        for (var diff in diffList) {
          final String chunk = diff.text;
          final List<String> _characters = chunk.characters.toList();
          for (var i = 0; i < _characters.length; i++) {
            final String character = _characters[i];
            if (character != ' ' && character != '\n') {
              if (diff.operation == DIFF_EQUAL) {
                _buffer.write(character);
              } else if (diff.operation == DIFF_DELETE) {
                if (character.trim().length == 1) {
                  _color = color;
                  _color = color;
                } else {
                  _color = Colors.green;
                }
              } else if (diff.operation == DIFF_INSERT) {
                _buffer.write(character);
                _color = color;
                _color = color;
              }
            } else {
              if (_color != Colors.green && _buffer.isNotEmpty) if (_color !=
                      Colors.green &&
                  _buffer
                      .isNotEmpty) if (stringList[_widgets.length] ==
                  _buffer.toString()) {
                _widgets.add(
                  Text(
                    _buffer.toString(),
                    style: TextStyle(
                      color: _color,
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                    ),
                    overflow: null,
                  ),
                );

                _buffer.clear();
                _color = Colors.black;
              }
            }
          }
        }
        if (_buffer.isNotEmpty)
          _widgets.add(
            Text(
              _buffer.toString(),
              style: TextStyle(
                color: _color,
                fontSize: 16,
                fontWeight: FontWeight.w700,
              ),
            ),
          );

        _buffer.clear();
        _color = Colors.black;
        return _widgets;
      }(),
    );
  }
}
