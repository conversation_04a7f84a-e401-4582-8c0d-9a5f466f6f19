import 'package:flutter/cupertino.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:moon_design/moon_design.dart';

enum Tag {
  meaning('09074aa0-d8f1-461b-b02a-ba74bcc7b143'),
  number('589428cd-e8da-43e3-958c-b567c0cea32e'),
  tenses('610cd902-19d3-4a3a-815f-f4915633c162'),
  subject_verb('63cc3ed8-ddbf-4caf-aac0-9d88e3a35ddc'),
  spelling('67c4a317-9034-42f8-b387-99df5d6bf8f3'),
  word_order('744d9d96-ea24-4b5b-bafc-1b270b715569'),
  articles('8566099d-010d-45d3-a117-5a0075b98982'),
  punctuation('a9b7c39c-162c-4faa-bea8-c82d573a16ef'),
  prepositions('cb5d4977-52e0-4cd6-a3b7-6ce6cbd87124'),
  other('d030f3b9-cd86-4659-998b-6d98bf455502');

  const Tag(this.id);

  final String id;
}

extension StringConverterToTag on String {
  Tag get tag {
    switch (this) {
      case 'meaning':
        return Tag.meaning;
      case 'number':
        return Tag.number;
      case 'tenses':
        return Tag.tenses;
      case 'subject_verb':
        return Tag.subject_verb;
      case 'spelling':
        return Tag.spelling;
      case 'word_order':
        return Tag.word_order;
      case 'articles':
        return Tag.articles;
      case 'punctuation':
        return Tag.punctuation;
      case 'prepositions':
        return Tag.prepositions;
      case 'other':
        return Tag.other;
      default:
        return Tag.other;
    }
  }
}

class CardTag extends StatelessWidget {
  final Tag tag;

  const CardTag({required this.tag}); // Private constructor

  factory CardTag.fromId({required String tagId}) {
    return CardTag(tag: _getTagFromId(tagId));
  }

  factory CardTag.fromName({required String tagName}) {
    return CardTag(tag: tagName.tag);
  }

  static Tag _getTagFromId(String? id) {
    return Tag.values
        .firstWhere((element) => element.id == id, orElse: () => Tag.other);
  }

  static const Map<Tag, Color> _tagColorMap = {
    Tag.meaning: Color(0xFFDFF2EB),
    Tag.number: Color(0xFFD0E8C5),
    Tag.tenses: Color(0xFFF6E3E3),
    Tag.subject_verb: Color(0xFFF2E3E3),
    Tag.spelling: Color(0xFFCFFACC),
    Tag.word_order: Color(0xFFFCDDB0),
    Tag.articles: Color(0xFFCBA0AE),
    Tag.punctuation: Color(0xFFBCCEF8),
    Tag.prepositions: Color(0xFFCFDAC8),
    Tag.other: Color(0xFFDAD0C2),
  };

  Color get backgroundColor => _tagColorMap[tag] ?? _tagColorMap[Tag.other]!;

  @override
  Widget build(BuildContext context) {
    return MoonTag(
        label: Text(
          FlutterI18n.translate(
            context,
            'tags.${tag.name}',
          ),
          // tag.name,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
        tagSize: MoonTagSize.xs,
        backgroundColor: this.backgroundColor);
  }
}
