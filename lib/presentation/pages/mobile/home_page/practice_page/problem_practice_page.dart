import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/backend/model/practice_problem_model.dart';
import 'package:langda/data/sample_practice_problems.dart';
import 'package:langda/presentation/pages/mobile/constants.dart';

import '../../../../../common/LDState.dart';
import '../../../../../common/custom_app_bar.dart';
import 'components/practice_problem_card.dart';
import 'components/practice_progress_indicator.dart';

class ProblemPracticePage extends StatefulWidget {
  const ProblemPracticePage({super.key});

  @override
  State<ProblemPracticePage> createState() => _ProblemPracticePageState();
}

class _ProblemPracticePageState extends LDState<ProblemPracticePage> {
  late List<PracticeProblem> _problems;
  late PracticeProgress _progress;
  Map<String, String> _userAnswers = {};
  Map<String, bool> _answerResults = {};
  bool _showResults = false;

  @override
  void initState() {
    super.initState();
    _initializeProblems();
  }

  void _initializeProblems() {
    // Get a random subset of 10 problems for this session
    _problems = SamplePracticeProblems.getRandomProblems(10);
    _progress = PracticeProgress.initial(_problems.length);
  }

  @override
  Widget buildContent(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: customAppBar(
        title: FlutterI18n.translate(context, 'practice.title'),
        leadingWidget: Image.asset(
          'assets/images/icons/chevron-left_1.5.png',
          width: 48,
          fit: BoxFit.scaleDown,
          scale: 1.3,
        ),
        leadingOnTap: () {
          context.canPop() ? context.pop() : null;
        },
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Progress indicator
            PracticeProgressIndicator(
              progress: _progress,
              showAccuracy: true,
            ),

            // Main content
            Expanded(
              child: _showResults ? _buildResultsView() : _buildPracticeView(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPracticeView() {
    return PageView.builder(
      itemCount: _problems.length,
      itemBuilder: (context, index) {
        final problem = _problems[index];
        final isAnswered = _userAnswers.containsKey(problem.id);

        return SingleChildScrollView(
          child: PracticeProblemCard(
            problem: problem,
            isAnswered: isAnswered,
            selectedAnswer: _userAnswers[problem.id],
            onAnswerSelected: (answer, isCorrect) {
              _handleAnswerSelected(problem.id, answer, isCorrect);
            },
          ),
        );
      },
    );
  }

  Widget _buildResultsView() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Results header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: _progress.accuracyPercentage >= 70
                  ? Colors.green.shade50
                  : _progress.accuracyPercentage >= 50
                      ? Colors.orange.shade50
                      : Colors.red.shade50,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: _progress.accuracyPercentage >= 70
                    ? Colors.green
                    : _progress.accuracyPercentage >= 50
                        ? Colors.orange
                        : Colors.red,
                width: 2,
              ),
            ),
            child: Column(
              children: [
                Icon(
                  _progress.accuracyPercentage >= 70
                      ? Icons.celebration
                      : _progress.accuracyPercentage >= 50
                          ? Icons.thumb_up
                          : Icons.refresh,
                  size: 48,
                  color: _progress.accuracyPercentage >= 70
                      ? Colors.green
                      : _progress.accuracyPercentage >= 50
                          ? Colors.orange
                          : Colors.red,
                ),
                const SizedBox(height: 12),
                Text(
                  _progress.accuracyPercentage >= 70
                      ? 'Excellent Work!'
                      : _progress.accuracyPercentage >= 50
                          ? 'Good Job!'
                          : 'Keep Practicing!',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'You got ${_progress.correctAnswers} out of ${_progress.totalProblems} questions correct',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: _restartPractice,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: LDColors.mainLime,
                    foregroundColor: Colors.black87,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Try Again',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton(
                  onPressed: () => context.pop(),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.black87,
                    side: BorderSide(color: LDColors.lightGrey),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Back to Practice',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _handleAnswerSelected(String problemId, String answer, bool isCorrect) {
    setState(() {
      _userAnswers[problemId] = answer;
      _answerResults[problemId] = isCorrect;

      // Update progress
      final newCompletedCount = _userAnswers.length;
      final newCorrectCount =
          _answerResults.values.where((result) => result).length;

      _progress = _progress.copyWith(
        completedProblems: newCompletedCount,
        correctAnswers: newCorrectCount,
        completedProblemIds: _userAnswers.keys.toList(),
      );

      // Show results if all problems are completed
      if (_progress.isComplete) {
        Future.delayed(const Duration(milliseconds: 1500), () {
          if (mounted) {
            setState(() {
              _showResults = true;
            });
          }
        });
      }
    });
  }

  void _showResultsSummary() {
    setState(() {
      _showResults = true;
    });
  }

  void _restartPractice() {
    setState(() {
      _initializeProblems();
      _userAnswers.clear();
      _answerResults.clear();
      _showResults = false;
    });
  }
}
