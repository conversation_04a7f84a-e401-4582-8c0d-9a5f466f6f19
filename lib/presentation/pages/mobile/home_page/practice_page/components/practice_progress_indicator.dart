import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:langda/backend/model/practice_problem_model.dart';
import 'package:langda/presentation/pages/mobile/constants.dart';

class PracticeProgressIndicator extends StatelessWidget {
  final PracticeProgress progress;
  final bool showAccuracy;

  const PracticeProgressIndicator({
    super.key,
    required this.progress,
    this.showAccuracy = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: LDColors.lightGrey,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Progress text
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                FlutterI18n.translate(
                    context, 'practice.progress_indicator.progress'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              Text(
                '${progress.completedProblems} ${FlutterI18n.translate(context, 'practice.progress_indicator.questions_completed', translationParams: {
                      'total': progress.totalProblems.toString()
                    })}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: LDColors.mainGrey,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Progress bar
          Container(
            height: 8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: LDColors.lightGrey,
            ),
            child: progress.progressFraction > 0.0
                ? FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: progress.progressFraction,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        gradient: LinearGradient(
                          colors: [
                            LDColors.foundationLime,
                            LDColors.mainLime,
                          ],
                        ),
                      ),
                    ),
                  )
                : null, // Show empty progress bar when at 0%
          ),

          // Accuracy (if enabled and there are completed problems)
          if (showAccuracy && progress.completedProblems > 0) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.check_circle_outline,
                  size: 16,
                  color: progress.accuracyPercentage >= 70
                      ? Colors.green
                      : progress.accuracyPercentage >= 50
                          ? Colors.orange
                          : Colors.red,
                ),
                const SizedBox(width: 6),
                Text(
                  FlutterI18n.translate(
                      context, 'practice.progress_indicator.accuracy',
                      translationParams: {
                        'percentage':
                            progress.accuracyPercentage.toStringAsFixed(1)
                      }),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: progress.accuracyPercentage >= 70
                        ? Colors.green
                        : progress.accuracyPercentage >= 50
                            ? Colors.orange
                            : Colors.red,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  FlutterI18n.translate(
                      context, 'practice.progress_indicator.correct_answers',
                      translationParams: {
                        'correct': progress.correctAnswers.toString(),
                        'total': progress.completedProblems.toString()
                      }),
                  style: TextStyle(
                    fontSize: 12,
                    color: LDColors.mainGrey,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}

/// A compact version of the progress indicator for smaller spaces
class CompactProgressIndicator extends StatelessWidget {
  final PracticeProgress progress;

  const CompactProgressIndicator({
    super.key,
    required this.progress,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: LDColors.foundationLimeLight,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${progress.completedProblems} / ${progress.totalProblems}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: LDColors.foundationLimeDark,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  height: 4,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2),
                    color: Colors.white.withValues(alpha: 0.5),
                  ),
                  child: progress.progressFraction > 0.0
                      ? FractionallySizedBox(
                          alignment: Alignment.centerLeft,
                          widthFactor: progress.progressFraction,
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(2),
                              color: LDColors.foundationLimeDark,
                            ),
                          ),
                        )
                      : null, // Show empty progress bar when at 0%
                ),
              ],
            ),
          ),
          if (progress.completedProblems > 0) ...[
            const SizedBox(width: 12),
            Text(
              '${progress.accuracyPercentage.toStringAsFixed(0)}%',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: LDColors.foundationLimeDark,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
