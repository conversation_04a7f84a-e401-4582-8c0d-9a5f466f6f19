import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/presentation/pages/mobile/constants.dart';
import 'package:langda/presentation/router_constants.dart';

import '../../../../../common/LDState.dart';
import '../../../../../common/custom_app_bar.dart';

class PracticeMainPage extends StatefulWidget {
  final Function(int)? changePage;

  const PracticeMainPage({
    super.key,
    this.changePage,
  });

  @override
  State<PracticeMainPage> createState() => _PracticeMainPageState();
}

class _PracticeMainPageState extends LDState<PracticeMainPage> {
  @override
  Widget buildContent(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: customAppBar(
        title: FlutterI18n.translate(context, 'practice.title'),
        trailing: IconButton(
          icon: const Icon(FontAwesomeIcons.chartSimple, color: Colors.black),
          onPressed: () {
            context.push(RoutePaths.analyticsPath());
          },
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: LDColors.foundationLime.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: LDColors.foundationLime.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.quiz_outlined,
                      size: 32,
                      color: LDColors.foundationLimeDark,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      FlutterI18n.translate(context, 'practice.header.title'),
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      FlutterI18n.translate(
                          context, 'practice.header.description'),
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Stats section (placeholder for future implementation)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.grey[200]!,
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      FlutterI18n.translate(context, 'practice.progress.title'),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                              FlutterI18n.translate(
                                  context, 'practice.progress.problems_solved'),
                              '0'),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                              FlutterI18n.translate(
                                  context, 'practice.progress.accuracy_rate'),
                              '0%'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const Spacer(),

              // Start Practice button
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: () {
                    context.push(RoutePaths.practiceProblemsPath());
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: LDColors.foundationLime,
                    foregroundColor: Colors.black,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    FlutterI18n.translate(context, 'practice.start_button'),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: LDColors.foundationLimeDark,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}
