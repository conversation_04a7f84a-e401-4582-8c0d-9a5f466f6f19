import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import 'constants.dart';

class EmptyListWidget extends StatelessWidget {
  final String title;
  final String description;
  final Icon? icon;

  const EmptyListWidget({
    super.key,
    required this.title,
    required this.description,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          height: 45.sp,
        ),
        icon ??
            Icon(
              FontAwesomeIcons.penToSquare,
              size: 20.sp,
              color: LDColors.mainGrey,
            ),
        SizedBox(
          height: 13.sp,
        ),
        Text(
          title,
          style: TextStyle(
            fontSize: 14.sp,
            color: LDColors.mainGrey,
            fontWeight: FontWeight.w700,
          ),
          textAlign: TextAlign.center,
        ),
        Sized<PERSON><PERSON>(
          height: 10.sp,
        ),
        Text(
          description,
          style: TextStyle(
            fontSize: 13.sp,
            color: LDColors.mainGrey,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(
          height: 45.sp,
        ),
      ],
    );
  }
}
