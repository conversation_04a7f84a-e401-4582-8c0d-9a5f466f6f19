import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:langda/presentation/pages/mobile/constants.dart';
// AppStateService import 추가
import 'package:langda/presentation/services/app_state_service.dart';
import 'package:langda/utils/my_logger.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart'; // Provider import 추가
import 'package:url_launcher/url_launcher.dart' as url_launcher;

class UpdateRequiredPage extends StatefulWidget {
  const UpdateRequiredPage({Key? key}) : super(key: key);

  @override
  State<UpdateRequiredPage> createState() => _UpdateRequiredPageState();
}

class _UpdateRequiredPageState extends State<UpdateRequiredPage>
    with WidgetsBindingObserver {
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    // Observer 등록
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    // Observer 해제
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    // 앱이 다시 활성화될 때 버전 체크
    if (state == AppLifecycleState.resumed) {
      myLog('[UpdateRequiredPage] App resumed, checking version state.',
          LogLevel.info);
      // checkVersionState 호출 (비동기 처리 필요 시 await 사용)
      // AppStateService().checkVersionState(); // TODO: checkVersionState가 비동기인지 확인하고 필요시 await 추가
      // TODO: checkVersionState 호출 후 상태 변화에 따른 UI 업데이트 로직 확인 필요
      // 예를 들어, 버전이 업데이트 되었다면 다른 페이지로 이동하거나 이 페이지를 닫아야 할 수 있음
      // Provider를 통해 AppStateService 인스턴스 접근
      final appStateService =
          Provider.of<AppStateService>(context, listen: false);
      await appStateService.checkVersionState();
    }
  }

  // Function to get the appropriate store URL based on the platform
  Future<String?> _getStoreUrlString() async {
    if (kIsWeb) {
      return null; // No specific store URL for web
    }
    if (defaultTargetPlatform == TargetPlatform.iOS) {
      // On iOS, use the given app ID
      return 'https://apps.apple.com/app/id6695738311';
    } else if (defaultTargetPlatform == TargetPlatform.android) {
      final packageInfo = await PackageInfo.fromPlatform();
      // On Android, use the package name from PackageInfo
      return 'https://play.google.com/store/apps/details?id=${packageInfo.packageName}';
    } else {
      myLog(
        '[UpdateRequiredPage] Unsupported platform: $defaultTargetPlatform',
        LogLevel.error,
      );
      return null;
    }
  }

  // Function to launch the app store
  Future<void> _launchStore() async {
    final urlString = await _getStoreUrlString();
    if (urlString == null) {
      myLog('[UpdateRequiredPage] Store URL is null, cannot launch.',
          LogLevel.warning);
      return;
    }

    final Uri storeUri = Uri.parse(urlString);

    if (await url_launcher.canLaunchUrl(storeUri)) {
      await url_launcher.launchUrl(
        storeUri,
        mode: url_launcher.LaunchMode.externalApplication,
      );
    } else {
      myLog(
        '[UpdateRequiredPage] Cannot launch URL: $storeUri',
        LogLevel.error,
      );
    }
  }

  // Handles the update button press
  void _handleUpdate() async {
    setState(() {
      _loading = true;
    });

    await _launchStore(); // 스토어 실행

    // 스토어 실행 후 바로 로딩 상태 해제 (사용자는 이미 스토어로 이동)
    // 앱으로 돌아왔을 때의 처리는 didChangeAppLifecycleState에서 담당
    if (mounted) {
      setState(() {
        _loading = false;
      });
    }

    // --- Future.delayed 및 checkVersionState 호출 제거 ---
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black.withValues(alpha: 0.5), // bg-slate-900
      body: SafeArea(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 448),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.0),
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 8.0,
                      offset: Offset(0, 4),
                    ),
                  ],
                ),
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      FlutterI18n.translate(
                        context,
                        'app.info.version.validation.required',
                      ),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 18.0,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16.0),
                          backgroundColor: Theme.of(context).primaryColor,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                        onPressed: _loading ? null : _handleUpdate,
                        child: _loading
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    height: 20.0,
                                    width: 20.0,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2.0,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        LDColors.foundationLime,
                                      ),
                                    ),
                                  ),
                                ],
                              )
                            : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    FlutterI18n.translate(
                                      context,
                                      'app.info.version.validation.button',
                                    ),
                                    style: TextStyle(
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w700,
                                      color: Colors.black, // 텍스트 색상 확인 필요
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
