import 'package:flutter/material.dart';
import 'package:langda/presentation/pages/mobile/word_page/card_edit_page_widget.dart';

import '../../../../common/LDState.dart';

class WordCardListPageWidget extends StatefulWidget {
  const WordCardListPageWidget({super.key});

  @override
  State<WordCardListPageWidget> createState() => _WordCardListPageWidgetState();
}

class _WordCardListPageWidgetState extends LDState<WordCardListPageWidget> {
  bool _isEditing = false;
  @override
  void initState() {
    pageName = "wordCardList";
    super.initState();
  }

  @override
  buildContent(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('wordCardListPage'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              Navigator.push(context, MaterialPageRoute(builder: (context) {
                return const CardEditPageWidget();
              }));
            },
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.push(context, MaterialPageRoute(builder: (context) {
                return const CardEditPageWidget();
              }));
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              constraints: const BoxConstraints(
                minHeight: 50,
                maxHeight: 100,
              ),
              child: Column(
                children: const [
                  Row(
                    children: [
                      Text('WordCardPage'),
                    ],
                  ),
                ],
              ),
            ),
            ListView.builder(
              itemCount: 5,
              itemBuilder: (context, index) {
                if (index == 0) {
                  return ListTile(
                    title: wordCardListTileCell(
                      word: '단어',
                      editedAt: '수정날짜',
                      avgDaysForLearning: '평균복습일',
                      failureCount: '실피',
                    ),
                  );
                } else {
                  return ListTile(
                    leading: _isEditing
                        ? Checkbox(
                            value: false,
                            onChanged: (value) {},
                          )
                        : null,
                    title: wordCardListTileCell(
                        word: 'wdlkj',
                        editedAt: '20202',
                        avgDaysForLearning: '2일',
                        failureCount: '3'),
                    // trailing: Text('1'),
                    // subtitle: Text('WordCardPage'),
                    onTap: () {},
                  );
                }
              },
              shrinkWrap: true,
            ),
          ],
        ),
      ),
    );
  }

  Row wordCardListTileCell({
    required String word,
    required String editedAt,
    required String avgDaysForLearning,
    required String failureCount,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.max,
      children: [
        Flexible(
          flex: 2,
          child: Text(
            word,
            style: TextStyle(fontSize: 12),
          ),
        ),
        Flexible(
          flex: 1,
          child: Text(
            editedAt,
            style: TextStyle(fontSize: 12),
          ),
        ),
        Flexible(
          flex: 1,
          child: Text(
            avgDaysForLearning,
            style: TextStyle(fontSize: 12),
          ),
        ),
        FittedBox(
          child: Text(
            failureCount,
            style: TextStyle(fontSize: 12),
          ),
        ),
      ],
    );
  }
}
