import 'package:flutter/material.dart';
import 'package:langda/common/ld_field.dart';

import '../../component.dart';

class CardEditPageWidget extends StatefulWidget {
  const CardEditPageWidget({super.key});

  @override
  State<CardEditPageWidget> createState() => _CardEditPageWidgetState();
}

class _CardEditPageWidgetState extends State<CardEditPageWidget> {
  final TextEditingController _wordTextController = TextEditingController();
  final GlobalKey<FormState> _wordTextKey = GlobalKey<FormState>();
  final FocusNode _wordTextFocusNode = FocusNode();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('ddd'),
      ),
      body: Safe<PERSON>rea(
        child: Padding(
          padding: EdgeInsets.all(8.0),
          child: Column(
            children: [
              L<PERSON>ield(
                title: 'title',
                hintText: 'hintText',
                fieldKey: _wordTextKey,
                focusNode: _wordTextFocusNode,
                controller: _wordTextController,
              ),
              // LDField(
              //   title: 'title',
              //   hintText: 'hintText',
              //   fieldKey: _wordTextKey,
              //   focusNode: _wordTextFocusNode,
              //   controller: _wordTextController,
              // ),
              // LDField(
              //   title: 'title',
              //   hintText: 'hintText',
              //   fieldKey: _wordTextKey,
              //   focusNode: _wordTextFocusNode,
              //   controller: _wordTextController,
              // ),
              LDButton(
                label: '',
                onPressed: () {},
              ),
            ],
          ),
        ),
      ),
    );
  }
}
