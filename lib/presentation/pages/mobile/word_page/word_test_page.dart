import 'dart:math';

import 'package:appinio_swiper/appinio_swiper.dart';
import 'package:flip_card/flip_card.dart';
import 'package:flutter/material.dart';
import 'package:langda/presentation/pages/mobile/word_page/word_card_List_page_widget.dart';
import 'package:star_menu/star_menu.dart';

import '../../component.dart';
import '../constants.dart';

enum MenuState {
  closed,
  closing,
  opening,
  open,
}

class WordTestPage extends StatefulWidget {
  const WordTestPage({super.key});

  @override
  State<WordTestPage> createState() => _WordTestPageState();
}

class _WordTestPageState extends State<WordTestPage> {
  bool _showAnswer = false;
  final controller = AppinioSwiperController();
  StarMenuController starMenuController = StarMenuController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('WordTestPage'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              Navigator.push(context, MaterialPageRoute(builder: (context) {
                return const WordCardListPageWidget();
              }));
            },
          ),
          StarMenu(
            params: StarMenuParameters(
              gridShapeParams: GridShapeParams(),
            ),
            onStateChanged: (state) => print('State changed: $state'),
            onItemTapped: (index, controller) {
              // here you can programmatically close the menu
              // if (index == 7) controller.closeMenu();
              print('Menu item $index tapped');
            },
            lazyItems: () async {
              return [
                Container(
                  color: Color.fromARGB(255, Random().nextInt(255),
                      Random().nextInt(255), Random().nextInt(255)),
                  width: 60,
                  height: 40,
                  child: Text('lorem'),
                ),
                Container(
                  color: Color.fromARGB(255, Random().nextInt(255),
                      Random().nextInt(255), Random().nextInt(255)),
                  width: 60,
                  height: 40,
                  child: Text('lorem'),
                ),
              ];
            },
            // items: [
            //   StarMenuItem(
            //     icon: Icon(Icons.looks_one),
            //     title: 'One',
            //     onTap: () => print('One tapped'),
            //   ),
            //   StarMenuItem(
            //     icon: Icon(Icons.looks_two),
            //     title: 'Two',
            //     onTap: () => print('Two tapped'),
            //   ),
            //   StarMenuItem(
            //     icon: Icon(Icons.looks_3),
            //     title: 'Three',
            //     onTap: () => print('Three tapped'),
            //   ),
            // ],
            child: FloatingActionButton(
              onPressed: () {
                print('FloatingActionButton tapped');
              },
              child: Icon(Icons.looks_one),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(8.0),
                height: 300,
                child: AppinioSwiper(
                  invertAngleOnBottomDrag: true,
                  backgroundCardCount: 1,
                  swipeOptions: const SwipeOptions.all(),
                  controller: controller,
                  onCardPositionChanged: (
                    SwiperPosition position,
                  ) {
                    //debugPrint('${position.offset.toAxisDirection()}, '
                    //    '${position.offset}, '
                    //    '${position.angle}');
                  },
                  onSwipeEnd: _swipeEnd,
                  onEnd: _onEnd,
                  cardCount: 5,
                  cardBuilder: (BuildContext context, int index) {
                    return FlipCard(
                        front: Card(
                          elevation: 4,
                          color: Colors.white,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text('WordTestPage'),
                                ],
                              ),
                              Text('WordTestPage'),
                            ],
                          ),
                        ),
                        back: Card(
                          elevation: 4,
                          color: Colors.white,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text('WordTestPage'),
                                ],
                              ),
                              Text('WordTestPage'),
                            ],
                          ),
                        ));
                  },
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  testResultButton(
                    context,
                    text: '다시',
                    onTap: () {},
                    buttonColor: LDColors.mainRed,
                  ),
                  testResultButton(
                    context,
                    text: '어려움',
                    onTap: () {},
                    buttonColor: LDColors.mainGrey,
                  ),
                  testResultButton(
                    context,
                    text: '맞음',
                    onTap: () {},
                    buttonColor: LDColors.foundationLime,
                  ),
                  testResultButton(
                    context,
                    text: '쉬움',
                    onTap: () {},
                    buttonColor: LDColors.darkHoverGrey,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Flexible testResultButton(
    BuildContext context, {
    required String text,
    required Function() onTap,
    required Color buttonColor,
  }) {
    return Flexible(
      child: Padding(
        padding: const EdgeInsets.all(4.0),
        child: LDButton(
          backgroundColor: buttonColor,
          label: text,
          onPressed: () {
            setState(() {
              _showAnswer = true;
            });
            onTap;
          },
        ),
      ),
    );
  }

  void _swipeEnd(int previousIndex, int targetIndex, SwiperActivity activity) {
    switch (activity) {
      case Swipe():
        // log('The card was swiped to the : ${activity.direction}');
        // log('previous index: $previousIndex, target index: $targetIndex');
        break;
      case Unswipe():
        // log('A ${activity.direction.name} swipe was undone.');
        // log('previous index: $previousIndex, target index: $targetIndex');
        break;
      case CancelSwipe():
        // log('A swipe was cancelled');
        break;
      case DrivenActivity():
        // log('Driven Activity');
        break;
    }
  }

  void _onEnd() {
    // log('end reached!');
  }
}
