import 'package:flutter/material.dart';

import '../../../../common/ld_field.dart';

class CardDeckEditPage extends StatefulWidget {
  const CardDeckEditPage({super.key});

  @override
  State<CardDeckEditPage> createState() => _CardDeckEditPageState();
}

class _CardDeckEditPageState extends State<CardDeckEditPage> {
  final TextEditingController _wordTextController = TextEditingController();
  final GlobalKey<FormState> _wordTextKey = GlobalKey<FormState>();
  final FocusNode _wordTextFocusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('단어 카드 덱 추가'),
        actions: [
          IconButton(
            icon: const Icon(
              Icons.save,
            ),
            onPressed: () {
              // Navigator.push(context, MaterialPageRoute(builder: (context) {
              //   return const CardEditPageWidget();
              // }));
            },
          ),
        ],
      ),
      body: Safe<PERSON>rea(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              const Text('단어 카드 덱 추가'),
              LDField(
                title: '덱 이름',
                hintText: 'hintText',
                fieldKey: _wordTextKey,
                focusNode: _wordTextFocusNode,
                controller: _wordTextController,
              ),
              Text(
                '단어 카드 추가',
              ),
              Expanded(
                  child: ListView.builder(
                itemCount: 10,
                itemBuilder: (context, index) {
                  return ListTile(
                    title: Text('단어 $index'),
                    subtitle: Text('뜻 $index'),
                  );
                },
              )),
            ],
          ),
        ),
      ),
    );
  }
}
