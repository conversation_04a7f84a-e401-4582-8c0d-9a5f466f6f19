import 'package:flutter/material.dart';
import 'package:langda/common/LDState.dart';

import '../../../../backend/model/deck_model.dart';
import 'card_deck_edit_page.dart';
import 'word_test_page.dart';

class WordCardDeckListPage extends StatefulWidget {
  const WordCardDeckListPage({super.key});

  @override
  State<WordCardDeckListPage> createState() => _WordCardDeckListPageState();
}

class _WordCardDeckListPageState extends LDState<WordCardDeckListPage> {
  List<DeckModelStructure> decks = [
    // DeckModelStructure(
    //   id: '1',
    //   name: 'Deck 1',
    //   description: 'Deck 1 Description',
    //   wordCount: 10,
    //   wordCountToday: 5,
    //   createdAt: DateTime.now(),
    //   updatedAt: DateTime.now(),
    //   deletedAt: null,
    // ),
  ];
  buildContent(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('단어 카드 덱 목록'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.push(context, MaterialPageRoute(builder: (context) {
                return const CardDeckEditPage();
              }));
            },
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // Navigator.push(context, MaterialPageRoute(builder: (context) {
              //   return const CardEditPageWidget();
              // }));
            },
          ),
        ],
      ),
      body: SafeArea(
        child: ListView.builder(
          itemCount: decks.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.5),
                      spreadRadius: 2,
                      blurRadius: 5,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child:
                    _wordCardDeckListTileBuilder(context, deck: decks[index]),
              ),
            );
          },
          shrinkWrap: true,
        ),
      ),
    );
  }

  ListTile _wordCardDeckListTileBuilder(BuildContext context,
      {required DeckModelStructure deck}) {
    return ListTile(
      title: Text('WordCardPage'),
      trailing: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            deck.wordCount.toString(),
            style: TextStyle(
              fontSize: 15,
            ),
          ),
          SizedBox(width: 10),
          Text(
            deck.wordCountToday.toString(),
            style: TextStyle(
              fontSize: 15,
            ),
          ),
          SizedBox(width: 10),
          Icon(
            Icons.arrow_forward_ios,
            size: 20,
          ),
        ],
      ),
      onTap: () {
        Navigator.push(context, MaterialPageRoute(builder: (context) {
          return const WordTestPage(
              // deck: deck,
              );
        }));
      },
    );
  }
}
