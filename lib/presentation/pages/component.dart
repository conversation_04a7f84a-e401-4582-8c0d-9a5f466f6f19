import 'package:flutter/material.dart';
import 'package:langda/presentation/pages/mobile/constants.dart';

class LDButton extends StatelessWidget {
  LDButton({
    required this.label,
    this.backgroundColor,
    this.textColor,
    required this.onPressed,
    this.isActivated = true,
    this.width = null,
    this.height = null,
    this.fonSize = null,
  });

  final String label;
  final VoidCallback onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final bool isActivated;
  final double? width;
  final double? height;
  final double? fonSize;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Ink(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: isActivated
              ? backgroundColor ?? Theme.of(context).colorScheme.primary
              : LDColors.lightGrey,
        ),
        child: Semantics(
          button: true,
          label: label,
          enabled: isActivated,
          child: InkWell(
            onTap: isActivated ? onPressed : null,
            borderRadius: BorderRadius.circular(8), // InkWell에도 borderRadius 추가
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: [
                SizedBox(
                  width: width,
                  height: height,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    child: Text(
                      label,
                      style: TextStyle(
                        color: textColor ?? Colors.black,
                        fontSize: fonSize ?? 14,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class LDSpinIconButton extends StatefulWidget {
  final ImageIcon icon;
  final Color backgroundColor;
  final void Function() onPressed;
  final String semanticLabel;
  const LDSpinIconButton(
      {super.key,
      required this.icon,
      required this.backgroundColor,
      required this.onPressed,
      required this.semanticLabel});

  @override
  State<LDSpinIconButton> createState() => _LDSpinIconButtonState();
}

class _LDSpinIconButtonState extends State<LDSpinIconButton>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller = AnimationController(
    duration: const Duration(milliseconds: 1500),
    reverseDuration: const Duration(milliseconds: 300),
    vsync: this,
  );
  late final Animation<double> _animation = CurvedAnimation(
    parent: _controller,
    curve: Curves.elasticOut,
  );
  @override
  void initState() {
    _controller.forward();

    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Semantics(
      button: true,
      label: widget.semanticLabel,
      child: InkWell(
        onTap: () async {
          await _controller.reverse();
          widget.onPressed();
        },
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: RotationTransition(
            turns: _animation,
            child: widget.icon,
          ),
        ),
      ),
    );
  }
}

enum ToastType {
  error,
  success,
  info,
  warning,
}

Align LDtoast(String message, ToastType type, {double? maxWidth}) {
  return Align(
    alignment: Alignment.bottomCenter,
    child: type == ToastType.error
        ? Container(
            constraints: BoxConstraints(
              maxWidth: maxWidth ?? 400,
            ),
            padding:
                const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25.0),
              color: Colors.redAccent,
            ),
            child: Row(
              children: [
                const Image(
                  image:
                      AssetImage('assets/images/icons/alert-triangle_1.5.png'),
                  height: 24.0,
                  width: 24.0,
                  color: Colors.white,
                ),
                const SizedBox(
                  width: 12.0,
                ),
                Expanded(
                  child: Text(
                    message,
                    softWrap: true,
                    maxLines: 3,
                  ),
                ),
              ],
            ),
          )
        : (type == ToastType.success)
            ? Container(
                constraints: BoxConstraints(
                  maxWidth: maxWidth ?? 400,
                ),
                padding: const EdgeInsets.symmetric(
                    horizontal: 24.0, vertical: 12.0),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25.0),
                  color: Colors.green,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.check,
                      color: Colors.white,
                    ),
                    const SizedBox(
                      width: 12.0,
                    ),
                    Expanded(
                      child: Text(
                        message,
                      ),
                    ),
                  ],
                ),
              )
            : (type == ToastType.info)
                ? Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24.0, vertical: 12.0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(25.0),
                      color: LDColors.mainBlue,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.info, color: Colors.white),
                        const SizedBox(
                          width: 12.0,
                        ),
                        Container(
                          child: Expanded(
                            child: Text(
                              message,
                              style: const TextStyle(
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                : Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24.0, vertical: 12.0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(25.0),
                      color: Colors.yellow,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.warning),
                        const SizedBox(
                          width: 12.0,
                        ),
                        Container(
                          child: Text(
                            message,
                            style: const TextStyle(
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
  );
}

const Image gemIcon = Image(
  image: AssetImage(
    'assets/images/gems.png',
  ),
  height: 20.0,
  width: 20.0,
);
