import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../pages/mobile/constants.dart';

class HomeNavListBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onItemTapped;
  const HomeNavListBar({
    super.key,
    required this.currentIndex,
    required this.onItemTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: defaultPadding),
          Row(
            children: [
              Image.asset(
                'assets/images/logos/langda.png',
                width: 20.sp,
              ),
            ],
          ),
          SizedBox(height: defaultPadding),
          Container(
            decoration: BoxDecoration(
              color: currentIndex == 0 ? LDColors.darkGrey : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ListTile(
              selected: currentIndex == 0,
              leading: ImageIcon(
                AssetImage(
                  'assets/images/icons/calendar_1.5-512.png',
                ),
              ),
              title: Text(
                FlutterI18n.translate(context, 'navigation.diary'),
              ),
              onTap: () => onItemTapped(
                0,
              ),
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: currentIndex == 1 ? LDColors.darkGrey : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ListTile(
              selected: currentIndex == 1,
              leading: ImageIcon(
                AssetImage(
                  'assets/images/icons/bookmark_1.5-512.png',
                ),
              ),
              title: Text(FlutterI18n.translate(context, 'navigation.card')),
              onTap: () => onItemTapped(
                1,
              ),
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: currentIndex == 2 ? LDColors.darkGrey : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ListTile(
              selected: currentIndex == 2,
              leading: ImageIcon(
                AssetImage(
                  'assets/images/icons/pen-tool-02_1.5-l.png',
                ),
              ),
              title:
                  Text(FlutterI18n.translate(context, 'navigation.practice')),
              onTap: () => onItemTapped(
                2,
              ),
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: currentIndex == 3 ? LDColors.darkGrey : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ListTile(
              selected: currentIndex == 3,
              leading: ImageIcon(
                AssetImage(
                  'assets/images/icons/user-03_1.5-mg.png',
                ),
              ),
              title: Text(
                FlutterI18n.translate(
                  context,
                  'navigation.profile',
                ),
              ),
              onTap: () => onItemTapped(
                3,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
