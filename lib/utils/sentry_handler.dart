import 'package:flutter/foundation.dart' show kDebugMode, kReleaseMode;
import 'package:sentry_flutter/sentry_flutter.dart';

class SentryHandler {
  static Future<void> init() async {
    if (kReleaseMode) {
      await SentryFlutter.init(
        (options) {
          options.dsn =
              'https://<EMAIL>/4508742627295312';
          options.environment = 'production';
          options.debug = false;
          options.beforeSend = (event, hint) {
            if (kDebugMode) {
              return null;
            }

            return event;
          };
        },
      );
    }
  }

  static void reportError(dynamic error, dynamic stackTrace) {
    print('Caught error: $error');
    if (stackTrace != null) {
      print(stackTrace);
    }

    if (kReleaseMode) {
      Sentry.captureException(
        error,
        stackTrace: stackTrace,
      );
    }
  }
}
