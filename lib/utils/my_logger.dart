import 'dart:async';
import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

enum LogLevel { debug, info, warning, error }

void myLog(String message, [LogLevel level = LogLevel.info]) {
  if (kDebugMode) {
    if (level == LogLevel.debug) {
      log(
        'DEBUG: $message',
        zone: Zone.current,
        stackTrace: StackTrace.current,
      );
    } else {
      log('INFO: $message');
    }
  } else if (level == LogLevel.warning) {
    log('WARNING: $message');
  } else if (level == LogLevel.error) {
    log(
      'ERROR: $message',
      zone: Zone.current,
      error: Exception(message),
      stackTrace: StackTrace.current,
    );
    Sentry.captureException(
      Exception(message),
      stackTrace: StackTrace.current,
    );
  }
}
