import 'package:flutter/widgets.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

extension WeatherIcon on FontAwesomeIcons {
  static const Map<String, Widget> _weatherIcons = {
    'sunny': ImageIcon(
      AssetImage('assets/images/icons/weather/sun_1.5.png'),
      size: 20,
    ),
    'rainy': ImageIcon(
      AssetImage('assets/images/icons/weather/cloud-raining-04_1.5.png'),
      size: 20,
    ),
    'snowy': ImageIcon(
      AssetImage('assets/images/icons/weather/cloud-snowing-01_1.5.png'),
      size: 20,
    ),
    'foggy': Icon(
      FontAwesomeIcons.smog,
      size: 20,
    ),
    'cloudy': ImageIcon(
      AssetImage('assets/images/icons/weather/cloud-01_1.5.png'),
      size: 20,
    ),
    'thunder': Icon(
      FontAwesomeIcons.bolt,
      size: 20,
    ),
  };

  static Widget getWeatherIcon(String weather) {
    return _weatherIcons[weather] ?? _weatherIcons['sunny']!;
  }

  static String getWeatherIconName(Widget icon) {
    return _weatherIcons.entries
        .firstWhere(
          (entry) => entry.value == icon,
          orElse: () => MapEntry('sunny', _weatherIcons['sunny']!),
        )
        .key;
  }

  static List<String> getWeatherIconsNameList() {
    return _weatherIcons.keys.toList();
  }

  static Map<String, Widget> getWeatherIconList() {
    return _weatherIcons;
  }
}
