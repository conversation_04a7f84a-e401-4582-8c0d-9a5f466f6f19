import 'dart:io' show Platform;

import 'package:flutter/services.dart';
import 'package:langda/utils/my_logger.dart' as logger;
import 'package:purchases_flutter/purchases_flutter.dart';

Future<void> initPlatformState() async {
  await Purchases.setLogLevel(LogLevel.debug);

  PurchasesConfiguration configuration = PurchasesConfiguration('');
  if (Platform.isAndroid) {
    configuration = PurchasesConfiguration('goog_UfvqUSkbrLLDSXoxeTmvjWqQAqs');
  } else if (Platform.isIOS) {
    configuration = PurchasesConfiguration('appl_GxYxJZZtWoSbHLmOLkEculddfeV');
  }
  await Purchases.configure(configuration);
}

Future<void> login(String userId) async {
  try {
    await Purchases.logIn(userId);
  } on PlatformException catch (e) {
    logger.myLog(
        'Purchases login error : ${e.toString()}', logger.LogLevel.error);
  }
}

Future<void> logout() async {
  await Purchases.logOut();
}

Future<List<Package?>> getOfferings() async {
  try {
    Offerings offerings = await Purchases.getOfferings();
    if (offerings.current != null &&
        offerings.current!.availablePackages.isNotEmpty) {
      return offerings.current!.availablePackages;
    }
  } on PlatformException catch (e) {
    logger.myLog('getOffers error : ${e.toString()}', logger.LogLevel.error);
  }
  return [];
}

Future<Map<String, String>?> userSubscribInfo() async {
  String? expirationDate = '';
  try {
    CustomerInfo info = await customerInfo();
    final entitlements = info.entitlements.all['LangDa'];
    if (entitlements == null) {
      return null;
    }

    final productPlanIdentifier = Platform.isAndroid
        ? entitlements.productPlanIdentifier
        : entitlements.productIdentifier;
    expirationDate = entitlements.expirationDate;

    if (entitlements.isActive && productPlanIdentifier != null) {
      if (Platform.isAndroid) {
        if (productPlanIdentifier.contains('annual')) {
          return {'status': 'subscribed_annual'};
        } else if (productPlanIdentifier.contains('monthly')) {
          return {'status': 'subscribed_monthly'};
        }
      } else {
        if (productPlanIdentifier.contains('Annual')) {
          return {'status': 'subscribed_annual'};
        } else if (productPlanIdentifier.contains('Monthly')) {
          return {'status': 'subscribed_monthly'};
        }
      }
    }

    if (expirationDate != null) {
      final DateTime parsedDate = DateTime.parse(expirationDate);

      if (parsedDate.isBefore(DateTime.now())) {
        return {'status': 'expired'};
      }
    }
  } catch (e) {
    logger.myLog(
        'userSubscribInfo error : ${e.toString()}', logger.LogLevel.error);
  }

  return null;
}

Future<CustomerInfo> customerInfo() async {
  CustomerInfo customerInfo = await Purchases.getCustomerInfo();
  return customerInfo;
}

Future<Map<bool, String?>> makePurchase(Package package) async {
  try {
    await Purchases.purchasePackage(package);
    return {true: null};
  } on PlatformException catch (e) {
    var errorCode = PurchasesErrorHelper.getErrorCode(e);
    if (errorCode != PurchasesErrorCode.purchaseCancelledError) {
      logger.myLog(
          'makePurchase error : ${e.toString()}', logger.LogLevel.error);

      return {false: e.toString()};
    }
  }

  return {false: null};
}

Future<void> restorePurchases() async {
  try {
    await Purchases.restorePurchases();
  } on PlatformException catch (e) {
    logger.myLog(
        'restorePurchases error : ${e.toString()}', logger.LogLevel.error);
  }
}
