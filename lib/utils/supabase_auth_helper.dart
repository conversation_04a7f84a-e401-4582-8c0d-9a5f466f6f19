import 'dart:convert';
import 'dart:io';

import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:kakao_flutter_sdk_user/kakao_flutter_sdk_user.dart';
import 'package:langda/utils/my_logger.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

Future<void> signUpNewUser(String email, String password) async {
  try {
    await Supabase.instance.client.auth.signUp(
      email: email,
      password: password,
    );
  } catch (e) {
    throw e;
  }
}

Future<void> signInWithMagicLink(
  String email,
) async {
  try {
    await Supabase.instance.client.auth.signInWithOtp(
      email: email,
      emailRedirectTo: 'com.oibori.langda://sign-in-magic-link',
      shouldCreateUser: false,
    );
  } catch (e) {
    throw e;
  }
}

Future<void> signUpWithMagicLink(
  String email,
) async {
  try {
    await Supabase.instance.client.auth.signInWithOtp(
      email: email,
      emailRedirectTo: 'com.oibori.langda://sign-up-magic-link',
      shouldCreateUser: true,
    );
  } catch (e) {
    throw e;
  }
}

Future<void> signInWithEmail(String email, String password) async {
  await Supabase.instance.client.auth
      .signInWithPassword(password: password, email: email);
}

Future<String> kakaoLoginHelper({
  List<Prompt>? prompts,
  List<String>? channelPublicIds,
  List<String>? serviceTerms,
  String? loginHint,
  String? nonce,
}) async {
  final clientId = 'e7d5fa47e2b1b454ca7148a588bf7d0c';
  final codeVerifier = AuthCodeClient.codeVerifier();
  final redirectUri = KakaoSdk.redirectUri;

  final authCode = await AuthCodeClient.instance.authorize(
    redirectUri: redirectUri,
    prompts: prompts,
    channelPublicIds: channelPublicIds,
    serviceTerms: serviceTerms,
    codeVerifier: codeVerifier,
    loginHint: loginHint,
    nonce: nonce,
    webPopupLogin: true,
  );

  try {
    final dio = Dio();
    final response = await dio.post(
      'https://kauth.kakao.com/oauth/token',
      options: Options(
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
        },
      ),
      data: {
        'grant_type': 'authorization_code',
        'client_id': clientId,
        'redirect_uri': redirectUri,
        'code': authCode,
        'client_secret': 'mAESCprNTuqU3D9XuvRyh3dC3X3JM6vg',
      },
    );

    final responseData = response.data;
    return responseData['id_token'];
  } catch (e) {
    myLog(
      '[kakaoLoginHelper] Error: $e',
      LogLevel.error,
    );
    return '';
  }
}

Future<bool> signInWithKakao() async {
  try {
    OAuthToken? tokens = null;
    if (Platform.isIOS) {
      tokens = await UserApi.instance.loginWithKakaoAccount(
        prompts: [Prompt.selectAccount],
      );
    } else {
      tokens = await UserApi.instance.loginWithKakaoTalk().onError(
        (error, stackTrace) {
          if (error is PlatformException &&
              error.message != null &&
              error.message!.contains('KakaoTalk is not installed')) {
            return UserApi.instance.loginWithKakaoAccount(
              prompts: [Prompt.selectAccount],
            );
          } else {
            myLog(
              '[signInWithKakao] Error: $error',
              LogLevel.error,
            );
            throw error as Object;
          }
        },
      );
    }

    final idToken = tokens.idToken;
    if (idToken == null) {
      throw const AuthException(
          'Could not find ID Token from generated credential.');
    } else {
      final user = await Supabase.instance.client.auth.signInWithIdToken(
        provider: OAuthProvider.kakao,
        idToken: idToken,
      );
      return user.user != null;
    }
  } catch (e) {
    myLog(
      '[signInWithKakao] Error: $e',
      LogLevel.error,
    );
    throw e;
  }
}

Future<bool> signInWithGoogle() async {
  const webClientId =
      '************-vb2ng7epgpotc867cg6420mah6j18lvd.apps.googleusercontent.com';

  var clientId = null;
  if (Platform.isIOS) {
    clientId =
        '************-9cpngpr087392v4jsn41ppt1qt2bs3ct.apps.googleusercontent.com';
  }

  final GoogleSignIn googleSignIn = GoogleSignIn(
    clientId: clientId,
    serverClientId: webClientId,
    scopes: ["profile", "email"],
  );
  GoogleSignInAccount? googleUser;
  try {
    googleUser = await googleSignIn.signIn();
  } catch (e) {
    myLog(
      '[signInWithGoogle] Error: $e',
      LogLevel.error,
    );
    return false;
  }
  if (googleUser == null) {
    return false;
  }

  final googleAuth = await googleUser.authentication;
  final accessToken = googleAuth.accessToken;
  final idToken = googleAuth.idToken;

  if (accessToken == null) {
    // #SentryError
    Sentry.captureException('No Access Token found.');
    return false;
  }
  if (idToken == null) {
    // #SentryError
    Sentry.captureException('No ID Token found.');
    return false;
  }
  final user = await Supabase.instance.client.auth.signInWithIdToken(
    provider: OAuthProvider.google,
    idToken: idToken,
    accessToken: accessToken,
  );

  return user.user != null;
}

Future<bool> signInWithApple() async {
  final rawNonce = Supabase.instance.client.auth.generateRawNonce();
  final hashedNonce = sha256.convert(utf8.encode(rawNonce)).toString();

  final credential = await SignInWithApple.getAppleIDCredential(
    scopes: [
      AppleIDAuthorizationScopes.email,
      AppleIDAuthorizationScopes.fullName,
    ],
    nonce: hashedNonce,
  );

  final idToken = credential.identityToken;
  if (idToken == null) {
    throw const AuthException(
        'Could not find ID Token from generated credential.');
  }

  final user = await Supabase.instance.client.auth.signInWithIdToken(
    provider: OAuthProvider.apple,
    idToken: idToken,
    nonce: rawNonce,
  );

  return user.user != null;
}

Future<void> resetPassword(String email) async {
  try {
    await Supabase.instance.client.auth.resetPasswordForEmail(
      email,
      redirectTo: 'com.oibori.langda://reset-password',
    );
  } catch (e) {
    myLog(
      '[resetPassword] Error: $e',
      LogLevel.error,
    );
  }
}

String? currentUserId() {
  return Supabase.instance.client.auth.currentUser?.id;
}
