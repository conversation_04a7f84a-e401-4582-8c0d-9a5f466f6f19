import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:langda/backend/langda_api_client.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

import '../presentation/pages/mobile/home_page/my_page/subscribe_page/component/product_box.dart';
import '../utils/my_logger.dart' as my_logger show LogLevel, myLog;
import '../utils/revenucat_helper.dart';
import 'key_constants.dart';

final Query<ProductBoxModel> Function() fetchProductBoxModelQuery = () => Query(
      queryFn: () async {
        ProductBoxModel productBoxModel = ProductBoxModel(
          offerings: [],
          customerInfo: null,
        );
        final customerInfoFuture = customerInfo();
        final offeringsFuture = getOfferings();

        // Get customer info and offerings
        final result = await Future.wait([customerInfoFuture, offeringsFuture]);

        productBoxModel.customerInfo = result[0] as CustomerInfo?;
        productBoxModel.offerings = result[1] as List<Package?>;

        return productBoxModel;
      },
      key: productBoxKey(),
      onError: (e) {
        my_logger.myLog(
          '[Fetch Product Box Model Query onError] e: $e',
          my_logger.LogLevel.error,
        );
      },
    );

final Query<int> Function() fetchDeepExplanationCostQuery = () => Query(
      queryFn: () async {
        try {
          final result =
              await LangdaApiClient.client.currency.getCurrencyFeatureCosts();
          return result.featureCosts.deepExplanation.cost.toInt();
        } catch (e) {
          my_logger.myLog(
            '[Fetch Gem Cost Query] e: $e',
            my_logger.LogLevel.error,
          );
          return 0;
        }
      },
      key: QueryKeys.gemCostKey('deep_explanation'),
      onError: (e) {
        my_logger.myLog(
          '[Fetch Gem Cost Query onError] e: $e',
          my_logger.LogLevel.error,
        );
      },
    );

String productBoxKey() => 'product_box';
