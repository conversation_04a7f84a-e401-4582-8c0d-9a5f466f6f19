class QueryKeys {
  static String userKey(String userId) => 'user$userId';
  // Diary
  static String diaryKey(String entryId) => 'diary$entryId';
  static List<String> monthlyDiaryKey(DateTime date) => [
        'diaryList',
        date.year.toString(),
        date.month.toString(),
      ];
  // Card
  static String cardListKey(String entryId) => 'cardList$entryId';
  static String cardKey(String entryId) => 'card$entryId';

  // Products
  static String gemCostKey(String name) => 'gemCost$name';
}
