import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:intl/intl.dart';
import 'package:langda/api/export.dart';
import 'package:langda/backend/langda_api_client.dart';
import 'package:langda/backend/service/currency_service.dart';
import 'package:langda/queries/key_constants.dart';
import 'package:langda/utils/my_logger.dart';
import 'package:posthog_flutter/posthog_flutter.dart';

import '../backend/model/diary_model.dart';
import '../backend/model/user_model.dart';
import '../backend/service/diary_service.dart';
import '../common/notification_box.dart';
import '../presentation/pages/mobile/home_page/diary_page/component/diary_write_button.dart';
import '../presentation/pages/mobile/home_page/my_page/profile_page/daily-reward/daily_reward_service.dart';
import '../presentation/pages/mobile/home_page/my_page/profile_page/daily-reward/daily_reward_types.dart';
import '../utils/supabase_auth_helper.dart';
import 'auth_query.dart';
import 'card_query.dart';

final Query<Map<String, int>> Function(DateTime date) fetchNumberOfDiaryQuery =
    (DateTime date) => Query(
          queryFn: () async {
            return await DiaryService().fetchNumberOfDiary(date);
          },
          key: 'number_of_notes',
          onError: (e) {
            myLog(
              '[Fetch Number of Notes Query onError] e: $e',
              LogLevel.error,
            );
          },
        );

final Query<List<DiaryListModelStruct>> Function(int year, int month)
    fetchDiaryListWithDateQuery = (int year, int month) => Query(
          queryFn: () async {
            return await DiaryService()
                .fetchDiaryList(year: year, month: month);
          },
          key: diaryListKey(year, month),
          onError: (e) {
            myLog(
              '[Fetch Diary List Query onError] e: $e',
              LogLevel.error,
            );
          },
        );

final Query<DiaryListModelStruct?> Function(String entryId) getDiaryQuery =
    (String entryId) => Query(
        queryFn: () async {
          final result = fetchDiaryListWithDateQuery(
            DateTime.now().year,
            DateTime.now().month,
          );
          final noteList = await result.result;
          final note = noteList.data?.firstWhere(
            (note) => note.id == entryId,
            orElse: () => DiaryListModelStruct(
              id: '',
              date: DateTime.now(),
              weather: 'sunny',
              originalVersion: null,
              correctedVersion: null,
            ),
          );
          if (note == null) {
          } else {
            return note;
          }

          return null;
        },
        key: QueryKeys.diaryKey(entryId));

final Query<List<List<List<LdCharacter>>>> Function(String, String, bool)
    fetchParagraphsQuery =
    (String versionId, String content, bool isOriginal) => Query(
          queryFn: () async {
            return await DiaryService()
                .getParagraphs(versionId, content, isOriginal);
          },
          key: 'paragraphs$versionId',
          onError: (e) {
            myLog(
              '[Fetch Paragraphs Query onError] e: $e',
              LogLevel.error,
            );
          },
        );

final Query<DailyRewardViewModel> Function() fetchDiaryRewardQuery =
    () => Query(
          queryFn: () async {
            final rewards = await LangdaApiClient.client.currency
                .getCurrencyRewardsStreakCalendar();
            return createRewardsViewModel(rewards);
          },
          key: fetchDiaryRewardKey(),
          onError: (e) {
            myLog(
              '[Fetch Diary Reward Query onError] e: $e',
              LogLevel.error,
            );
          },
        );

// 주보 버전
final Mutation<DiaryEntryResponse?, void> Function(String, String, DateTime)
    addDiaryMutationJubo =
    (String content, String weather, DateTime date) => Mutation(
            queryFn: (args) async {
              String? userId = currentUserId();
              String? entryDate = '';
              if (userId == null) {
                myLog('User is not logged in: from addNoteMutation');
                return null;
              } else {
                final entryDateResult =
                    await getCurrentEntryDateQuery(userId).result;
                if (entryDateResult.data == null) {
                  myLog('Current entry date is null');
                  return null;
                } else {
                  entryDate = entryDateResult.data;
                  if (entryDate!.startsWith('"')) {
                    entryDate = entryDate.substring(1, entryDate.length - 1);
                  }

                  final body = DiaryInsertionRequestBody(
                    userId: userId,
                    content: content,
                    weather: weather,
                    date: entryDate,
                  );
                  final res =
                      await LangdaApiClient.client.diary.postInsert(body: body);

                  return res;
                }
              }
            },
            onSuccess: (res, arg) async {
              if (res == null) {
                myLog('[Add Note Mutation onSuccess] res is null');
                return;
              }

              CurrencyService.instance.updateCurrencyBalance(res);

              myLog('[Add Note Mutation onSuccess] res: $res');
              final queries = CachedQuery.instance.whereQuery(
                  (query) => query.key.startsWith(allCardsKeyPrefix));
              queries?.forEach((query) {
                query.invalidateQuery();
              });

              final userId = currentUserId();
              String nickname = 'Langda'; // Default nickname

              if (userId != null) {
                final userInfoQuery = fetchUserInfoQuery();
                final userInfoResult = await userInfoQuery.result;
                final userInfo = userInfoResult.data;
                if (userInfo != null) {
                  if (userInfo.nickname.isNotEmpty) {
                    nickname = userInfo.nickname;
                  }
                }
              }

              int? amount = res.currencyGrants.map(
                (grant) {
                  return grant.when(
                    streakMilestone: (amount, type, milestone) => null,
                    periodicReward: (amount, type, period, subscriptionType) {
                      if (subscriptionType == SubscriptionType.trial) {
                        if (period == PeriodicGrantPeriod.initial) {
                          myLog(
                            'Trial Periodic Reward: $amount, Period: $period, Subscription Type: $subscriptionType',
                          );
                          return amount;
                        }
                      }
                      return null;
                    },
                    achievement:
                        (amount, type, achievementId, achievementType) => null,
                  );
                },
              ).firstOrNull;

              final notificationQuery =
                  fetchNotificationBoxQuery(res.data.id, nickname, amount);

              CachedQuery.instance.invalidateCache(key: 'notification_box');

              // fetchNotificationBoxQuery(res.data.id, nickname, amount)
              // .invalidateQuery();
            },
            onError: (_, a, b) {
              myLog(
                '[Add Note Mutation onError] Error: $a',
                LogLevel.error,
              );
            },
            refetchQueries: [
              diaryListKey(date.year, date.month),
              'number_of_notes',
              hasEntryForEntryDateKey(DateFormat('yyyy-MM-dd').format(date)),
              diaryWriteButtonModelKey(date),
              'entry_ids',
              fetchDiaryRewardKey(),
              // 'notification_box',
            ]);

// Query key functions
String diaryListKey(int year, int month) => 'note-list$year$month';
String getCurrentEntryDateKey() => 'current_entry_date${DateTime.now().hour}';
String hasEntryForEntryDateKey(String date) => 'has_entry_for_entry_date_$date';
String diaryWriteButtonModelKey(DateTime selectedDate) =>
    'note_write_button_model_${getCurrentEntryDateKey()}_${DateFormat('yyyy-MM-dd').format(selectedDate)}';
String fetchDiaryRewardKey() => 'diary_reward';

// Queries
final Query<String> Function(String userId) getCurrentEntryDateQuery =
    (String userId) => Query(
          queryFn: () async {
            return await DiaryService().getCurrentEntryDate(userId);
          },
          key: getCurrentEntryDateKey(),
          onError: (e) {
            myLog(
              '[Get Current Entry Date Query onError] e: $e',
              LogLevel.error,
            );
          },
          onSuccess: (res) {
            myLog(
              '[Get Current Entry Date Query onSuccess] res: $res',
            );
          },
        );

final Query<bool> Function(String entryDate) hasEntryForEntryDateQuery =
    (String entryDate) => Query(
          queryFn: () async {
            return await DiaryService().hasEntryForEntryDate(entryDate);
          },
          key: hasEntryForEntryDateKey(entryDate),
          onError: (e) {
            myLog(
              '[Has Entry for Entry Date Query onError] e: $e',
              LogLevel.error,
            );
          },
          onSuccess: (res) {
            myLog(
              '[Has Entry for Entry Date Query onSuccess] res: $res',
            );
          },
        );

final Query<NoteWriteButtonModel> Function(DateTime selectedDate)
    diaryWriteButtonModelQuery = (DateTime selectedDate) => Query(
          key: diaryWriteButtonModelKey(selectedDate),
          queryFn: () async {
            final userId = currentUserId();

            if (userId == null) {
              return NoteWriteButtonModel(
                currentEntryDate: '',
                // hasWrittenNoteToday: false,
                selectedDate: selectedDate,
                subscriptionStatus: SubscriptionStatus.none,
              );
            }

            final userInfoQuery = fetchUserInfoQuery();
            final userInfoResult = await userInfoQuery.result;
            final userInfo = userInfoResult.data;

            final currentEntryDateQ = getCurrentEntryDateQuery(userId);
            final currentEntryDateResult = await currentEntryDateQ.result;
            final currentEntryDate = currentEntryDateResult.data;

            if (currentEntryDate == null || currentEntryDate.isEmpty) {
              return NoteWriteButtonModel(
                currentEntryDate: '',
                // hasWrittenNoteToday: false,
                selectedDate: selectedDate,
                subscriptionStatus: userInfo?.status ?? SubscriptionStatus.none,
              );
            }

            // final hasWrittenNoteQ = hasEntryForEntryDateQuery(currentEntryDate);
            // final hasWrittenNote = await hasWrittenNoteQ.result;

            final noteWriteButtonModel = NoteWriteButtonModel(
              currentEntryDate: currentEntryDate,
              // hasWrittenNoteToday: hasWrittenNote.data ?? false,
              selectedDate: selectedDate,
              subscriptionStatus: userInfo?.status ?? SubscriptionStatus.none,
            );

            return noteWriteButtonModel;
          },
          onError: (e) {
            myLog(
              '[Diary Write Button Model Query onError] e: $e',
              LogLevel.error,
            );
          },
        );

final Query<NotificationBoxModel> Function(String, String, int?)
    fetchNotificationBoxQuery =
    (String entryId, String userNickname, int? gems) => Query(
          queryFn: () async {
            final userId = currentUserId();

            if (userId == null) {
              myLog('User is not logged in: from fetchNotificationBoxQuery');
            }

            final userInfoQuery = fetchUserInfoQuery();
            final userInfoResult = await userInfoQuery.result;
            final userInfo = userInfoResult.data;

            final diary = await DiaryService().fetchDiaryById(entryId);

            final numberOfNotesQuery = fetchNumberOfDiaryQuery(DateTime.now());
            final numberOfNotesResult = await numberOfNotesQuery.result;
            final numberOfNotes = numberOfNotesResult.data;

            final isFirstNote =
                numberOfNotes != null && numberOfNotes['total'] == 1;

            await Posthog().capture(eventName: 'diary created', properties: {
              'entry_id': entryId,
              'nickname': userNickname,
              'created_at': DateTime.now().toIso8601String(),
              'is_first_diary': isFirstNote.toString(),
              'total_diaries': numberOfNotes?['total'].toString() ?? '0',
            });

            return NotificationBoxModel(
              isFirstDiary: isFirstNote,
              nickname: userInfo?.nickname ?? 'Langda',
              gems: gems,
              note: diary,
            );
          },
          key: 'notification_box',
          onError: (e) {
            myLog(
              '[Fetch Notification Box Query onError] e: $e',
              LogLevel.error,
            );
          },
        );
