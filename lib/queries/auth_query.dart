import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:langda/backend/service/auth_service.dart';
import 'package:purchases_flutter/purchases_flutter.dart' as RevenueCat;
import 'package:supabase_flutter/supabase_flutter.dart';

import '../backend/model/user_model.dart';
import '../utils/my_logger.dart';

String userInfoKey() {
  final String userId = Supabase.instance.client.auth.currentUser?.id ?? '';
  return 'user_info$userId';
}

final Mutation<void, void> Function() deleteUserMutation = () => Mutation(
      queryFn: (args) async {
        await AuthService().deleteUser();
        await Supabase.instance.client.auth.signOut();
      },
      onError: (arg, error, fallback) => {
        myLog(
          '[Delete User Mutation onError] Error: $error',
          LogLevel.error,
        )
      },
    );

final Query<UserModelStructure?> Function() fetchUserInfoQuery =
    () => Query(
          queryFn: () async {
            UserModelStructure? userModel = null;
            userModel = await AuthService().getUser();
            if (userModel != null) {
              userModel.customerInfo =
                  await RevenueCat.Purchases.getCustomerInfo();
            }
            return userModel;
          },
          key: userInfoKey(),
          onError: (e) {
            myLog(
              '[Fetch User Info Query onError] e: $e',
              LogLevel.error,
            );
          },
        );
