import 'package:cached_query_flutter/cached_query_flutter.dart';
import 'package:langda/backend/model/card_model.dart';
import 'package:langda/backend/repository/clients/api/api_correction_repository.dart';
import 'package:langda/backend/service/card_service.dart';
import 'package:langda/backend/service/currency_service.dart';
import 'package:langda/presentation/pages/mobile/home_page/card_page/component/card_tag.dart';
import 'package:langda/queries/auth_query.dart';
import 'package:langda/utils/my_logger.dart';

import '../api/models/deep_explanation_response.dart';
import '../backend/langda_api_client.dart';
import '../models/generated_classes.dart';
import 'key_constants.dart';

void _updateCardInAllCardsQueries(
    String cardId, CardModel Function(CardModel) updateFn) {
  CachedQuery.instance.updateQuery(
    updateFn: (dynamic old) {
      myLog("Updating query for allCardsKeyPrefix");
      final List<CardModel>? oldList = old as List<CardModel>?;
      if (oldList == null) {
        myLog("[_updateCardInAllCardsQueries] oldList is null");
        return old;
      }

      return oldList.map((card) {
        if (card.correctionId == cardId) {
          return updateFn(card);
        }
        return card;
      }).toList();
    },
    filterFn: (unencodedKey, key) => key.startsWith(allCardsKeyPrefix),
  );
}

final Query<CardModel> Function(String) fetchCardQuery =
    (String correctionId) => Query(
          queryFn: () async {
            final userInfo = await fetchUserInfoQuery().result;
            if (userInfo.data == null) {
              // throw Exception('User explanation language preference is null');
            } else {
              myLog(
                '[fetchCardQuery] User explanation language preference: ${userInfo.data!.explanationLanguagePreference}',
                LogLevel.debug,
              );
            }

            final explanationLanguagePreference =
                userInfo.data!.explanationLanguagePreference;
            return await CardService()
                .getCard(correctionId, explanationLanguagePreference);
          },
          key: QueryKeys.cardKey(correctionId),
          onError: (e) {
            myLog(
              '[fetchCardQuery] Error: $e',
              LogLevel.error,
            );
          },
        );

final Mutation<DeepExplanationResponse, void> Function(
        String cardId, Function, Function) getDeepExplanationMutation =
    (String cardId, Function onFinished, Function onError) => Mutation(
          queryFn: (args) async {
            final result = await LangdaApiClient.client.diary
                .postCorrectionsIdDeepExplain(id: cardId);

            return result;
          },
          onSuccess: (res, _) {
            _updateCardInAllCardsQueries(
              cardId,
              (card) => card.copyWith(
                deepExplanation: res.data.explanationUserLanguage,
              ),
            );
            onFinished(res);

            CurrencyService.instance.updateCurrencyBalance(res);

            CachedQuery.instance.refetchQueries(keys: [userInfoKey()]);
          },
          onError: (arg, error, fallback) {
            myLog(
              '[getDeepExplanationMutation] Error: $error',
              LogLevel.error,
            );
            onError(error);
          },
          refetchQueries: [userInfoKey(), QueryKeys.cardKey(cardId)],
        );

final Query<List<CardModel>> Function({List<Tag>? tags, bool bookmarkedOnly})
    fetchAllCardsQuery =
    ({List<Tag>? tags, bool bookmarkedOnly = false}) => Query(
          queryFn: () async {
            final userInfo = await fetchUserInfoQuery().result;
            if (userInfo.data == null) {
              // throw Exception('User explanation language preference is null');
            } else {
              myLog(
                '[fetchAllCardsQuery] User explanation language preference: ${userInfo.data!.explanationLanguagePreference}',
                LogLevel.debug,
              );
            }
            final explanationLanguagePreference =
                userInfo.data!.explanationLanguagePreference;
            myLog(
                '[fetchAllCardsQuery]: Getting all corrections from Supabase, Key = ${allCardsKey(tags: tags, bookmarkedOnly: bookmarkedOnly)}');
            final allCorrections = await CardService().getAllCards(
                bookmarkedOnly: bookmarkedOnly,
                tags: tags,
                explanationLanguagePreference: explanationLanguagePreference);
            myLog(
                '[fetchAllCardsQuery]: Got ${allCorrections.length} corrections from Supabase');
            return allCorrections;
          },
          key: allCardsKey(
            tags: tags,
            bookmarkedOnly: bookmarkedOnly,
          ),
          onError: (e) {
            myLog(
              '[fetchAllCardsQuery] Error: $e',
              LogLevel.error,
            );
          },
        );

final Query<List<Corrections>> Function() fetchSavedCardsQuery = () => Query(
      queryFn: () async {
        myLog(
            '[fetchSavedCardsQuery]: Getting saved corrections from Supabase');
        final savedCorrections = await CardService().getSavedCorrections();
        myLog(
            '[fetchSavedCardsQuery]: Got ${savedCorrections.length} saved corrections from Supabase');
        return savedCorrections;
      },
      key: 'saved_cards',
      onError: (e) {
        myLog(
          '[fetchSavedCardsQuery] Error: $e',
          LogLevel.error,
        );
      },
    );
final Mutation<void, bool> Function(String cardId) cardBookmarkingMutation =
    (String cardId) => Mutation(
          queryFn: (bool newState) async {
            await CardService().updateCorrectionSavedness(cardId, newState);
          },
          onStartMutation: (bool newState) {
            _updateCardInAllCardsQueries(
              cardId,
              (card) => card.copyWith(isSaved: newState),
            );
          },
          refetchQueries: ['saved_cards', QueryKeys.cardKey(cardId)],
          onError: (arg, error, fallback) => {
            myLog(
              '[cardBookmarkingMutation] Error: $error',
              LogLevel.error,
            )
          },
        );

final Mutation<void, void> Function(String correctionId)
    upvoteCorrectionMutation = (String id) => Mutation(
          queryFn: (args) async {
            myLog(
                '[upvoteCorrectionMutation] Starting vote mutation for key: ${QueryKeys.cardKey(id)}');
            await CardService().voteCorrection(id, VoteType.upvote);
            myLog(
                '[upvoteCorrectionMutation] Vote complete, invalidating query for key: ${QueryKeys.cardKey(id)}');
          },
          onStartMutation: (_) {
            _updateCardInAllCardsQueries(
              id,
              (card) => card.copyWith(isVoted: true),
            );
          },
          refetchQueries: [QueryKeys.cardKey(id)],
          onError: (arg, error, fallback) => {
            myLog(
              '[upvoteCorrectionMutation] Error: $error',
              LogLevel.error,
            )
          },
        );

final Mutation<void, void> Function(String correctionId)
    downvoteCorrectionMutation = (String id) => Mutation(
          queryFn: (args) async {
            myLog(
                '[downvoteCorrectionMutation] Starting vote mutation for key: ${QueryKeys.cardKey(id)}');
            await CardService().voteCorrection(id, VoteType.downvote);
            myLog(
                '[downvoteCorrectionMutation] Vote complete, invalidating query for key: ${QueryKeys.cardKey(id)}');
          },
          onStartMutation: (_) {
            _updateCardInAllCardsQueries(
              id,
              (card) => card.copyWith(isVoted: true),
            );
          },
          refetchQueries: [QueryKeys.cardKey(id)],
          onError: (arg, error, fallback) => {
            myLog(
              '[downvoteCorrectionMutation] Error: $error',
              LogLevel.error,
            )
          },
        );

String allCardsKeyPrefix = 'all_cards-';
String allCardsKey({List<Tag>? tags, bool bookmarkedOnly = false}) {
  final sortedTags = tags?.map((e) => e.name).toList();
  sortedTags?.sort();
  final tagString = sortedTags?.join('');
  return '${allCardsKeyPrefix}${tagString ?? ''}-${bookmarkedOnly ? 'bookmarked' : ''}';
}
