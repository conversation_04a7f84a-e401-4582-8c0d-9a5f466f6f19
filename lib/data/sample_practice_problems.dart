import '../backend/model/practice_problem_model.dart';

class SamplePracticeProblems {
  static List<PracticeProblem> getSampleProblems() {
    return [
      PracticeProblem(
        id: '1',
        sentence: 'I am going to the store tomorrow.',
        blankPosition: 2,
        correctAnswer: 'going',
        options: ['go', 'going', 'went', 'gone'],
        explanation:
            'We use "going" with "am" to form the present continuous tense, which is used for future plans.',
        category: 'Tenses',
      ),
      PracticeProblem(
        id: '2',
        sentence: 'She has been working here for five years.',
        blankPosition: 1,
        correctAnswer: 'has',
        options: ['have', 'has', 'had', 'having'],
        explanation:
            '"Has" is used with third person singular subjects (she, he, it) in present perfect tense.',
        category: 'Subject-Verb Agreement',
      ),
      PracticeProblem(
        id: '3',
        sentence: 'The book is on the table.',
        blankPosition: 4,
        correctAnswer: 'on',
        options: ['in', 'on', 'at', 'under'],
        explanation:
            'We use "on" to indicate something is positioned on top of a surface.',
        category: 'Prepositions',
      ),
      PracticeProblem(
        id: '4',
        sentence: 'I would like a cup of coffee, please.',
        blankPosition: 4,
        correctAnswer: 'a',
        options: ['a', 'an', 'the', ''],
        explanation:
            'We use the indefinite article "a" before singular countable nouns that start with a consonant sound.',
        category: 'Articles',
      ),
      PracticeProblem(
        id: '5',
        sentence: 'They were playing soccer when it started to rain.',
        blankPosition: 1,
        correctAnswer: 'were',
        options: ['was', 'were', 'are', 'is'],
        explanation:
            '"Were" is used with plural subjects (they) in past continuous tense.',
        category: 'Tenses',
      ),
      PracticeProblem(
        id: '6',
        sentence: 'Could you please help me with this problem?',
        blankPosition: 0,
        correctAnswer: 'Could',
        options: ['Can', 'Could', 'May', 'Might'],
        explanation: '"Could" is more polite than "can" when making requests.',
        category: 'Politeness',
      ),
      PracticeProblem(
        id: '7',
        sentence: 'The children are playing in the park.',
        blankPosition: 1,
        correctAnswer: 'children',
        options: ['child', 'children', 'childs', 'childrens'],
        explanation: '"Children" is the correct plural form of "child".',
        category: 'Plural Forms',
      ),
      PracticeProblem(
        id: '8',
        sentence: 'I have never been to Japan before.',
        blankPosition: 2,
        correctAnswer: 'never',
        options: ['ever', 'never', 'always', 'sometimes'],
        explanation:
            '"Never" is used to indicate that something has not happened at any time in the past.',
        category: 'Adverbs',
      ),
      PracticeProblem(
        id: '9',
        sentence: 'She speaks English very fluently.',
        blankPosition: 4,
        correctAnswer: 'very',
        options: ['very', 'much', 'too', 'so'],
        explanation: '"Very" is used to intensify adverbs like "fluently".',
        category: 'Adverbs',
      ),
      PracticeProblem(
        id: '10',
        sentence: 'If I had more time, I would travel around the world.',
        blankPosition: 2,
        correctAnswer: 'had',
        options: ['have', 'had', 'has', 'having'],
        explanation:
            'In second conditional sentences, we use "had" in the if-clause to express hypothetical situations.',
        category: 'Conditionals',
      ),
      PracticeProblem(
        id: '11',
        sentence: 'The movie was so boring that I fell asleep.',
        blankPosition: 3,
        correctAnswer: 'so',
        options: ['very', 'so', 'too', 'quite'],
        explanation:
            '"So...that" is used to show cause and effect relationships.',
        category: 'Conjunctions',
      ),
      PracticeProblem(
        id: '12',
        sentence: 'My sister is taller than me.',
        blankPosition: 3,
        correctAnswer: 'taller',
        options: ['tall', 'taller', 'tallest', 'more tall'],
        explanation:
            'We use the comparative form "taller" when comparing two people or things.',
        category: 'Comparatives',
      ),
      PracticeProblem(
        id: '13',
        sentence: 'I am looking forward to seeing you again.',
        blankPosition: 5,
        correctAnswer: 'to',
        options: ['to', 'for', 'at', 'on'],
        explanation:
            'The phrasal verb "look forward to" is always followed by "to".',
        category: 'Phrasal Verbs',
      ),
      PracticeProblem(
        id: '14',
        sentence: 'There are many people waiting in line.',
        blankPosition: 0,
        correctAnswer: 'There',
        options: ['There', 'Their', 'They\'re', 'Where'],
        explanation:
            '"There are" is used to indicate the existence of something.',
        category: 'Common Mistakes',
      ),
      PracticeProblem(
        id: '15',
        sentence: 'I wish I could speak French fluently.',
        blankPosition: 3,
        correctAnswer: 'could',
        options: ['can', 'could', 'will', 'would'],
        explanation:
            'After "wish", we use "could" to express ability we don\'t currently have.',
        category: 'Wish Sentences',
      ),
      PracticeProblem(
        id: '16',
        sentence: 'The weather is getting colder every day.',
        blankPosition: 3,
        correctAnswer: 'getting',
        options: ['get', 'getting', 'got', 'gets'],
        explanation: '"Is getting" shows a gradual change happening over time.',
        category: 'Progressive Tenses',
      ),
      PracticeProblem(
        id: '17',
        sentence: 'Neither John nor Mary was at the party.',
        blankPosition: 5,
        correctAnswer: 'was',
        options: ['was', 'were', 'are', 'is'],
        explanation:
            'With "neither...nor", the verb agrees with the subject closest to it (Mary - singular).',
        category: 'Subject-Verb Agreement',
      ),
      PracticeProblem(
        id: '18',
        sentence: 'I have been studying English for three years.',
        blankPosition: 3,
        correctAnswer: 'studying',
        options: ['study', 'studied', 'studying', 'studies'],
        explanation:
            'Present perfect continuous uses "have been + -ing form" to show ongoing action.',
        category: 'Perfect Tenses',
      ),
      PracticeProblem(
        id: '19',
        sentence: 'The house which we bought last year is very beautiful.',
        blankPosition: 2,
        correctAnswer: 'which',
        options: ['which', 'that', 'who', 'where'],
        explanation: '"Which" is used to refer to things in relative clauses.',
        category: 'Relative Pronouns',
      ),
      PracticeProblem(
        id: '20',
        sentence: 'By the time you arrive, I will have finished cooking.',
        blankPosition: 8,
        correctAnswer: 'have',
        options: ['have', 'has', 'had', 'having'],
        explanation:
            'Future perfect tense uses "will have + past participle" to show completion before a future time.',
        category: 'Future Perfect',
      ),
    ];
  }

  /// Get problems by category
  static List<PracticeProblem> getProblemsByCategory(String category) {
    return getSampleProblems()
        .where((problem) => problem.category == category)
        .toList();
  }

  /// Get all available categories
  static List<String> getCategories() {
    return getSampleProblems()
        .map((problem) => problem.category ?? 'General')
        .toSet()
        .toList()
      ..sort();
  }

  /// Get a random subset of problems
  static List<PracticeProblem> getRandomProblems(int count) {
    final allProblems = getSampleProblems();
    final List<PracticeProblem> shuffledProblems = List.from(allProblems);
    shuffledProblems.shuffle();
    return shuffledProblems.take(count).toList();
  }
}
