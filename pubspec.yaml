name: langda
description: "AI-Corrected English Diary App"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.2.9+86

environment:
  sdk: ">=3.0.0 <4.0.0"

dependency_overrides:
  intl: any

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  mocktail: ^1.0.4
  cupertino_icons: ^1.0.6
  provider: ^6.1.2
  go_router: ^14.6.2
  moon_design: ^0.59.2
  table_calendar: ^3.1.2
  shared_preferences: ^2.2.3
  animation_wrappers: ^3.0.0
  animations: ^2.0.11
  intl: ^0.19.0
  responsive_sizer: ^3.3.1
  flutter_i18n: ^0.36.0
  font_awesome_flutter: ^10.7.0
  flutter_form_builder: ^9.2.1
  flutter_spinkit: ^5.2.1
  supabase_flutter: ^2.5.8
  http: ^1.2.2
  cloud_firestore: ^5.6.3
  page_transition: ^2.1.0
  flutter_launcher_icons: ^0.13.1
  flutter_langdetect: ^0.0.1
  cached_query_flutter: ^2.2.0
  json_annotation: ^4.9.0
  in_app_purchase: ^3.2.0
  supabase_auth_ui: ^0.5.1
  purchases_flutter: ^8.1.2
  kakao_flutter_sdk: ^1.9.5 # 전체 추가
  kakao_flutter_sdk_user: ^1.9.5 # 카카오 로그인 API 패키지
  kakao_flutter_sdk_share: ^1.9.5 # 카카오톡 공유 API 패키지
  kakao_flutter_sdk_talk: ^1.9.5 # 카카오톡 채널, 카카오톡 소셜, 카카오톡 메시지 API 패키지
  kakao_flutter_sdk_friend: ^1.9.5 # 피커 API 패키지
  kakao_flutter_sdk_navi: ^1.9.5 # 카카오내비 API 패키지
  google_sign_in: ^6.2.1 # 구글 로그인 API 패키지
  sign_in_with_apple: ^6.1.0 # 애플 로그인 API 패키지
  crypto: ^3.0.1 # 암호화 패키지
  skeletonizer: ^1.4.2
  wave: ^0.2.2
  flutter_slidable: ^3.1.2
  firebase_messaging: ^15.2.2
  flutter_local_notifications: ^17.2.3
  package_info_plus: ^8.1.1
  firebase_core: ^3.11.0
  expandable: ^5.0.1
  webview_flutter: ^4.0.0
  webview_flutter_android: ^3.6.1
  webview_flutter_wkwebview: ^3.16.0
  auto_size_text: ^3.0.0
  device_info_plus: ^11.1.1
  flutter_emoji: ^2.5.1
  diff_match_patch: ^0.4.1
  flutter_animate: ^4.5.0
  multi_dropdown: ^3.0.1
  scroll_snap_list: ^0.9.1
  wheel_slider: ^1.2.1
  animated_custom_dropdown: ^3.1.1
  animated_text_kit: ^4.2.2
  like_button: ^2.0.5
  flutter_gradient_animation_text: ^1.0.2
  flutter_glow: ^0.3.2
  animated_toggle_switch: ^0.8.3
  lordicon: ^1.0.3
  confetti: ^0.8.0
  glowy_borders: ^1.0.2
  appinio_swiper: ^2.1.1
  flip_card: ^0.7.0
  star_menu: ^4.0.1
  flutter_animated_icon_button: ^1.1.3
  mockito: ^5.4.5
  lottie: ^2.7.0
  overlay_tooltip: ^0.2.3
  carousel_slider: ^5.0.0
  sentry_flutter: ^8.12.0
  uuid: ^4.5.1
  random_nickname: ^0.0.2
  async_button_builder: ^3.0.0+1
  dio: ^5.8.0+1
  retrofit: ^4.4.2
  freezed_annotation: ^2.4.4 # for freezed
  flutter_markdown: ^0.7.6+2
  just_the_tooltip: ^0.0.12
  modal_bottom_sheet: ^3.0.0
  share_plus: ^10.1.4
  flutter_stepindicator: ^0.1.7
  clipboard: ^0.1.3
  locale_names: ^1.1.1
  in_app_review: ^2.0.10
  flutter_chat_ui: ^1.6.15
  bubble: ^1.2.1
  loading_animation_widget: ^1.3.0
  posthog_flutter: ^4.0.0
  fl_chart: ^0.69.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  json_serializable: ^6.9.0
  retrofit_generator: ^9.1.7
  swagger_parser: ^1.21.4
  build_runner: ^2.4.12
  freezed: ^2.5.7 # for freezed

flutter_launcher_icons:
  android: "launcher_icon"
  ios: "AppIcon"
  image_path: "assets/images/icons/langda.png"
  adaptive_icon: true
  remove_alpha_ios: true

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/icons/
    - assets/images/icons/weather/
    - assets/images/icons/purposes/
    - assets/images/logos/
    - assets/images/splashImage.png
    - assets/lottie/
    - assets/i18n/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Pretendard
      fonts:
        - asset: assets/fonts/Pretendard-Bold.ttf
          weight: 700
        - asset: assets/fonts/Pretendard-Medium.ttf
          weight: 500
        - asset: assets/fonts/Pretendard-Regular.ttf
          weight: 400
        - asset: assets/fonts/Pretendard-SemiBold.ttf
          weight: 600
    - family: Inconsolata
      fonts:
        - asset: assets/fonts/Inconsolata-VariableFont_wdth,wght.ttf

  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
