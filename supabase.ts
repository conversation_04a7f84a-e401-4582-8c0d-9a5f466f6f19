export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      correction_categories: {
        Row: {
          id: string
          name_en: string | null
          name_ko: string | null
          names: <PERSON><PERSON>
        }
        Insert: {
          id?: string
          name_en?: string | null
          name_ko?: string | null
          names: <PERSON><PERSON>
        }
        Update: {
          id?: string
          name_en?: string | null
          name_ko?: string | null
          names?: Json
        }
        Relationships: []
      }
      corrections: {
        Row: {
          corrected_phrase_id: string | null
          correction_tags: number[] | null
          created_at: string
          deep_explanation_diary_language: string | null
          deep_explanation_user_language: string | null
          explanation_diary_language: string
          explanation_user_language: string
          id: string
          is_saved: boolean
          likes: number
          original_phrase_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          corrected_phrase_id?: string | null
          correction_tags?: number[] | null
          created_at?: string
          deep_explanation_diary_language?: string | null
          deep_explanation_user_language?: string | null
          explanation_diary_language: string
          explanation_user_language: string
          id?: string
          is_saved?: boolean
          likes?: number
          original_phrase_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          corrected_phrase_id?: string | null
          correction_tags?: number[] | null
          created_at?: string
          deep_explanation_diary_language?: string | null
          deep_explanation_user_language?: string | null
          explanation_diary_language?: string
          explanation_user_language?: string
          id?: string
          is_saved?: boolean
          likes?: number
          original_phrase_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "corrections_corrected_phrase_id_fkey"
            columns: ["corrected_phrase_id"]
            isOneToOne: false
            referencedRelation: "phrases"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "corrections_original_phrase_id_fkey"
            columns: ["original_phrase_id"]
            isOneToOne: false
            referencedRelation: "phrases"
            referencedColumns: ["id"]
          },
        ]
      }
      corrections_correction_categories: {
        Row: {
          category_id: string
          correction_id: string
          created_at: string
        }
        Insert: {
          category_id: string
          correction_id: string
          created_at?: string
        }
        Update: {
          category_id?: string
          correction_id?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "corrections_correction_categories_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "correction_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "corrections_correction_categories_correction_id_fkey"
            columns: ["correction_id"]
            isOneToOne: false
            referencedRelation: "corrections"
            referencedColumns: ["id"]
          },
        ]
      }
      currency_transactions: {
        Row: {
          amount: number
          created_at: string
          id: string
          metadata: Json | null
          transaction_type: Database["public"]["Enums"]["currency_transaction_type"]
          user_id: string
        }
        Insert: {
          amount: number
          created_at?: string
          id?: string
          metadata?: Json | null
          transaction_type: Database["public"]["Enums"]["currency_transaction_type"]
          user_id: string
        }
        Update: {
          amount?: number
          created_at?: string
          id?: string
          metadata?: Json | null
          transaction_type?: Database["public"]["Enums"]["currency_transaction_type"]
          user_id?: string
        }
        Relationships: []
      }
      diary_entries: {
        Row: {
          created_at: string
          entry_date: string
          id: string
          subject_id: string | null
          updated_at: string
          user_id: string
          weather: string
        }
        Insert: {
          created_at?: string
          entry_date: string
          id?: string
          subject_id?: string | null
          updated_at?: string
          user_id: string
          weather?: string
        }
        Update: {
          created_at?: string
          entry_date?: string
          id?: string
          subject_id?: string | null
          updated_at?: string
          user_id?: string
          weather?: string
        }
        Relationships: []
      }
      diary_version_chunks: {
        Row: {
          created_at: string
          diary_version_id: string
          end_index: number | null
          id: string
          sentences: string[] | null
          sequence_number: number | null
          start_index: number | null
        }
        Insert: {
          created_at?: string
          diary_version_id: string
          end_index?: number | null
          id?: string
          sentences?: string[] | null
          sequence_number?: number | null
          start_index?: number | null
        }
        Update: {
          created_at?: string
          diary_version_id?: string
          end_index?: number | null
          id?: string
          sentences?: string[] | null
          sequence_number?: number | null
          start_index?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "diary_version_chunks_diary_version_id_fkey"
            columns: ["diary_version_id"]
            isOneToOne: false
            referencedRelation: "diary_versions"
            referencedColumns: ["id"]
          },
        ]
      }
      diary_versions: {
        Row: {
          content: string
          created_at: string
          diary_entry_id: string
          id: string
          updated_at: string
          version_type: Database["public"]["Enums"]["diary_version_type"]
        }
        Insert: {
          content: string
          created_at?: string
          diary_entry_id: string
          id?: string
          updated_at?: string
          version_type: Database["public"]["Enums"]["diary_version_type"]
        }
        Update: {
          content?: string
          created_at?: string
          diary_entry_id?: string
          id?: string
          updated_at?: string
          version_type?: Database["public"]["Enums"]["diary_version_type"]
        }
        Relationships: [
          {
            foreignKeyName: "diary_versions_diary_entry_id_fkey"
            columns: ["diary_entry_id"]
            isOneToOne: false
            referencedRelation: "diary_entries"
            referencedColumns: ["id"]
          },
        ]
      }
      generation_tasks: {
        Row: {
          created_at: string
          generation_task_id: string
          request_data: Json
          result_data: Json | null
          status: Database["public"]["Enums"]["generation_task_status"]
          task_type: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          generation_task_id?: string
          request_data: Json
          result_data?: Json | null
          status: Database["public"]["Enums"]["generation_task_status"]
          task_type: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          generation_task_id?: string
          request_data?: Json
          result_data?: Json | null
          status?: Database["public"]["Enums"]["generation_task_status"]
          task_type?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      kysely_migration: {
        Row: {
          name: string
          timestamp: string
        }
        Insert: {
          name: string
          timestamp: string
        }
        Update: {
          name?: string
          timestamp?: string
        }
        Relationships: []
      }
      kysely_migration_lock: {
        Row: {
          id: string
          is_locked: number
        }
        Insert: {
          id: string
          is_locked?: number
        }
        Update: {
          id?: string
          is_locked?: number
        }
        Relationships: []
      }
      notifications: {
        Row: {
          body: string
          created_at: string
          id: string
          user_id: string
        }
        Insert: {
          body: string
          created_at?: string
          id?: string
          user_id: string
        }
        Update: {
          body?: string
          created_at?: string
          id?: string
          user_id?: string
        }
        Relationships: []
      }
      phrases: {
        Row: {
          content: string
          created_at: string
          diary_version_id: string
          end_index: number
          id: string
          start_index: number
          updated_at: string
        }
        Insert: {
          content: string
          created_at?: string
          diary_version_id: string
          end_index: number
          id?: string
          start_index: number
          updated_at?: string
        }
        Update: {
          content?: string
          created_at?: string
          diary_version_id?: string
          end_index?: number
          id?: string
          start_index?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "phrases_diary_version_id_fkey"
            columns: ["diary_version_id"]
            isOneToOne: false
            referencedRelation: "diary_versions"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          created_at: string
          currency_balance: number
          current_streak: number
          explanation_language: Database["public"]["Enums"]["explanation_language_preference"]
          fcm_token: string | null
          learning_motivation: Database["public"]["Enums"]["learning_motivation"]
          native_language: string | null
          nickname: string
          referral_code: string | null
          trial_ends_at: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          currency_balance?: number
          current_streak?: number
          explanation_language?: Database["public"]["Enums"]["explanation_language_preference"]
          fcm_token?: string | null
          learning_motivation: Database["public"]["Enums"]["learning_motivation"]
          native_language?: string | null
          nickname: string
          referral_code?: string | null
          trial_ends_at?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          currency_balance?: number
          current_streak?: number
          explanation_language?: Database["public"]["Enums"]["explanation_language_preference"]
          fcm_token?: string | null
          learning_motivation?: Database["public"]["Enums"]["learning_motivation"]
          native_language?: string | null
          nickname?: string
          referral_code?: string | null
          trial_ends_at?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      referrals: {
        Row: {
          created_at: string
          id: string
          referred_id: string
          referrer_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          referred_id: string
          referrer_id: string
        }
        Update: {
          created_at?: string
          id?: string
          referred_id?: string
          referrer_id?: string
        }
        Relationships: []
      }
      revenuecat_processed_events: {
        Row: {
          event_id: string
          event_type: string
          processed_at: string
          raw_payload: Json
          user_id: string
        }
        Insert: {
          event_id: string
          event_type: string
          processed_at?: string
          raw_payload: Json
          user_id: string
        }
        Update: {
          event_id?: string
          event_type?: string
          processed_at?: string
          raw_payload?: Json
          user_id?: string
        }
        Relationships: []
      }
      subject_today: {
        Row: {
          date: string
          emoji: string
          id: string
          subject: string
        }
        Insert: {
          date: string
          emoji: string
          id?: string
          subject: string
        }
        Update: {
          date?: string
          emoji?: string
          id?: string
          subject?: string
        }
        Relationships: []
      }
      user_questions: {
        Row: {
          body: string
          created_at: string
          device_info: string
          id: string
          status: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          body: string
          created_at?: string
          device_info: string
          id?: string
          status?: string
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          body?: string
          created_at?: string
          device_info?: string
          id?: string
          status?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      feature_usage_transactions: {
        Row: {
          amount: number | null
          created_at: string | null
          feature_id: string | null
          id: string | null
          metadata: Json | null
          user_id: string | null
        }
        Insert: {
          amount?: number | null
          created_at?: string | null
          feature_id?: never
          id?: string | null
          metadata?: Json | null
          user_id?: string | null
        }
        Update: {
          amount?: number | null
          created_at?: string | null
          feature_id?: never
          id?: string | null
          metadata?: Json | null
          user_id?: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      currency_transaction_type:
        | "streak_milestone"
        | "achievement"
        | "trial_bonus"
        | "periodic_reward"
        | "feature_usage"
        | "feature_usage_refund"
        | "referral_reward"
      diary_version_type: "original" | "corrected"
      explanation_language_preference: "native_language" | "diary_language"
      generation_task_status: "processing" | "completed" | "failed"
      learning_motivation:
        | "social"
        | "study_abroad"
        | "emigration"
        | "career"
        | "fun"
        | "other"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      currency_transaction_type: [
        "streak_milestone",
        "achievement",
        "trial_bonus",
        "periodic_reward",
        "feature_usage",
        "feature_usage_refund",
        "referral_reward",
      ],
      diary_version_type: ["original", "corrected"],
      explanation_language_preference: ["native_language", "diary_language"],
      generation_task_status: ["processing", "completed", "failed"],
      learning_motivation: [
        "social",
        "study_abroad",
        "emigration",
        "career",
        "fun",
        "other",
      ],
    },
  },
} as const
