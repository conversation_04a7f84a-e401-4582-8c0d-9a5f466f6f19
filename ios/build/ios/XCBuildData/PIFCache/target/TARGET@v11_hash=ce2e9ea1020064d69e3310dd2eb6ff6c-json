{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98938775da442a738b0c421fd2b76f8949", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98194b751f5abfc138decb3fe24264cc8b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98346c11e8478413283d75fcb319a5f7af", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9835022e4a239124c436cbf904598bf186", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98346c11e8478413283d75fcb319a5f7af", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98118973e06804e171b5f1eb1db3ee3606", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986d5cb431970e70ea9af86636ffa53c85", "guid": "bfdfe7dc352907fc980b868725387e98d384eaaabdbaf4b1e061d09ccbeba17a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98010707625f3e3278ff603f4b87a6de7c", "guid": "bfdfe7dc352907fc980b868725387e98cd7932b32c9b493e9b6f9206b21fbf67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f25d663a7e50b4242c6b1808d905f946", "guid": "bfdfe7dc352907fc980b868725387e980b1b0cd39c32498e5f4d1cc9b6b40afa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cb7173515939ce0970ab91b4002ed7d", "guid": "bfdfe7dc352907fc980b868725387e980fb692c157730a600ddf950118abf609"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f34fae07bac4fa5c4aff22038001ea4f", "guid": "bfdfe7dc352907fc980b868725387e98da6fec28eb8b3e3f9bf3809a7f450b72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d406b0c1fd8156f81e04547dac31ba5", "guid": "bfdfe7dc352907fc980b868725387e9892ee26d4fd212f3c09b70e8b5ff90a77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ef769a4db4b5b63de8c81abf58671c5", "guid": "bfdfe7dc352907fc980b868725387e9880a21aff2c589d67a2d9256ac5be06bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7981599afef6bb05d1fe215af98b996", "guid": "bfdfe7dc352907fc980b868725387e98585257934ba0da7db5e1846113bba831"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c51481685f78ed6cfc058d57a51f1a32", "guid": "bfdfe7dc352907fc980b868725387e98c6116365e00f346682397e91ac3074d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d36be1583a308cbfd12ff0c6cb3fb2a5", "guid": "bfdfe7dc352907fc980b868725387e98b75c44e2b2c3f18f4306e2ad1ef0064e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851e9a932a5d74ea16d6a0102f252bf88", "guid": "bfdfe7dc352907fc980b868725387e9806b9870b31d0e9f8528da1001bf62046"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6d888dffc71dfaa661319f8aea06999", "guid": "bfdfe7dc352907fc980b868725387e98c40b236f89d9d92d8cc151e2d2e57e60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a742a495011e9a4df8f6883ba9d96e92", "guid": "bfdfe7dc352907fc980b868725387e98af4645afcb1ddfbeeec2f8d5bd5ab446", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9286bf262eb5c254189e67c5849546c", "guid": "bfdfe7dc352907fc980b868725387e98a2fbebce0b0c8101782aa2c9c90d13a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887e491defd6f1353eb91849fe65de0b6", "guid": "bfdfe7dc352907fc980b868725387e987423a40d851db2e093f3838df6bbeebd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce85539e3e60a626efd2630d23f4025e", "guid": "bfdfe7dc352907fc980b868725387e9865a1e4d092f9a33bbe8b0606794fd846"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983292170d7567954d7e658106d9707995", "guid": "bfdfe7dc352907fc980b868725387e9813f7d1a46e386b915f4d681181ce47e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981189ef266082e40580c65f14d617bd08", "guid": "bfdfe7dc352907fc980b868725387e982c34e5566adb538320b6ee1ed13554b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98746a08e6fdb49a548139dd38a3153122", "guid": "bfdfe7dc352907fc980b868725387e988eadd3fffd1258ce9cfd8e9891672ef8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbfbb3152972e75814324eebb0b6aac5", "guid": "bfdfe7dc352907fc980b868725387e9828c2e406abe0af5753dd110aebbc4bde", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6f52c1d090763574b698bd356e4b7c8", "guid": "bfdfe7dc352907fc980b868725387e98fa9207f97db6cb47c828152a7a828351"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b34a827392490dc0280c19e4134556e3", "guid": "bfdfe7dc352907fc980b868725387e98f754838f348ba6dcd8e665400cd9ae7a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9875d4beb2b0a7a55adea1625a66d92123", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cc1f8a64d43d675eb049797906fc4dab", "guid": "bfdfe7dc352907fc980b868725387e98f46661f9e0c8931469789903cc3d5e53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cdd073451f019dd0f4405f398877f2c", "guid": "bfdfe7dc352907fc980b868725387e987df50dd3dc1bff1c3795a7df7559d74e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b955f0eba5489ab7971c6bccec546c6b", "guid": "bfdfe7dc352907fc980b868725387e98dd70fdc998f20d43f77ca83c6e8a2b31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a77303044f25009a2d5028339f7e34ad", "guid": "bfdfe7dc352907fc980b868725387e98f6eb6099d88743422b7f1597e980e58c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844c891b9ae6de6589e6046c91b9508cd", "guid": "bfdfe7dc352907fc980b868725387e984f85892269686dd308ac624e3e0f9541"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9c82780fcb3190c1a290e708cf8eee9", "guid": "bfdfe7dc352907fc980b868725387e98b17152766c5ed89498ae5c75e857a04e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867c0d8ec9b0cba96e41c7fd38bf45426", "guid": "bfdfe7dc352907fc980b868725387e98682e6a21106bf50e6df62324053ef1a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848b3ea082c20c14436a983692862d3d3", "guid": "bfdfe7dc352907fc980b868725387e98db71e9d6d366cdc056f509d6428994c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853197bcf169047e4c4e7509f291b5763", "guid": "bfdfe7dc352907fc980b868725387e98937f40e4f88d007bfe346f745cfa0ee5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5a5f59cb8ddd34c0f564e0b52acddcd", "guid": "bfdfe7dc352907fc980b868725387e989870b1697ef7e88bbe1c592344530843"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815329c73f07d6606885036a79a70e41b", "guid": "bfdfe7dc352907fc980b868725387e9843373458c3001053ec5d25d36208ef7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5c0b43bb8239acf2da0f3f814a02ff4", "guid": "bfdfe7dc352907fc980b868725387e98758cfd58129f9dbaaa804a947ff16b32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810c03e03af90b09ff6107a97299e5222", "guid": "bfdfe7dc352907fc980b868725387e9807b1359871102e6c4b4f8d87cef99d21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a66c41d3a0f1ac890d254755486b48ec", "guid": "bfdfe7dc352907fc980b868725387e9821bb11dff1ab00b4de43665f7057355a"}], "guid": "bfdfe7dc352907fc980b868725387e986a984cd377c335a3333c37062db11470", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1b96f727184f819fafd78f8ff8588be", "guid": "bfdfe7dc352907fc980b868725387e98f9ee339a3b2198d855ad6ee07d471ef6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f437ad05bf957ed1d437c3c5ca5ba035", "guid": "bfdfe7dc352907fc980b868725387e98273b65771cbeb23297ddbc0fcc8db1bd"}], "guid": "bfdfe7dc352907fc980b868725387e9853455e397c8d35097944748cdda6bfa2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e989570262a3bae148b655e5a366cd84cdb", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e983e28802f33daacbb5dd0fabb86307719", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}