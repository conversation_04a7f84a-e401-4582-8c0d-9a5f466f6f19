{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98de063bdd870f9582a92bfa3b7685f8fe", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98257c964a7023180072e29780d840472d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b3df12f9ee8f4d35584b117bd143749e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bc7ffde142e738efff38b4831b55bf45", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b3df12f9ee8f4d35584b117bd143749e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cafcc84dfc22a248c5d63c61d273081d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ea08a37e0ca2b584d58993d2f4d0a942", "guid": "bfdfe7dc352907fc980b868725387e98f0ab6adc0c048bc22737d05663a04dcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5bb8adc86755518a9e496bdc0a57c5f", "guid": "bfdfe7dc352907fc980b868725387e98cb32a39d608020ac5f1b64941b27c9c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989631ef67d3c78c9ba5e9143ce3848e31", "guid": "bfdfe7dc352907fc980b868725387e984471f5df5e15895df267e7f782651e8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a46fa762715688206d055c82627c93a", "guid": "bfdfe7dc352907fc980b868725387e98fb4dbcb262c6197b79e8a743f08c21b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bde24e36d363c8fc9f1fd1638048e6d0", "guid": "bfdfe7dc352907fc980b868725387e989b113725191e912c667bd0a0027a291c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e1109388ce9c7a0a5d87897ffed4f5d", "guid": "bfdfe7dc352907fc980b868725387e98bc00be8a391e3b012b94959caff7b4ed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4c91bc1496e3fc7d8a30d127c28bac4", "guid": "bfdfe7dc352907fc980b868725387e98311a2efca91c6b0502a45c26d9f52c9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859267c547e8db6b670e149945ec6c3b0", "guid": "bfdfe7dc352907fc980b868725387e98b93602fb8fcecbf0a792304220df190a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859a3eae3f91723459ed8438fc7028992", "guid": "bfdfe7dc352907fc980b868725387e9875487283ed0528e7435027f238549877", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee538d80d4c589e67d753d662c6b7a4", "guid": "bfdfe7dc352907fc980b868725387e98c009d92cf6accd02ca49980d13e0dd7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e328d1526e67fa84ba6a83617bb56bfa", "guid": "bfdfe7dc352907fc980b868725387e987553d778ee6a7356c8aa97e8ace1a300", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817d813e3e37fee2f607c33ef9912fcc0", "guid": "bfdfe7dc352907fc980b868725387e98545bf161b0d8a814fe1b0c81816aa959"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875d60da280a899d00ab9d46fc267ba84", "guid": "bfdfe7dc352907fc980b868725387e9867bef1f8e7a9a3a8f5dd94dc89dbc758", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857335834b2d0a06527b269790b9281df", "guid": "bfdfe7dc352907fc980b868725387e989b78dadee69c0c0233e39c45f4b26bb8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcb86d9d8df3e1233b2ef7ed9b1fc5f3", "guid": "bfdfe7dc352907fc980b868725387e98c1c7c618880e130fef747b6bf9455f9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852dc8c168ed6cfbe23e405131dd35783", "guid": "bfdfe7dc352907fc980b868725387e9891afc37b0e3ec3f757d16f98d5355d1d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e8f838fd05a804d8e5ca4190b2653ab", "guid": "bfdfe7dc352907fc980b868725387e987fe39eb582e6d078e7106ad0f452509f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f85ebebf62b9e9069e1d0a0e7eaa23a", "guid": "bfdfe7dc352907fc980b868725387e98229eb611f39f4e7fa0863dbc0cdb589d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc4766aec76970a0e505c5c7cf04150d", "guid": "bfdfe7dc352907fc980b868725387e98b47b5c2050a36dadec9d01bf6424171b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f970bc1535b486d21ad1e2ecfc868b8", "guid": "bfdfe7dc352907fc980b868725387e980440710f024fa082dcb5de7643d57b95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829b9f42722d61d20ebbd9258381e59ae", "guid": "bfdfe7dc352907fc980b868725387e985c2727bf3309cbb3629a58773db26bb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98067cbc02ca15bdf3e2081f9446e3b311", "guid": "bfdfe7dc352907fc980b868725387e988bfb0846450ae1dbb6aa5a88d8b4ac7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984878d480554389aa3b5af33a13c055ee", "guid": "bfdfe7dc352907fc980b868725387e98cc181686777363434156f67350373035", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844081e6f4f7871c0b17b920717637be2", "guid": "bfdfe7dc352907fc980b868725387e98aabd9fcb99630053fac165cdd7ada025"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989419df97263038fb29f59916a8e4cc74", "guid": "bfdfe7dc352907fc980b868725387e98fd2a63b3701342d36b73269569daadfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8c9270f8b93552ff8089fdc716a9f7a", "guid": "bfdfe7dc352907fc980b868725387e984ebc1135a6742648cc08336bd140e348", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980884200ea77523149a545d10bf269a68", "guid": "bfdfe7dc352907fc980b868725387e985a951fe0689a658a057202d765e70b0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98657ed3eae4979512b18102f6a6aeb246", "guid": "bfdfe7dc352907fc980b868725387e98cc5edc62f2a2115329b6d73ad981fa74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f909fe0629e6037000995b36b6c3d0e", "guid": "bfdfe7dc352907fc980b868725387e98bbf05ba7cbf92e97e22b5cd526100eb0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1155d7eb50222f6c49ea0bc79fd663f", "guid": "bfdfe7dc352907fc980b868725387e98d9aabd22f20118977724d1684a710784", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897bb7e0b3fe6ca56536d9bc85eefabe8", "guid": "bfdfe7dc352907fc980b868725387e98b83a5469dea13bf0c4d44cd4da18cfca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de46ff114d7add7b45f57e910f2fd3a4", "guid": "bfdfe7dc352907fc980b868725387e989a6a1980a15242269c218e92de0d5ae3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988d50a034d47046148640f6f6ba5bdc10", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9833730a07f3593e8ee202c00cb6968a2f", "guid": "bfdfe7dc352907fc980b868725387e98c72ba25fad66e3f5aef253fe08f74c0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d833ac5581e7d3f38f39fc7d100a2fd", "guid": "bfdfe7dc352907fc980b868725387e984a68f8dbcbaadbd85856867f22108979"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98702e2419e882c0817b47835f6d32fc56", "guid": "bfdfe7dc352907fc980b868725387e984406a80c04af55a73a77983ffe5e69b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7903b31af5b282debf6c3ad957f49e4", "guid": "bfdfe7dc352907fc980b868725387e98a9a315c45aab494db467feda66a04abe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899dc2a295c4819663d67e868e56b71ae", "guid": "bfdfe7dc352907fc980b868725387e983a8c3487863af4472c051e86a2611cc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986de7649f12f18f94fcb0c0bc07c67e39", "guid": "bfdfe7dc352907fc980b868725387e98d6816981ceea6313947e77cee56a92fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b32f09c52e86c1905c9629e0a9e9d1f", "guid": "bfdfe7dc352907fc980b868725387e98b7b33aaf562b972d13c80f51add48ebe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a7761683b1026b192c26f71e007cecc", "guid": "bfdfe7dc352907fc980b868725387e981554b067c680388810b4279bb01c9090"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ea99d2e0ed7248cafd13b69e91024ab", "guid": "bfdfe7dc352907fc980b868725387e988b82b7f03f0fe7599aca0eb6bc19b302"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a07870aeb922bb79a5401983fd628aa", "guid": "bfdfe7dc352907fc980b868725387e98d5b311f63f7c4141612ca02ff5910bf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f731ce4331ac6baae87b3981f3ccb0e6", "guid": "bfdfe7dc352907fc980b868725387e98b8ca49ebe71371e481cd9fa1189f0f04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853944c4e1046f54b7c8982afbc0d13a4", "guid": "bfdfe7dc352907fc980b868725387e982bf9e42c763a447fed0369b194d67840"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e0141d524f5c0352e14cd434a3d0ccf", "guid": "bfdfe7dc352907fc980b868725387e982c27e0c2e19b3480b2d26149c201a246"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bea4083eedc4ba9fd5d4f7e64dd39295", "guid": "bfdfe7dc352907fc980b868725387e983ea44f99154432a79d799bde645d9b92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845aa687def9a7007ba98ad0d73824ee4", "guid": "bfdfe7dc352907fc980b868725387e98d96ba59134c05edaea06d07ff7b498e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a90a3ddf5487ee17609956e2bfe60df", "guid": "bfdfe7dc352907fc980b868725387e986ee37fe9a87122cda148c0e88c519f03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98150ded171fc7ef5a334377caaa467056", "guid": "bfdfe7dc352907fc980b868725387e985126150576979cfbaf778f2ecf9387e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b61bae3c47bdb06d4ddbed73524bf7fa", "guid": "bfdfe7dc352907fc980b868725387e9842ece1f32a642668da71f2887fb45024"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5e62f1ed034eabe3cd060d1830e2216", "guid": "bfdfe7dc352907fc980b868725387e9886ecd7fdcc9561e95f24b64a58f28db9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989be20b875d329a409e4691221f43f604", "guid": "bfdfe7dc352907fc980b868725387e98e769ba2a634ee6046e90c25f1405c579"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98927354bc2fe7ba39f88791145abb1ddc", "guid": "bfdfe7dc352907fc980b868725387e98a9adf31fa28ddb311b8b8f5a59b8cac4"}], "guid": "bfdfe7dc352907fc980b868725387e98cb82d6715c3d65eab2addb48d32be85b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1b96f727184f819fafd78f8ff8588be", "guid": "bfdfe7dc352907fc980b868725387e986708772e8ef21b4dcdcf2e8842f9d7ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccda7428b1e1e06a214f0df30f1cf669", "guid": "bfdfe7dc352907fc980b868725387e98847bcac6484e3319177fb3059281ee4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6e9503832ae66365cf6bea4fa1207a8", "guid": "bfdfe7dc352907fc980b868725387e98490ab8802e4c4ecc0137b5646c82797f"}], "guid": "bfdfe7dc352907fc980b868725387e98482293c3c7a85b6c15f0cd0933e96827", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98498c8635bdd2c0b4a5d2f36e617bac5e", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e981184a209427df512316982984ea9e249", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}