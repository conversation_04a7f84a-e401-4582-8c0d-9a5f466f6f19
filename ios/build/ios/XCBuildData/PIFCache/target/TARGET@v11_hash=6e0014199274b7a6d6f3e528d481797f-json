{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a5eccc2256226203c7999a1b23e85285", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892005d2921633428b21c2808d64855e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9857422e057d54da17bf8e667a1c9dde5e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98997e66c0e98e1db88e18272b5fafa165", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9857422e057d54da17bf8e667a1c9dde5e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8402fb7425dde778441c192bedf25e0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818d07400e285c5f4bec48ba004a0df66", "guid": "bfdfe7dc352907fc980b868725387e9885f570a1fec8afde570b6011a9e01975", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fc07ffcfb9af0c1c7bb15d56372f2b4", "guid": "bfdfe7dc352907fc980b868725387e98082854ee4a41b3fd489314e873b937f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e42e35947bfbd0ee33caded793cb2d88", "guid": "bfdfe7dc352907fc980b868725387e98f3b275689294f5da0d7646561606561d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bc59d29767bff8057af7d0e569cb716", "guid": "bfdfe7dc352907fc980b868725387e98a9b7e91d301d772c730f15807247a083", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ea512938ab25646c150471b8e6eb340", "guid": "bfdfe7dc352907fc980b868725387e98462f46004f19f4a2fda6d1dffe38501f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3b01e7483fddec9a5d3dd2fe3e70cda", "guid": "bfdfe7dc352907fc980b868725387e986575a6de7d67f4425a2ada3e509af23f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98674f7eeeec060cff236ab2cb79577eae", "guid": "bfdfe7dc352907fc980b868725387e989be957624290feefc0177aa6bca85fb5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e6ea3bf170c8b7a530cd2013f2fc979", "guid": "bfdfe7dc352907fc980b868725387e982c1c18398d97485b9a952207ba8a2281", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812b55cb4f16be30bbd4dab3dc21bb497", "guid": "bfdfe7dc352907fc980b868725387e9802af5c838625970b4872195ef6a5fde5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9c34bef561cbb5dfca816249932cc35", "guid": "bfdfe7dc352907fc980b868725387e98d2a891aaf35a15669bb6405e475b1b5e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980abfb43e885eb4881e44ad2e538ac354", "guid": "bfdfe7dc352907fc980b868725387e98f284c603505c8a52851038f4c1cb1d45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98557e53ca011217883910b7612d5d805d", "guid": "bfdfe7dc352907fc980b868725387e9842b2dd80710e75de786ce9dee85ff9ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e7b11c1be2b57b98eda74ef1b253ee4", "guid": "bfdfe7dc352907fc980b868725387e98410534e5af45d620a747f5cfbd78ddcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803a65174c752af0a7d0f59297579c550", "guid": "bfdfe7dc352907fc980b868725387e98c6b30541c48a81179af51ae8671eb35c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98709a4ba4d11b5df355eded857df06e02", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dcecbb6ac454405ae114a9435dd06fbd", "guid": "bfdfe7dc352907fc980b868725387e987273c06ce13df10f6888be466d8c45e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814bc28ea6268f093d0036b1a4219e7a1", "guid": "bfdfe7dc352907fc980b868725387e981d3ab80bd99acfd869b2c06667c9982a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa6773c747e112d1f156544f631bcf2d", "guid": "bfdfe7dc352907fc980b868725387e98f927cba9ee7a8ebc39bf57f25850045f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ac66d01f5e5c29b0ac065d15fd61939", "guid": "bfdfe7dc352907fc980b868725387e9886c2a0c8a1d0069ef0c7145c65c30c73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98692f2db71d774402ed4cabc3cfa39a3e", "guid": "bfdfe7dc352907fc980b868725387e98108bc5852255ccc67714e84eb526c6bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e84cd078557c691d491184bffedaf3f", "guid": "bfdfe7dc352907fc980b868725387e98c9eeb6ba93e695358b4d75836330bcb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4945b0f1b8901b1e34d540ba3f71e5d", "guid": "bfdfe7dc352907fc980b868725387e9810ca6f305582506da89d432333c0611b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b96ba34908b485942d2ab9b2d82126e", "guid": "bfdfe7dc352907fc980b868725387e98a370bc12ce58307ef81145e14dafa61a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d2b310e170ec931bde6ab431584dde2", "guid": "bfdfe7dc352907fc980b868725387e98cd148bea990d6a3fb870c61646e64186"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8a29dcff41147f5bdfc60e62b44e0cc", "guid": "bfdfe7dc352907fc980b868725387e98f3cc5bc7015333adea4a9e0c953e1c2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98882fdc490411c27f8a316ad35977e013", "guid": "bfdfe7dc352907fc980b868725387e986b7c2f8760ffaf440ea375915ddbb9ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987130f4aba13c03d6ff4ae6463f43ab11", "guid": "bfdfe7dc352907fc980b868725387e98369086b790aa6b2b4cc9757d633bf8db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e957dc69cf69372dd312d6d591f24e4", "guid": "bfdfe7dc352907fc980b868725387e98a64f8069bd87d144da648379ab47ab37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98642ea80e27834216d50213ffc03d7e38", "guid": "bfdfe7dc352907fc980b868725387e988c96dfc3e27c45de9ab5e6c3cc48d6e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983035ab3aecfd5724c5bae159e4074500", "guid": "bfdfe7dc352907fc980b868725387e98f7521d0015268273e0bba7ae6f0861bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d6b1a7c670d887eabc7013bab8e40d2", "guid": "bfdfe7dc352907fc980b868725387e98a64c2ab6006e8f33bde1108ec713bd0e"}], "guid": "bfdfe7dc352907fc980b868725387e986ab5620dc55715e8e8f6fcd49a2ef83a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e984fe03a62b0f3efa2b48d3a3971811d17"}], "guid": "bfdfe7dc352907fc980b868725387e98319a14c9389993bee5d96d155795db97", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987cd2512697fd8475ad5d01e85d7d220e", "targetReference": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9"}], "guid": "bfdfe7dc352907fc980b868725387e9868992c43aa4117f939ab7493574523f6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9", "name": "in_app_purchase_storekit-in_app_purchase_storekit_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982a930221dc4925ae3ad26ac05af9179d", "name": "in_app_purchase_storekit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b48307e2bc58dc7155fc2e80bc197afb", "name": "in_app_purchase_storekit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}