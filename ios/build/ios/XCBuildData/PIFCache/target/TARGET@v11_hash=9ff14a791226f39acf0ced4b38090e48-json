{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98876d4953d81adf2885da79085db63c74", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d148c5dff319768c19aaa1eeb41e6592", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d386835cb4c51dcd2323c87cef4d916d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9856027043db16367a038669a29db4fca5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d386835cb4c51dcd2323c87cef4d916d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9864d43c8dc4a98e7f639aabab9b156f8e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9886be4e5638be31422e485deb8b8d42d6", "guid": "bfdfe7dc352907fc980b868725387e98efece4e26c2fb74a3e713ea43678098a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b6d796fb45886cd8426d17e14ee0f36", "guid": "bfdfe7dc352907fc980b868725387e98300b166fd0eaa8db4776154515b251b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d797843d5d59a9d46ea70dc07677fe3", "guid": "bfdfe7dc352907fc980b868725387e984a323c2722ac8318e205bceb90d47ef6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f98e81ac423dba203c35d3af47799563", "guid": "bfdfe7dc352907fc980b868725387e987b51f12c07abbe54e69a74906611d2a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0e02098c46b815685396762d632d63c", "guid": "bfdfe7dc352907fc980b868725387e98145b4a3ffb2bcf12d0dc85d2435182cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981544f89efe554bac462aeba3bbc459a0", "guid": "bfdfe7dc352907fc980b868725387e98230e77070083dd289768a1715de3f7b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864faec19e14f1619bae43c9d95a12515", "guid": "bfdfe7dc352907fc980b868725387e9876e833a1565468ceee123d49ef49c2ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b1810afe8a191f868822c6c18743389", "guid": "bfdfe7dc352907fc980b868725387e9808380e0c93de1bb7f55bcfebe2b84f63", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820a48f7b029bb9cf0450dd60d7e57be3", "guid": "bfdfe7dc352907fc980b868725387e98e738afad4fe1c5a33786d3460244e6c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981057e6b063cc40f6a6935b71b09decfa", "guid": "bfdfe7dc352907fc980b868725387e983993804a9bc4e57ea892972c68b1b5b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980293f6c907e9cbb3e0f1e322cb4cb538", "guid": "bfdfe7dc352907fc980b868725387e98a0ccbc92ed6b40959a83efc38a5b012a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ab1bcb879c1efe732e7bfd27fd60b38", "guid": "bfdfe7dc352907fc980b868725387e98a430c5be0a8fea46fc24efeba0f2b9c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983abf65890fff7b4edb8d71eec5f05991", "guid": "bfdfe7dc352907fc980b868725387e982c5780693a03b8cd5208eb5ccc130af9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a12fe439effc1df00073580e80a89254", "guid": "bfdfe7dc352907fc980b868725387e986c8257d1f25b7ab346f3a93534388e2f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898cc7d46881e9e328ea0bc763601eb74", "guid": "bfdfe7dc352907fc980b868725387e987ac44c5f3ed794c4ba2e593465e34282"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbfb37eb1dc86c2ffea2941ea57ea756", "guid": "bfdfe7dc352907fc980b868725387e98a234fc8671aa60105f2ab2804262b85a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f92219a61142754cea269829a2ca1a00", "guid": "bfdfe7dc352907fc980b868725387e98e0dca1ba66a1625171eee071653d6cc7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f303a05f2fdc857abccb36a70a5f2aaf", "guid": "bfdfe7dc352907fc980b868725387e985a870fbf40eb339cb9842b842403fbe7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c8ff2febe618d5c390e79cb1783803d", "guid": "bfdfe7dc352907fc980b868725387e98b1a9d1dc16a7c2686d4580cc73a7e596"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a218f42999438402857c935558775fdb", "guid": "bfdfe7dc352907fc980b868725387e9807c3cafcbb6a0ca3fc733ff71e20aa29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd447d485a84e01d028c7948d1d32388", "guid": "bfdfe7dc352907fc980b868725387e9836cf4907bff20a174e094c90e899a40e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864934cebf0ccb3cb7bc8b129508d0c78", "guid": "bfdfe7dc352907fc980b868725387e98db3c1667a7b423ecef5c5b5bb99ad12f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980505d838d33077c74f5a76c6aa3bd817", "guid": "bfdfe7dc352907fc980b868725387e9859803622cf446731a8898b481ff1e7c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c33817227724768caeebc09997d76f5", "guid": "bfdfe7dc352907fc980b868725387e9814f183b7092a3664f71ec67061add451"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ed4c0d0ed6988156de5ff71bdb6ee81", "guid": "bfdfe7dc352907fc980b868725387e98da365cb88b7b44be189b8cb0a6f896a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db0a2ab63dd680ff914f9bbcb0d7cc70", "guid": "bfdfe7dc352907fc980b868725387e989d849fecbdff3a7f472eee52f077a84d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cbe896322452c2f63c3609c58b5c7b8", "guid": "bfdfe7dc352907fc980b868725387e988f220b9dafc26c62bbdb5d7ba2514de3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983216fad3b22a9ea3457641d70d6a525e", "guid": "bfdfe7dc352907fc980b868725387e98f5f9f6c1b59711f2eb9859f0c995e00c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfeb0d010fb7878e7a06afd51b5afd99", "guid": "bfdfe7dc352907fc980b868725387e98581d1de6f5281ef34bd1106adef69895"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fc5e84e94d97cc1c9ce9a3e3cd72241", "guid": "bfdfe7dc352907fc980b868725387e981ab4527b608c3c971eb554113719984c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98169df0043382bd18fc87a385670e363e", "guid": "bfdfe7dc352907fc980b868725387e9814edf5fc1bbf874423edeb74eaf047b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6269a4d1543cf8d103df711fa17de7b", "guid": "bfdfe7dc352907fc980b868725387e98f5c45608fe7272d4b5751aee077d526a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6541f0c6175a5ccf6fa6b60f428926d", "guid": "bfdfe7dc352907fc980b868725387e984acffc82583b7978442bac4a0ea9fb2d"}], "guid": "bfdfe7dc352907fc980b868725387e9861e93a2b0d5d29275b7f21d4a46c3871", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98086c5c98ee26a8f95a8c1076b90e29eb", "guid": "bfdfe7dc352907fc980b868725387e988cafece03ef7022a417a33e1e604c56b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989989c20820f989627f520b03cbca7e35", "guid": "bfdfe7dc352907fc980b868725387e98077d3be33a96596ccaf38fc44efbfafe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98feaec319fc74612569ce9f4b0c68121a", "guid": "bfdfe7dc352907fc980b868725387e98b131297bbc8c84d075bf2e3a942becac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a23171002dbf96adcc8959e638d18206", "guid": "bfdfe7dc352907fc980b868725387e98a96eaee0e80ec9830a3814552dc958c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ad6010eb45a890962bedca88bee3a78", "guid": "bfdfe7dc352907fc980b868725387e988ef50e3a220d5e2619866f3e0c9894f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbf2feedf31360a3c87e383e387f0454", "guid": "bfdfe7dc352907fc980b868725387e9891bc9552da108c6d0dd4885c425b2c43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988928691d86aefeedd2ab437831733365", "guid": "bfdfe7dc352907fc980b868725387e98a56a1589af225fffdc580badb2f25e15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887d4b43d85833850fcfb1929d78db059", "guid": "bfdfe7dc352907fc980b868725387e98bcf23e638eb7d9471cd43f840d1db437"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987748c8c6cb94f07c3dce7c9491d3281b", "guid": "bfdfe7dc352907fc980b868725387e985f696c2c3b6dd67ea381b490e0a95db9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c464069a064d44b69bcd35a0762a8fd9", "guid": "bfdfe7dc352907fc980b868725387e98365dabba242e611f9b9bebea52cd2eff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98172b63e7d38482a935dc39cca089f5ba", "guid": "bfdfe7dc352907fc980b868725387e986ecb57587405cefbe43046729dd7f0f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceb0fcab929dfe706bdc3923c21464a7", "guid": "bfdfe7dc352907fc980b868725387e98e704cb40958c8e2eb8bfdd4be2080190"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f5e8ce8cab94f57e5fa24c3bb2c6aa", "guid": "bfdfe7dc352907fc980b868725387e9844accd4f0fc43f25fc492f8a191fdd99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98661b144186533ed40a72db03833ad301", "guid": "bfdfe7dc352907fc980b868725387e9868a772e80377bb8bf82e0d7a03ab0647"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b95e0fc019dc209e85c402da9a15aba", "guid": "bfdfe7dc352907fc980b868725387e98eb33d0299ad4a2ef984a158b38dd0c4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f27191146d15c20049ea899683dd7fe6", "guid": "bfdfe7dc352907fc980b868725387e98ac8cd87d4025945c25fba5edb8f89ad2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7d2253907eeb75eb9f91f3bc8822739", "guid": "bfdfe7dc352907fc980b868725387e986cf9d3e59be06a4394121c0dead06c46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98566dc5435c5e5f4135beae760310a4a7", "guid": "bfdfe7dc352907fc980b868725387e982d4d38dfc8f7240d9e1fdcc3ddd35c74"}], "guid": "bfdfe7dc352907fc980b868725387e98a4108b187b39d896ee582f4dccac08f2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1b96f727184f819fafd78f8ff8588be", "guid": "bfdfe7dc352907fc980b868725387e98161f5a8ae72d678986d90112fb68252b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccda7428b1e1e06a214f0df30f1cf669", "guid": "bfdfe7dc352907fc980b868725387e98ac0aa9a04e749fedf6cba7e3b096a427"}], "guid": "bfdfe7dc352907fc980b868725387e98580d369413a873ae97feab9e2cdaf9d3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982208b4bd630aecd8992a3fcf7b103179", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98f4694a5f1c652f805bfde16b05c3a9b3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}