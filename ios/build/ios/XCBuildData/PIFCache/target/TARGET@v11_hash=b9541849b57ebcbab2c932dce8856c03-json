{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9811452f345823497d6e4abfafa1701ec4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9834e719c0bf1344dbf21d1a940e4436b6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894c90f479e7791cf6725df0cb9225283", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9877de9d6d662477efa9568204a98a6016", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894c90f479e7791cf6725df0cb9225283", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98629be23034a8589583986503f296fb5e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981b77daa2fdc7606228ac61fb17e05649", "guid": "bfdfe7dc352907fc980b868725387e98ffa11672f92eda622d96ed481dba1aa8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849f023416321797fd4e559a25bce5122", "guid": "bfdfe7dc352907fc980b868725387e988193ab2270dec40d59501ae545768548", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840bd1f3749f01348d536bc69e57d06e9", "guid": "bfdfe7dc352907fc980b868725387e98f0507375c6f1123051a1f3f965cfe6f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d252b56828c4fe0eada8c05cf7352d0b", "guid": "bfdfe7dc352907fc980b868725387e9853f500eab1e64f1e9c1be4e42caa4ca7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b93758214569b7a1bdbcb3beed2921a0", "guid": "bfdfe7dc352907fc980b868725387e98cccffeb2c4b6c02735ec55288729c3c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5ac7ef8d4861f3fe1de72bf2a5cbbfe", "guid": "bfdfe7dc352907fc980b868725387e98f3a74c708e4c2406e5a1556c37248a3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871855e2db414b084e9b12c0e5b522760", "guid": "bfdfe7dc352907fc980b868725387e983d54abd889117d539bcde1e17f2bd600"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ef0a20fdac5e3c5505e399adca98ba8", "guid": "bfdfe7dc352907fc980b868725387e98866fa212f34c46adb38340066b2bf643"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c95ae8d91dea99bdec937dd5b7486066", "guid": "bfdfe7dc352907fc980b868725387e98652075e49dcc97a8e2f3219a4d8c417d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f9c295a8193f3205914c2bf36a29540", "guid": "bfdfe7dc352907fc980b868725387e981986ccbde6ebfc4fbb0f561d1dbb42e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da650567984f9792979d822ea6a635b9", "guid": "bfdfe7dc352907fc980b868725387e989534074718b444e9b0fbd237e5034c25", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4aafb7d1975d151e0af7b5002a6e6c8", "guid": "bfdfe7dc352907fc980b868725387e9857e9937912e8df2496efecc7f9e7cf16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803dc6f66cedb9e1c342a0ef31b8e1c7e", "guid": "bfdfe7dc352907fc980b868725387e98a432090f46d8be04271d8256d1357d76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814051236e5f37b90deb3eb27b12d2199", "guid": "bfdfe7dc352907fc980b868725387e981eae8a3857d07c2c1d59b13b3c512fdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98558f251765f0643cbf82171df4c5d067", "guid": "bfdfe7dc352907fc980b868725387e98b2e004c035f07647be028cd2b128a976"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874ca89bf70dc4ee26bd9b7a68c3b850d", "guid": "bfdfe7dc352907fc980b868725387e989f2a4fa983c32e0b34804d61cc7c72b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb28805204b04b97c24c03ca0171fe80", "guid": "bfdfe7dc352907fc980b868725387e9833b425fb9776cc78b5743ff43e7952d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890da9bf57a909639289865e10999fa98", "guid": "bfdfe7dc352907fc980b868725387e98ab5b1ae47481eb4f7710b9bf94d2fb43", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b466851b2eea84ea997983d72fb4996c", "guid": "bfdfe7dc352907fc980b868725387e98c69c63504c8194509f07e3e510536530", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98041686ff68b0d71c4b6f43fc148bccbd", "guid": "bfdfe7dc352907fc980b868725387e98beadebc5a4e2dd45a876baaa2f14c024"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a79edad11d1d62db3da29f804676357", "guid": "bfdfe7dc352907fc980b868725387e982df651113c0c17dd78276162c662059c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6e63ba10007800165dd8151f96fc99b", "guid": "bfdfe7dc352907fc980b868725387e98888b001363184818b5df9c9c16387342"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a30ec816a3a88a7d9d683d34ec09e285", "guid": "bfdfe7dc352907fc980b868725387e98216af5dcf486cd3724699117d172aaa9", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cfbd5305cb9cca4596f59165db876884", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986f97b3eef8c4f7a3d35e0b644cc8ebf4", "guid": "bfdfe7dc352907fc980b868725387e986c591e17090a612d48995ddd155fbb6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98547d4ec26846cc0fbe9949f5e098b4f3", "guid": "bfdfe7dc352907fc980b868725387e98c2af5cdfbb7b1acc86172684d7cb9b59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98447780e9206aa375ea7c49a45d6d006f", "guid": "bfdfe7dc352907fc980b868725387e98997e9347a4710d9d12a06223ea62b005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98093d86ac764ccd8ce13c18f2992ba22e", "guid": "bfdfe7dc352907fc980b868725387e98cfa4d1b52c0f677540b6218848ab3210"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856c58f9aea8531cae9f388d31bbce968", "guid": "bfdfe7dc352907fc980b868725387e98946bb217605b36181e3ea60e729d8739"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826c9affd026db2772a996f9ec072f38d", "guid": "bfdfe7dc352907fc980b868725387e98d9eef53c0d89d8484dfb187767b64a91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988012f6f3c66ab0fcf84c0f68e330193e", "guid": "bfdfe7dc352907fc980b868725387e9844a474ce8399ef35d6557b39f36f1f98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98939f043b6d45692fcadbf233815b86a5", "guid": "bfdfe7dc352907fc980b868725387e98dad451e8b9e80017ad2d4be7485802f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d0e6b10fbb268471f1ad7a4a3e5565e", "guid": "bfdfe7dc352907fc980b868725387e987897084cd4d1bb757fb6bf8997fabc7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b498eb6b95ada904c7ac13b6f285926e", "guid": "bfdfe7dc352907fc980b868725387e9827fbb72df0b1e9f56155176cd17c4088"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7ad837461a707b5ab39670d999fdbee", "guid": "bfdfe7dc352907fc980b868725387e988c15f2daa0aa2c0d2f53bf81ab13b17e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983013ea7a3ece17a0d5ed8a42f6bccc63", "guid": "bfdfe7dc352907fc980b868725387e98ad16d6b275e89d3c5792535f42e34319"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fc796a1aecd7d75206bf82eb9bedabb", "guid": "bfdfe7dc352907fc980b868725387e9805b43b47803902258b40c1ace0a33e54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853be0a0aa53e2ad2a57a4249b36fd5ad", "guid": "bfdfe7dc352907fc980b868725387e981013225e09e4e6f7980b1d165d81a7aa"}], "guid": "bfdfe7dc352907fc980b868725387e983b92d1666dccc8380932bddd0d06f0ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e98017ed48f20ee1b8fbb265f4b17f1d06a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa77430775ffe39299fd399f367efbaa", "guid": "bfdfe7dc352907fc980b868725387e9815a3f6dd534a19153479475b61552868"}], "guid": "bfdfe7dc352907fc980b868725387e98170ff5e7957bc64424544e60536c9252", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981f26b0764afac6760e128187a20471f1", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98587db2aaa0d3a4430092befec1a154b1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}