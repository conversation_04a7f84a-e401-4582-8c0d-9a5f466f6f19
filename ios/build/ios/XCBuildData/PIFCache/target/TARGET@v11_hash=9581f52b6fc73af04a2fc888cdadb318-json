{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988548cd93586268a175a7af95f6e34072", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989f89b093ddbf991824321af6352dac6a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ab0988411ff45b0c89984b5c9bf1ea61", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ff737f7933bbd13264164548d62e0e4f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ab0988411ff45b0c89984b5c9bf1ea61", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ca47fb9fbe7ed4868eabae333c21cac7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9820c9b47ad995a9370c9942b70c6de4d2", "guid": "bfdfe7dc352907fc980b868725387e984c3d618fb9c7ba2e2ffb968d7817f557", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faa51a107b033be437d3b19f28de71ef", "guid": "bfdfe7dc352907fc980b868725387e98bdc8f3704ee222fe03f691d3f26aa2e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e1452bc5af7438c0c21f5fd918ba66e", "guid": "bfdfe7dc352907fc980b868725387e989f4982ea93dbc4d57636458a99a47d63", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2df7d726fb3ed0d3f0f8b2ed09f46e1", "guid": "bfdfe7dc352907fc980b868725387e98d063c557fe03cb54c3c778a4b132968c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d6e3204a50f93bbb8d52385c8b1aa38", "guid": "bfdfe7dc352907fc980b868725387e986b59fc3174592ad63b30521cac4dca17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd13739239e89bb12967b5acfa1e8499", "guid": "bfdfe7dc352907fc980b868725387e981ca52a50a7db629443c6baef0db954e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983184f53098908e8626157ec78d6fa3b7", "guid": "bfdfe7dc352907fc980b868725387e989b449d6da2f0813bf8b0fec04b46e298", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890f7a3623baf018e9f2878876c0f3a91", "guid": "bfdfe7dc352907fc980b868725387e980a7e4355434998a661aa1fd40cadfb1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a978fee6fe3948a050b70b332b08433e", "guid": "bfdfe7dc352907fc980b868725387e9888a28d1f701d36c7adac1d5e59118f26", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bfdb1b0a2c26dcf557b436e9526a0210", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9843f9160b61c8fe0dda22d73414af6658", "guid": "bfdfe7dc352907fc980b868725387e989c5b1899ea51d94fde7c5d8721a66620"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3bce3f4a40bd2ad4e73115cf5cf6f51", "guid": "bfdfe7dc352907fc980b868725387e986c9bffab9f54f52684c0737d3c6ea399"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810d00cf3b6b854fc253e962a4b677472", "guid": "bfdfe7dc352907fc980b868725387e98fd92476ad8986494dc756ded7fb8966d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856b7bc54801b7d9b6c5d4802636b9c79", "guid": "bfdfe7dc352907fc980b868725387e9878d02d3c80c3123237765bf7fca120c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2c5b68b4fa058aa0fcc87d1d9d791c9", "guid": "bfdfe7dc352907fc980b868725387e9805647d97c7c19d7708a7cf033fd5bc3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804c8de20778ccc1ee9557d6a52e2ea57", "guid": "bfdfe7dc352907fc980b868725387e9870bf7a21c65fe495621e6f4ef15cce89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d9391b440d7e8bfb5c3b85df3016f51", "guid": "bfdfe7dc352907fc980b868725387e9862ab70ced3d4b8abe2af988b629e674a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efb17e778b9ef6d9300e2bc06f238d05", "guid": "bfdfe7dc352907fc980b868725387e98f8f94bd60356ab4a38bfc43fc9da1736"}], "guid": "bfdfe7dc352907fc980b868725387e989d2a5b54c6518a09dcd9f2a7467ac93a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e98d1ccf4cf2550a30c173dbb1a61361915"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98756a5481f49628fbd0c3cc300230734d", "guid": "bfdfe7dc352907fc980b868725387e98d92d9721d7efa7da54a51d5724ad29bf"}], "guid": "bfdfe7dc352907fc980b868725387e9825f28ebbc92e971a0e3105f528c08289", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e55bab2ac5746084b65a86176eb43bc3", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e98321afef68c4b80bad1829fa4837899e0", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e98b27da38f485135a52d095da6aa255c64", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}