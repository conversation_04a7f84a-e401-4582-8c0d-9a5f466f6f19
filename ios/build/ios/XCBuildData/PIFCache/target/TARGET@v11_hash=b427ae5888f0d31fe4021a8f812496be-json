{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980714e3c71a0bfc827313da2b49e01486", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982bfd95f1f48ca6b1348a66db18d21287", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980fd84d084c08adc9c4331b7e715fc4b8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5219d7330cf2e66b18607e2989676e4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980fd84d084c08adc9c4331b7e715fc4b8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980e842831931c06a0076ce7ac97580821", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cc411f8a16501f8076b2280b1e7a665b", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e63df68f1d35ef9ffbcc66d2cf81db7", "guid": "bfdfe7dc352907fc980b868725387e9839b024cf6f83e355d78693988f86e960", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839e440181e8fb8dda0c5cae055def635", "guid": "bfdfe7dc352907fc980b868725387e9812f7a65b2ae225060f385b33f4cc27cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875ef7af726d09eb6ae416a79d0b0a7e4", "guid": "bfdfe7dc352907fc980b868725387e98cf57cf807fab8d1d41893eaa23936b64", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983506c1e8eefdeae532df3e9fd43deda7", "guid": "bfdfe7dc352907fc980b868725387e982f17bda710283057e3a2f641f82240ff", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efc790a28e9d9a2780be3e1e0e82fd88", "guid": "bfdfe7dc352907fc980b868725387e9853781a76a831ac4371385a899ac2dab6", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2af06f4e2fd6c91c64d22eeda0eebce", "guid": "bfdfe7dc352907fc980b868725387e98a9edb3addf901ee4b8cd1c9372116db3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a31ce8a656a8b42ea3cd52f0d2943f2", "guid": "bfdfe7dc352907fc980b868725387e98fc99e36fa566f7ebc989ab7471f1c0aa", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853e4cd30fe7a831a7b1bee58129b01e9", "guid": "bfdfe7dc352907fc980b868725387e98f0754fd8c182f3ab5f9b4fee559866c9", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986349dff443d1f0a54159672e02e5eb0d", "guid": "bfdfe7dc352907fc980b868725387e98d90387914a9a0610360a4b491ce27bfa", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984566ea0a458991dec91f824b2017f8d3", "guid": "bfdfe7dc352907fc980b868725387e98d06b7724754cb0db0c11b1f9d6e4ca9d", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbc1674e6780eeaaf2c3e1539e768e19", "guid": "bfdfe7dc352907fc980b868725387e9802b6f9b7e2b9d09073a239c64c542835", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcd3b1c641adda94847bf9f8a3c3379d", "guid": "bfdfe7dc352907fc980b868725387e98b40b33a0614ee8c9e8ab2cf12b36c616", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819b33179a492dfdc689c998658b24c3a", "guid": "bfdfe7dc352907fc980b868725387e988aaa55c16edfeed5e70b39574fb689fc", "headerVisibility": "private"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984224f3ace33a8901e3094f71686d8250", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980eed09b1dbac675c88087d8eed1c1e67", "guid": "bfdfe7dc352907fc980b868725387e98ec68cebc430d1eed77150a74d3f32575"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed8a920788df4df7138620a19b6f72c1", "guid": "bfdfe7dc352907fc980b868725387e98bc05c5b13b26a63047aeb4406d12fc6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a530c90db00bbeac4ce422d63fd3b068", "guid": "bfdfe7dc352907fc980b868725387e9800acf63e9e88478ed882e55dbe19ef90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d994e0ded8b735cf926074e56d54e0ea", "guid": "bfdfe7dc352907fc980b868725387e9889d4ba304d81b02aec62eddb8a5a0e8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f9eedfd325ccf63ef1ee6d1b4854c50", "guid": "bfdfe7dc352907fc980b868725387e9895553a5279ce4ee342b87e2efd751a6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d04c8620e85b45260e4a3bf6769d035d", "guid": "bfdfe7dc352907fc980b868725387e98faa8947fe0996a3d007a03d4553808e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980136a77fd23090566615a80e8f59bb96", "guid": "bfdfe7dc352907fc980b868725387e9857528f9b732a84025b689c44fb315d0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98041bb74c96cf853d8d9a7185bb4276d7", "guid": "bfdfe7dc352907fc980b868725387e98699c2ce29e22d171c50712a065420ac9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e6609f58409156eea25115ca4517f3a", "guid": "bfdfe7dc352907fc980b868725387e98fa032a5ebebc03157f4ba56755f1db79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c191a2c5d7b09464fbfb7fe7fca328f", "guid": "bfdfe7dc352907fc980b868725387e9828c0bc39534369bd1593184250248e33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d1462011c03c7b1ef309407a2cdcbc1", "guid": "bfdfe7dc352907fc980b868725387e98064d25dab8dc6628d9eb7abba931741d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c2b2c61ae76d15f1b47f60c60ecb017", "guid": "bfdfe7dc352907fc980b868725387e98a40e809a1447f6fe921870f9216eb5d3"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}