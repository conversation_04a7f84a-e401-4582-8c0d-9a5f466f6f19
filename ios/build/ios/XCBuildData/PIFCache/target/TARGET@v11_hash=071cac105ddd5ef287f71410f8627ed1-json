{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986302fd04397b22f7bf2d154190e5f46e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c281d4509377e7251e2785fcc54b0176", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c792b3a1fca9a3735ed64665444df25a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98edb77d66e13e8dee699274ecdf91a8a6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c792b3a1fca9a3735ed64665444df25a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b693028c00b551b2ed5715b58e42673c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bf1f053d255eb1b939308b0cf64d3941", "guid": "bfdfe7dc352907fc980b868725387e989ba99bcc540354e904cf7b1ae6a7f83c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bebee706f22d274b5f4a7df6e04e6725", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986ed8132b34b892ec4ccad480e5813cef", "guid": "bfdfe7dc352907fc980b868725387e98292fcb21a4d03dfad07d04d27c8dd82c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2f7c57293563e5cff89ed6e63497e5b", "guid": "bfdfe7dc352907fc980b868725387e9879ecc6960f23704518046e87c775b71b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f98357f8a933deca4fc97e5a5ded73db", "guid": "bfdfe7dc352907fc980b868725387e987b923d30c4a04ec2b83b4cd5a493ef10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a58deefe790c83cf5efbef4f5b2f0f5f", "guid": "bfdfe7dc352907fc980b868725387e98a32dd3174d4b8afe114adbb58a76acd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bdfa7972b0844191dcafd0d8748f978", "guid": "bfdfe7dc352907fc980b868725387e9866936c29460240ce36a1381646ae577a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d464bef37b3649d66fcd341706c7285", "guid": "bfdfe7dc352907fc980b868725387e981bf56e34fe023228ae8ef79c4fc46d0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832741f57a40e77e48b7b7b7bf4e9c8d2", "guid": "bfdfe7dc352907fc980b868725387e9892f7ccb8020c90c0dfae9ef61fe6d3e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98757323da87c60cb1171d74abc3836ac7", "guid": "bfdfe7dc352907fc980b868725387e98acd40da1e7c6f9dd89942bb7b36f52b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856be0761040383399fe2420013eb8636", "guid": "bfdfe7dc352907fc980b868725387e98c206a16248410f480bc9dc55a0a14454"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed42bdcc51910ee8a18539975a7abfc0", "guid": "bfdfe7dc352907fc980b868725387e986c0835466866c4fd931bc372200c2c68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b412a567891f2f6e27c7c5170c20ca2", "guid": "bfdfe7dc352907fc980b868725387e98a92591b3d1d0f10608b31456a93f892a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed3299e72f144c5eeb7fd024838ec9ba", "guid": "bfdfe7dc352907fc980b868725387e98f16432e355b21d92ea98845d1270b46a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894b112272e92f0b5ced401b7df15023a", "guid": "bfdfe7dc352907fc980b868725387e98bbfb9fe9254b6d86f1617f389e703292"}], "guid": "bfdfe7dc352907fc980b868725387e985185f29cadf23b5ce03e9ca0376bd7fd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e984af5759852989da183a5d39f24d94b86"}], "guid": "bfdfe7dc352907fc980b868725387e9821b429353f67f9ddf73f12e6b50e7970", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987663a65ba12d1a75c6621a3850d1d3da", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e989d7ce879fa758ef4ed7561bb1ff5e05d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}