{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981f2011311af82e473f0b8922b2049efd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9867a1912bfc15a097664338ebd2b10339", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98171ea4d08d851929e060689a2ea822e3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980227f4c110721c9d401bfd28f8219661", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98171ea4d08d851929e060689a2ea822e3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98de17c2725f11cdaab384cf464dd32d78", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986de9f694098cb5ebbaa17b972cc6a5e4", "guid": "bfdfe7dc352907fc980b868725387e98ae639ea0a0ae2e3b40489dc378578a21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dcad670f50f6739aca4d0c6fc83c659", "guid": "bfdfe7dc352907fc980b868725387e98943ca78829001240736b8469e9da9abf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899e490fe1b5823fa0d2423daabef092e", "guid": "bfdfe7dc352907fc980b868725387e985dcc793de2a1fbe1f36018a0c2ec9ba2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdc6b9547254f2d6e846864bc90c35cd", "guid": "bfdfe7dc352907fc980b868725387e98d9d06a65cb9e12699c2a5560f0539f70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b579475a11b6cba8ec2385dfd119266f", "guid": "bfdfe7dc352907fc980b868725387e988763b057779894e0bdc94ab400133034"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800676eb82fc555ab0dd5f23e9425f9f9", "guid": "bfdfe7dc352907fc980b868725387e986982678d02198743ea746b69a3aa1e4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff7602ee753fb2298df4e374e87c7e38", "guid": "bfdfe7dc352907fc980b868725387e980fd27da49197eaede23a640cc8517e17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982331412ff483ec49b46d75dc8191468b", "guid": "bfdfe7dc352907fc980b868725387e98cb1c2d6fa30eebe89654a0903ba24b1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a621470abbf9fc701ecc9bde1af2481", "guid": "bfdfe7dc352907fc980b868725387e989c5fe6eeeb022909a3576e89b685eff8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982227b9bed0721a1160fa565bf8ed9a00", "guid": "bfdfe7dc352907fc980b868725387e986ddb25f03698bea8c3a3fa5f44fb87b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b2a4f38676a14790820ec83c9b14ff5", "guid": "bfdfe7dc352907fc980b868725387e982ad378c5d901f9be06e38bdefa6e8d40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a4c51a556202be6839194069e1f7deb", "guid": "bfdfe7dc352907fc980b868725387e98ac1974e584bba72bb54d55eb94371c15", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98811dd770290439d2ad8ce913c4656606", "guid": "bfdfe7dc352907fc980b868725387e9894f61545e1d57ffc7ca828fff7c677ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba2725c21395659c4bce600aa0f63149", "guid": "bfdfe7dc352907fc980b868725387e98c32a4d49b288c2e5131c5082b73f3609", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981395abd7391d9578bfd3ddfc8c0984f5", "guid": "bfdfe7dc352907fc980b868725387e9811084764f5930aa12be837e39b07326e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8d895ee89e09915cca8d69b1d61b0b4", "guid": "bfdfe7dc352907fc980b868725387e987111e5968dd772c5d2216d63bd35f0a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817fd54935bd49fe2effbf9ac2928aa70", "guid": "bfdfe7dc352907fc980b868725387e98e1eea8465f925b7e861ce3ccef1c2fac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98256f3d5fd0730fb4f99d3db03e486de0", "guid": "bfdfe7dc352907fc980b868725387e980cfe086d2d15e0ad1cad905e2db2fdfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820621331e96806a7aaf20d6820e7b035", "guid": "bfdfe7dc352907fc980b868725387e98be4ff63928a31d224520502776079018"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa3bb86cf6debc56773a6243af2be9c1", "guid": "bfdfe7dc352907fc980b868725387e98b6c8120b2683f2bb22525063600318f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9b8314ea598e74780bd02a8a09e0b3d", "guid": "bfdfe7dc352907fc980b868725387e988731e4edae9a978f71a73be82c90373b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a968f43baa40de87e03d68029aa57a63", "guid": "bfdfe7dc352907fc980b868725387e98c7c5229813c636a4a3e77fe24dcc278d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982440809d39095c521b3568cd03954faa", "guid": "bfdfe7dc352907fc980b868725387e98d67c5d9623d0ebbbb7309f8c4952a46a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9d7985d07ce07fb3c06ea21ba0958ca", "guid": "bfdfe7dc352907fc980b868725387e987996229ff49b4a53682ae18ec4757ea9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e33aee948b6809a90081a49b63163a1", "guid": "bfdfe7dc352907fc980b868725387e985bd5a2083601c64af6e982962813a33c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864d85994435c4438f120099269aa4708", "guid": "bfdfe7dc352907fc980b868725387e98f739d8237f2764ca808621c832fedc5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810bd789194b60de4ccb819be985da3c3", "guid": "bfdfe7dc352907fc980b868725387e983ab7c5dddbc026b54f0f67ce04f3ea78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e1aa44cb770529ecf37e5cbd7e349aa", "guid": "bfdfe7dc352907fc980b868725387e989686b281f03102a97d62951d15111ba5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c916162de95438ac5cca896fd8b5e623", "guid": "bfdfe7dc352907fc980b868725387e98587560989f701b04f369d306a30d346e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f82b5abee5931c663d71c55bc1519940", "guid": "bfdfe7dc352907fc980b868725387e9801edbf8eec54b5b10f7243683f072f4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817aca67827f63506529801e7a73eba47", "guid": "bfdfe7dc352907fc980b868725387e98ca5b98bf9770ee80dff9715c3ab6844b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e9e856af3d300318b7cf4797022fb0e", "guid": "bfdfe7dc352907fc980b868725387e98937fc1f09e0a35357abdf83f7c5aa21f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db73b1efcd113d7fbd557082ae17afc9", "guid": "bfdfe7dc352907fc980b868725387e98974215d78fd13814d07e3008cf6ef0a0"}], "guid": "bfdfe7dc352907fc980b868725387e98be2949afc1edc61e49f79669cc4420b5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986845b0fb63cf74168553baa1669fbecc", "guid": "bfdfe7dc352907fc980b868725387e98c402f6d5b7ee982090d780cb7cc7251c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a5bf90e4055e75d4a818b0f6f7628c9", "guid": "bfdfe7dc352907fc980b868725387e9856c5bce6f3f325b98fde975dcca37389"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbc07c4382c98c33bf6d8f3b57366d88", "guid": "bfdfe7dc352907fc980b868725387e9803360fa5005a0df5167321a450a68c0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a54cf767d0e220e1fe0b0763c712665", "guid": "bfdfe7dc352907fc980b868725387e98bcebccacb915d17e36e283f04f81fbe7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864003586ff6a3bf023c239a33d0ee8ac", "guid": "bfdfe7dc352907fc980b868725387e98f0e988af527fb9bd8cc1e42d848387cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb903c2ea9298798ecb37dc20c3fad9c", "guid": "bfdfe7dc352907fc980b868725387e9833566978ff93045c029c6290e1fc058d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878e2c80ab6bafe703d0624590dc7b40a", "guid": "bfdfe7dc352907fc980b868725387e984f22cc70a5bbf020b15f4fd0f6ffe48e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856d933414cb331e4408227e82da08c9e", "guid": "bfdfe7dc352907fc980b868725387e9824081050ec04f575e68d8c535ea756af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982961c48bb7ca496bde8402821523c57f", "guid": "bfdfe7dc352907fc980b868725387e9848463be62dec39ecbcbd2d09b2827b60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98127aa2baae0738849e754a3940d568e3", "guid": "bfdfe7dc352907fc980b868725387e98bbfb14a7a80e03517dfa7def37e7beef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861af301acea17815f384f39e7894d80c", "guid": "bfdfe7dc352907fc980b868725387e9843da5bbc310402438388876933222ea6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e48ccbb416e7880c72930a3abdcbce3b", "guid": "bfdfe7dc352907fc980b868725387e98ee30b4cf04fa2f1875a18d3ddb0483a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbbec6f6cb75289944a1016fab8f6a72", "guid": "bfdfe7dc352907fc980b868725387e9803883a8ee20b47ec0033fb486c2e50fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856c88bb60f9e147658ee3a40ab8cc71a", "guid": "bfdfe7dc352907fc980b868725387e98b2f12587d865bcfa2ed56c98e72157c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc2df4c289c1e0bb4fb572bde6f99635", "guid": "bfdfe7dc352907fc980b868725387e980230df8a5ae3f62a8b8c02bf853db7c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985baeb1fed587a11f0d6009dd38fbc003", "guid": "bfdfe7dc352907fc980b868725387e98627fca126acbf00a72e8babd4d870890"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aa316d6026e027893f47b73c87f6f24", "guid": "bfdfe7dc352907fc980b868725387e989eec48212d7d430144efd1f6f3dad383"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4801b21fa53cd51b4c10273e26c4d01", "guid": "bfdfe7dc352907fc980b868725387e98d87b7d8ceece8a421abf9bd14c687ad1"}], "guid": "bfdfe7dc352907fc980b868725387e987272811fe56c9ebb20deee0420059eb0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e984ca214dec59142fbc2d82d87254219fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98756a5481f49628fbd0c3cc300230734d", "guid": "bfdfe7dc352907fc980b868725387e9808f460aa18768b20f5c88d4699f07902"}], "guid": "bfdfe7dc352907fc980b868725387e9841b0400c5318be629c66d64f338a544d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981b9176c75ae7f5b831d14f109d2e028c", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98356feb9ec2874c5c77d286cfc270fe99", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}