{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989a879d80e06b6e62ac6cf3027be73a9a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894cd6616b81533a3d640c2d53c29f2ea", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986f5772e0d6113bc1ac804208d36470d5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d63d296cbcab0774f674736907f7ae15", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986f5772e0d6113bc1ac804208d36470d5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984c8974d27ef7fa65c2ce945688596140", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9892b4fd39e53520846c0057c88bf3b05d", "guid": "bfdfe7dc352907fc980b868725387e98d2270a9b04a3727994d79973493e59a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a9522f052bf45f9ed4e090c00d43336", "guid": "bfdfe7dc352907fc980b868725387e987d33fa33a3ddf1affe0a4b8b7e7883fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844abc65bbe8c48af7cc6c851e2a3aa9d", "guid": "bfdfe7dc352907fc980b868725387e98cb0383fd49d95d68c6ee8e3a6bd2583a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baa0b823b948b370244a1cc9a2433390", "guid": "bfdfe7dc352907fc980b868725387e98cebf05fa0678661a1e9e3f0e37ba4e2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cedaf50a4292454abea4dcb0186ef052", "guid": "bfdfe7dc352907fc980b868725387e98ee208eb8c2f2a66586e900e59d31af72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866f2df2418999b410a6da9b2929f78b5", "guid": "bfdfe7dc352907fc980b868725387e981cd0cf325d7d616f9a1acef8a1fa5030", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fc68ff222bc044883c016c1c92518bb", "guid": "bfdfe7dc352907fc980b868725387e988b64223206b960aed8f638d26cb22c02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7aaf30a2ba15af5f5517023c738ff3a", "guid": "bfdfe7dc352907fc980b868725387e9891835123d6ea28e8597e425bc03238b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98937e8f17d7f27a7304b59657d3c23b2c", "guid": "bfdfe7dc352907fc980b868725387e9812fc6534bb35c5ad03e98f96195f167b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982a44fb1708849eb49fd593fadc6b1a4d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9866d8843ee72c1451a20619eedbcd5c45", "guid": "bfdfe7dc352907fc980b868725387e9839a99a8f462ea65954cda858c04871ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abd247423f02d0c31d47ce68ae4b7da5", "guid": "bfdfe7dc352907fc980b868725387e987ecedd50b53737eb60369af9cbcf5351"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98917637ab779ac52e9d258b0cbba75d6a", "guid": "bfdfe7dc352907fc980b868725387e9826e8ca5c5a20f4f26d557173f85b3392"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899396343b927493071a321d2a3a91ce3", "guid": "bfdfe7dc352907fc980b868725387e986de609d3be1d7b154aac7b04f6886478"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858d81d66bd63a486e15d5790f351d921", "guid": "bfdfe7dc352907fc980b868725387e985396ef7a28ac7945bbaf158dff1642c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98678c1b2711fb5a856b46c9be222fcab6", "guid": "bfdfe7dc352907fc980b868725387e9817ab0398601afbf9cbfab7c5c4401d44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df71388b5922ea0e6376b0438c60694c", "guid": "bfdfe7dc352907fc980b868725387e981f21b2d01c0618a8194547ddf689a809"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be155b81a3879f04c39bb228951e5c9b", "guid": "bfdfe7dc352907fc980b868725387e983792847b02c43eef5999d5bcb160a69b"}], "guid": "bfdfe7dc352907fc980b868725387e989d4c27b1d9340ea03fdba61ab990288c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1b96f727184f819fafd78f8ff8588be", "guid": "bfdfe7dc352907fc980b868725387e98a35814b9b41b248680d6f06dff01ce62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccda7428b1e1e06a214f0df30f1cf669", "guid": "bfdfe7dc352907fc980b868725387e98a69b9f8060e4134468df34cc7ffd8ba6"}], "guid": "bfdfe7dc352907fc980b868725387e98ba64206c4a9b48a790ad3d9dc2200a7f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98adfff437949753568f481eb8ef0adee9", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e98de35524c85003b51840b8d95e5ab7f5e", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e98fbec28c04aff0cb19cc09db4a3453b62", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}