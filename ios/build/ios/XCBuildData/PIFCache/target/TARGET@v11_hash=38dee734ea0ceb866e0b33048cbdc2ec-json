{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894996802aac6a9e5ff9af8257fb6161a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980b0491debad13020a165ab8d8fb8e7a6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f8e8aaa47973e8b925aee2f56ebc310", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5d17fb1d91f0a4f64158124b05fa286", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f8e8aaa47973e8b925aee2f56ebc310", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d48975f249f96592ebac62cabeb71693", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982dfbfd5623a17ee3bb3e703e36a1438a", "guid": "bfdfe7dc352907fc980b868725387e9883c10ad6045e2edb3b45c1c6c0413b92", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983a4fdc38bf986b19d561fd705b949d72", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a6cb5fd84426cd7a1f45991eaa702fb4", "guid": "bfdfe7dc352907fc980b868725387e98e9b9a077aec2829e41f7b525cdb5f60d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b0725b150719b24f68a13a34be1b5a2", "guid": "bfdfe7dc352907fc980b868725387e982570e67fb9f7fedd33485dad3b70f9c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1fe899c9e878d01ddd830011baa59fd", "guid": "bfdfe7dc352907fc980b868725387e9839f9cc43b816718b8339bde80510af46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869603e0fd7220272407a3f8f86173821", "guid": "bfdfe7dc352907fc980b868725387e98f770bfbb2edf29761736aa131848f377"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98085db0b0417080a8310ac74cabb9fb52", "guid": "bfdfe7dc352907fc980b868725387e98c272289f9ede46ef4b875e9efcb0a9e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da97fe79a5e6014e315e18a0cae1b0e7", "guid": "bfdfe7dc352907fc980b868725387e987dcbdb064d3550814dedc5f3663cc49a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c77b99cd7d5d6c0998021133c71ec29", "guid": "bfdfe7dc352907fc980b868725387e982c77c0d39d9438f5df7de0ae9e0463c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b90e6c49b46c65679d7e2adfd9810f9d", "guid": "bfdfe7dc352907fc980b868725387e98c62712a4df3c356fb2d14520e4bc3a6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbc7cf982ec344b2708f613fa8717ae3", "guid": "bfdfe7dc352907fc980b868725387e98d01de01b930c98f206e3019a29ebf79f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d9af179257dfe63d71b79b14ee0b135", "guid": "bfdfe7dc352907fc980b868725387e980aad36993f7ed15b178195abdfeafc06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e381998aede2f2372711ea97d8ba81ac", "guid": "bfdfe7dc352907fc980b868725387e988075b30bf532e5ac57ff36c36478d668"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849baf9f736b0ffd65d2c6cc7f9d6a357", "guid": "bfdfe7dc352907fc980b868725387e98648a145e078f28a3fd4a0718c559453b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d73c28507bedaa4b95779d0c864f71ed", "guid": "bfdfe7dc352907fc980b868725387e98270d765aca56f591bb4bd0f1ee520c21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b62fe6458248f9b03916ef4e56ba15e0", "guid": "bfdfe7dc352907fc980b868725387e982447e2b4ad0d9d4c5fe77342c8401f23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98082fa0d5ca219db7d7ca9c0f6644bf6f", "guid": "bfdfe7dc352907fc980b868725387e987949d316e7882843bd3459a52b596443"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f0b9c14e586d03d8f7ddfd59c31dc73", "guid": "bfdfe7dc352907fc980b868725387e989c63b455fae66cfcf1bf2045bb506583"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871966480406a9a8165d273009ccb202a", "guid": "bfdfe7dc352907fc980b868725387e98d9b8ac7268c2c05ebcf17beed5bf8083"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983075129e93d6aa6f5cac2bb846600471", "guid": "bfdfe7dc352907fc980b868725387e9863ebd364161d238ac6ac1a39db4e4e71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98add1bd6272dbe9ec7e7c34f630c384e7", "guid": "bfdfe7dc352907fc980b868725387e9859274f810f18ca8a5abaf6485d7aa36b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb30043a8d8fd12eaf1d57222673d78d", "guid": "bfdfe7dc352907fc980b868725387e98aae161d22548d9650c0a9263b2e46413"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834d4d3082b7d665a504a63b5bf60fed8", "guid": "bfdfe7dc352907fc980b868725387e984b9e69f35acc684a0b0b6634455ccfc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9d4c1ac19c6fb208b6f19a50d86493d", "guid": "bfdfe7dc352907fc980b868725387e98f7a3c4c0949ded7fd83c7512687fbeda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc2a5b87858ac343b8fedc8e2e7194cb", "guid": "bfdfe7dc352907fc980b868725387e9882175fdd8a7348a92212b1acc11bf29f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887c35bf02d511e501cc850ad9b980f68", "guid": "bfdfe7dc352907fc980b868725387e98f37074cc6a3f5eb1764aff6977d0458c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bb8df3a7d564131064eab687f4e9bfc", "guid": "bfdfe7dc352907fc980b868725387e9805ffc0c657a1f35c8e94c7dcf34bde98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891513ff3efd2083e2cb46fef8ef34712", "guid": "bfdfe7dc352907fc980b868725387e9838fe9856c583812cc5dea1256367a428"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98128a535a5269caa83fd3017d2349769c", "guid": "bfdfe7dc352907fc980b868725387e98d4231ff192404fa5576b4dd1bcb5af26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98420265f10b64b8fa26635432c35e9def", "guid": "bfdfe7dc352907fc980b868725387e9807da42eaab9173ea67e3882f183174dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ea10b302e6f8812a3402d7732a95939", "guid": "bfdfe7dc352907fc980b868725387e9873fae3d3f058e14409ac006e6a87a07b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874c4a1f9bdc25060d2ca59c48fdeb733", "guid": "bfdfe7dc352907fc980b868725387e98aa39bbd93ac821a9803c6af231df7d21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ce6d0d1c02d18596acf8f2f18a0ac44", "guid": "bfdfe7dc352907fc980b868725387e984769c1d580a788edb1cc39ad8c6fda31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efed1e95fd71466f9c2ab2b1c1f66505", "guid": "bfdfe7dc352907fc980b868725387e98ffd048e73eb3980bed74fd87fdd4e673"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98191330bb824e91bf7f2de24fe94a2e46", "guid": "bfdfe7dc352907fc980b868725387e988e26feaf37d9fa439cedd666948bb9e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a20513f290b050d0ab500f422527d5dd", "guid": "bfdfe7dc352907fc980b868725387e981c4bd8d279e2891906d36301438a9802"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ba7581c2b666e70125c039601655e4c", "guid": "bfdfe7dc352907fc980b868725387e9823383c730e88e4b98cbd9bbafc7e7638"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf4ce59b2e06ef1c0e480b421c15c182", "guid": "bfdfe7dc352907fc980b868725387e985eb53863a235762ebb44fa330fefcf8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b488c793ca397a29761cce6fd1690f46", "guid": "bfdfe7dc352907fc980b868725387e98ba1d41b32a7c8dc4609a4b1a4781c8ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982782399b2e423a68e26817e72c2e691e", "guid": "bfdfe7dc352907fc980b868725387e9825b8e602e8efe277e71add18babc9694"}], "guid": "bfdfe7dc352907fc980b868725387e98579fbecb47bc990731107675682637c6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e9817874d657b3f6cd3a4f0acf89676b8a6"}], "guid": "bfdfe7dc352907fc980b868725387e98e3e0578284590adbf8b73f59c73cb7a7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98acb6d21f8b6048502a790d59f4fc84cb", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98be611f6cbf3440ae2e558dafe4f7c459", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}