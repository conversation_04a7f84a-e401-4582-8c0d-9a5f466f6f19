{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f22a6ca7307d1049eec4e84cab23ecb7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9857b7a8ca875f98c21af24329342364d8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9806d87264c4082e510b23ac5c9d83ee76", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98068202740173e96b64e2696e143a779d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9806d87264c4082e510b23ac5c9d83ee76", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982401a3ec5793999f0fa0e7b2751159ed", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a59579641be696238ac90e32345b988", "guid": "bfdfe7dc352907fc980b868725387e98900da4f412f1f224d1f20fa077525e33", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9833dc39d7cced2611e737e5a850922bbf", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9878263f3d26f399012243f31216a3f7b6", "guid": "bfdfe7dc352907fc980b868725387e9849f47671cbbf5412bf37e4b95e2a27c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c18874653de62ce0fffb7a5a1e9ea4b", "guid": "bfdfe7dc352907fc980b868725387e9873f2d66fc38247b259b528c690c03c22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841835d90fd58729e77fd3502c05a353e", "guid": "bfdfe7dc352907fc980b868725387e988f97dd7ee3e68d6602570d59fba55077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f56a39480744e1066c1dbb45eff2732", "guid": "bfdfe7dc352907fc980b868725387e98db6e3ad6414ee35b293268488fbcd06c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aad16a8f76a19c01c5cfc1640e3c365", "guid": "bfdfe7dc352907fc980b868725387e989ade17396b1fbd60a82091de0ea43f75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcc2ba834089682772b696960695da53", "guid": "bfdfe7dc352907fc980b868725387e98f6841260f5a35d87180479edb927160c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98084e094fb40ae594201dc3d15c499b92", "guid": "bfdfe7dc352907fc980b868725387e987ea9722bcb460901f8028a215011bd99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fead7aa4dec76fc971657f2801a3455a", "guid": "bfdfe7dc352907fc980b868725387e988b9135c63561aa70221b21731770ffc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b88231669efe8036ae75152da8efa34", "guid": "bfdfe7dc352907fc980b868725387e980c0e27bf3bdead9e2fbdaae0d388f024"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffa3e815e9d78f663f298ab12c6096d1", "guid": "bfdfe7dc352907fc980b868725387e988f3717fd8585ec2d0be7175a5d77acf0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874f6f5536408539e7f228678772ac6ec", "guid": "bfdfe7dc352907fc980b868725387e988d84b51b71d03f4c62a877c3a2d1a899"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e954931548704854ccb3104ce0840b4c", "guid": "bfdfe7dc352907fc980b868725387e980b5a190073c134e0ed7a84c1d223d42c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98634464d2ce1d43e71b5be2a58127ea49", "guid": "bfdfe7dc352907fc980b868725387e98441d6686c3a243b23167e3085c064397"}], "guid": "bfdfe7dc352907fc980b868725387e98954fba655608db2cc4702019aeb6ffca", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e985d61b43a7d277bc18154c5d7deabdb1c"}], "guid": "bfdfe7dc352907fc980b868725387e98b0bffc55d0b8532007e6422f91b01f27", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f55ee706f5196134e55b4bb64d020541", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e987eb7305616fffb30a177129951395ce2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}