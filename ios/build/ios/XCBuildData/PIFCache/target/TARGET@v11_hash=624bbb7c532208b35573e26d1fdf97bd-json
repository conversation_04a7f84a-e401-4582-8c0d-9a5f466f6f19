{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9801838180a1210cfbb42e4b4b88c32d4f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989886ef651df8bbd92a097edb875339e9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d9baf79f37e3d39c8e600f1a57c79651", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986ff5ef3d0b144c50ca9363df263b6f59", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d9baf79f37e3d39c8e600f1a57c79651", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98124e75e4cbfa06d581f43819a25abcc8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9413608ef8a3680595a7d27e6b6dabd", "guid": "bfdfe7dc352907fc980b868725387e982fba98e4f484c194f5c3d71ba3a4f852", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823991ce984246c2aba6340317a4cbe00", "guid": "bfdfe7dc352907fc980b868725387e98b7355fc69081ed2f30da639bde6a1d13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830e819ec6b700af811c8fa15c4a7cf0b", "guid": "bfdfe7dc352907fc980b868725387e9835b3a9a8cfcdd1d656256771c4bd4a28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b4ff5245dcea619ca279ad0badeaec7", "guid": "bfdfe7dc352907fc980b868725387e98288c32b5835d7bd5a1088d7048426617", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981544fd4836b933d1dc3fe6c9e608d2f2", "guid": "bfdfe7dc352907fc980b868725387e984b38384ea34ddf09c5af837a353158bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98829c4350cd21696e55fc5d63b601a454", "guid": "bfdfe7dc352907fc980b868725387e98c3d0f8c63052aed011cf31e2b6637806", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98692730f6543037f7e637616d472caedc", "guid": "bfdfe7dc352907fc980b868725387e98cac06320ea06767fa65e792652220163", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a51e521a6121a230070fb7b6cf4556b", "guid": "bfdfe7dc352907fc980b868725387e9855ae6b8d4a16a25efd8e4ddcb6526797", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abbaab47a03454732e61dab00949abb5", "guid": "bfdfe7dc352907fc980b868725387e98752887f673b5f0a31e37b8ab62f45231", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98155391679f552673de7141f7aec54e53", "guid": "bfdfe7dc352907fc980b868725387e98d813eabb05799214268c34f281727ddf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfc70fe1b1d0f416e480885d1a7ebd3c", "guid": "bfdfe7dc352907fc980b868725387e9825ec12bd6f2d5afa102945c4d42c135c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98212109881700953a9e7c3d81c874111f", "guid": "bfdfe7dc352907fc980b868725387e98fe652e3eb9603dca2879c957440e7e5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852264f25e2e086ed0e173dd048ba3cbc", "guid": "bfdfe7dc352907fc980b868725387e98fda229671df05d921176fcccb20a0a46", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e568bb7059b05710813c701b2eebd8a3", "guid": "bfdfe7dc352907fc980b868725387e98277a6b3038cdb743f4a6c2aff2f9d165", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98176b22df5043fe46922410848395c2a5", "guid": "bfdfe7dc352907fc980b868725387e98582533f7053834fb136105b4bc7f4149", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3a43958aff2c5d6371b5d6789a89023", "guid": "bfdfe7dc352907fc980b868725387e98654c8338dfc5fb6ce7d863fb6c5af1f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d54019fa98369262dce9d3b96836b4a", "guid": "bfdfe7dc352907fc980b868725387e98f907b5fac50f67e858cf1d56e1599c21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809ef2f8fb153944c3218ea6e1ac656df", "guid": "bfdfe7dc352907fc980b868725387e98f7319cc26b9d128731aca79f1382283e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817be2cddf09d0d9ed2cbc7efa120ca7a", "guid": "bfdfe7dc352907fc980b868725387e98dde81d8c8c860cc1216a5ee6d70d8487", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db10c0ebb8bd4927b6ccf8cd6e7a63ea", "guid": "bfdfe7dc352907fc980b868725387e98860071fa5faba7c9309ebe2b78cf3f64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f190c133b2f01158cc4154731ae7f69", "guid": "bfdfe7dc352907fc980b868725387e984492561ebd3f0b6c091a359e3d691ac2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9ce27d86a3bd349909b253d696252da", "guid": "bfdfe7dc352907fc980b868725387e98f24bfa06d9230a5a5d6e972130a9387c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0cc9a521d88bfc8f508879368565f18", "guid": "bfdfe7dc352907fc980b868725387e987388a84518bbb879061bb2449b5a8039", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b15b0816d337289d3991fffe0213469", "guid": "bfdfe7dc352907fc980b868725387e987b69bdbde81d1a87fe1218b2c7a4835b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982712498a8d6bc541bb661bbcc2b1a252", "guid": "bfdfe7dc352907fc980b868725387e9849c2e8fa882744ac1696d8939dbdd18e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98041385b0f6a7cd9d7dc8a32a5af223c3", "guid": "bfdfe7dc352907fc980b868725387e9822ab0703a5684ab369aa407ebd08a399", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988719a3fe5d5fa9fcf0cbf580852f7b37", "guid": "bfdfe7dc352907fc980b868725387e98a2a6718c528f353a72a429bdd2c4c8fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98724a78b184f147836e5b67fb95f5625e", "guid": "bfdfe7dc352907fc980b868725387e98a6480e7fe92dea16ef02a047ec7a2bf8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833717c0457d28f3b8c97353d3f1a2989", "guid": "bfdfe7dc352907fc980b868725387e98b4bb64100b4bceaa12f149559f5c7007", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d0c84d81d723352e2f52deb351084e0", "guid": "bfdfe7dc352907fc980b868725387e98ebb4a815dc45050b0cd0bd57e84a4139", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987c3d0c7c47a71f91bc059240ee9b06d2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98400625ded7ba668e5924b0906cd158ad", "guid": "bfdfe7dc352907fc980b868725387e98fea7e8823b30f66dfa35c12c0863526f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cedbce24036a33652a98b580a015022", "guid": "bfdfe7dc352907fc980b868725387e98df6baea0c2cd3026a646285ff8b65a65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be5195e06affbd3df04c88de286d26a3", "guid": "bfdfe7dc352907fc980b868725387e984b39ec131432da1c12f8d0f8c0b2d605"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98054b9da791bcd2a5f8f4b2ef3f656890", "guid": "bfdfe7dc352907fc980b868725387e9853b09c21b16db75906860cc98bb5c28a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985be922a3bc425e7607e8662967ea7b05", "guid": "bfdfe7dc352907fc980b868725387e9877d8c133e0563e5e94c73f68023c2268"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eff23e35983717cac43d27138d6b0619", "guid": "bfdfe7dc352907fc980b868725387e98dfd6ba96eb934d2d1543c58cb40cf821"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f1f0e5b1612c22d14c5102a12d27373", "guid": "bfdfe7dc352907fc980b868725387e98e0eaa397e5cc25aa4eff4b1a7a9da586"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2005bb93303c608f2b0fbce40461872", "guid": "bfdfe7dc352907fc980b868725387e98ad5e46f68a985f15e9e189df4ef224f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d0890602a82fa69e6528d0be3e8d3c5", "guid": "bfdfe7dc352907fc980b868725387e98fe9100f4747589c2b2c5f02c6612ad28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98913f6a85dd865ae9c7b4857b82b14c7a", "guid": "bfdfe7dc352907fc980b868725387e988d7e70e7b106d3af802daf683bd5b0d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980385f3034bd8bba9b413f23e8a56d8da", "guid": "bfdfe7dc352907fc980b868725387e986986e3c3943dfdf9f2a25a7c664cba5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b24bd336754bd5ea2f1dbd5c2f4371b7", "guid": "bfdfe7dc352907fc980b868725387e98171e4dfa20dd2f61cc03466df0e41463"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98073fb4d471dd70c8ad0a71ed230e434d", "guid": "bfdfe7dc352907fc980b868725387e9825f8f2ad23c9b93d5ff0889e329ae3fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ce258bc733fd820358f11fc4e1ce992", "guid": "bfdfe7dc352907fc980b868725387e989a2c66059dc1a21d4a3526f97699eda6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ccd7882a351863f9b2efd4e4776cfad", "guid": "bfdfe7dc352907fc980b868725387e98b4793af539ce4aaa31609602d40082e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b42dab144f5e3402e86e51e1a5b3fac8", "guid": "bfdfe7dc352907fc980b868725387e9819a33865923b2840f8a3ead8734293bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc3db666c8d98e62fbe459f3716d7345", "guid": "bfdfe7dc352907fc980b868725387e989d00fd87b8509c1169df5a07d2100624"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c0e9d467d0b9ca96a8327fdd81a2533", "guid": "bfdfe7dc352907fc980b868725387e984e4a0b0260a1462c8e066fba6ec4de5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d11b585a2cef8c5da44f896425d9cb9", "guid": "bfdfe7dc352907fc980b868725387e985509a40ec7b39a2687affc82aaf0f89d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb8e91aca4f64583d719b20a95c24de4", "guid": "bfdfe7dc352907fc980b868725387e989b91340972c3661e3096324c3b536c56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e94b7889d8c02552b0d96cfb49fe87e", "guid": "bfdfe7dc352907fc980b868725387e984a9f286d51b58d0058588a21dc576eb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819e2ee8c0e365545d01f3225e749daf7", "guid": "bfdfe7dc352907fc980b868725387e981426e17d0fddb9e5033655b5d663ad65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe9fe7f8d08686e4c780a7b244ead22b", "guid": "bfdfe7dc352907fc980b868725387e9897c0b65d29dc623d5232ea7c5eb162bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889f59ed3097fcd8329d235137cd65933", "guid": "bfdfe7dc352907fc980b868725387e98c53cb58b0891ff4f75b7d260c01fb3e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d17c62ad64548a39a0331e7630d2f7a", "guid": "bfdfe7dc352907fc980b868725387e98d825eb51c7d97b27e37acf1d61bb87ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844284e2212ae760a92ade671be38a83b", "guid": "bfdfe7dc352907fc980b868725387e98669261eb717be02f042a33891b035ecd"}], "guid": "bfdfe7dc352907fc980b868725387e980ce341fde21f63883bd034e2e07f9c9f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1b96f727184f819fafd78f8ff8588be", "guid": "bfdfe7dc352907fc980b868725387e9855bb418263f94e4c41129c8676619500"}], "guid": "bfdfe7dc352907fc980b868725387e98de5161b787a1c2aadcf7f35b8f43ce27", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d9a9ccd97c697cf959c1243d33b775c6", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e9840b56e6be0775481162ee096af41fcf6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}