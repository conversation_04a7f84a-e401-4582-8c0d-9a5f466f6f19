{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f18daceeab9edc6ac884cd48765d4b5c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b080dfe6a7b5dba420c182061e2f6249", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982cf65fd3a3c887bc4ca22c4a57c3f21d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9810d198e9dbb73452aca1193e59abffef", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982cf65fd3a3c887bc4ca22c4a57c3f21d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9883b672b8b72067d4d9fe796695420fa2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98287f05a7f9c741614e246d7787b4ed23", "guid": "bfdfe7dc352907fc980b868725387e98d0ef36729d90f1ac5c8d0169dd3ebd00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98937e48c23bbe399d0f1637ac05e53fc3", "guid": "bfdfe7dc352907fc980b868725387e980ab7dc695247c0288e5d4c4278b3f9cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b199141821ad79192759f0ba9385d035", "guid": "bfdfe7dc352907fc980b868725387e98002b566e9595d25753a12f3578922ce2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e16fcffb8787f9cc573d6e647d91a734", "guid": "bfdfe7dc352907fc980b868725387e984922614ba0c2839fe54b16063a15cdea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbd89e733a9a74572a974e452311c061", "guid": "bfdfe7dc352907fc980b868725387e987a400d7a00331679890e8f4f0580f1b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981799c3935d038ac93829b3dc27ed189f", "guid": "bfdfe7dc352907fc980b868725387e9885681edb7a7b22c1c3bd330579dd03da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848a8d0c221c0647614c1611baa89f08a", "guid": "bfdfe7dc352907fc980b868725387e98a2bdfee40bf47de6a612c7d64643c755"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98255b16e21c0c69f7595e9d578bc88627", "guid": "bfdfe7dc352907fc980b868725387e98759722cb14b8fe14b0cb4c1ac57726dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882b629bdb36628bb5a9e6b04bb2f9de7", "guid": "bfdfe7dc352907fc980b868725387e98b374ad08f25b4b57fc01a10674ce60b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c31ad8236136f2a5c1b195594e2c346f", "guid": "bfdfe7dc352907fc980b868725387e98bef47290bd559e9f848e937d18a9b873", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c5d31ba0b7cc95eebc54fdf1c1ac8b8", "guid": "bfdfe7dc352907fc980b868725387e983a5276d7462f82bbfb4bfde13f25c517", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884ec7a1582f0392f20f6660d41935e94", "guid": "bfdfe7dc352907fc980b868725387e98475a1fce8c683e0ee69b7404364f817b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd7e97589536db23f7aed2a43c523378", "guid": "bfdfe7dc352907fc980b868725387e98b848ecbfe86d3a2ca777123105090d53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884fb616973e403b8b0a37320d583f196", "guid": "bfdfe7dc352907fc980b868725387e983ef8ca1b3a7ff9423b915f07c0e54104"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981da99dd1ca80c50c4ce85d4311f79991", "guid": "bfdfe7dc352907fc980b868725387e981db3d888fe046c423434b48cad54665a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98051556c2a5d7486059d7af553896bdee", "guid": "bfdfe7dc352907fc980b868725387e986bee1217c09bfac96bfb9057796d299f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98712f5b5c5c1f482e7a909f80109605cf", "guid": "bfdfe7dc352907fc980b868725387e98063e3c0db112250e7ee43f1f0df45543", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986928e1007ff5df809701f04fb108a093", "guid": "bfdfe7dc352907fc980b868725387e98d70377ce0d1601c9ef7d5dcb7341a5dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c97e918255284c6022267dab737f75cf", "guid": "bfdfe7dc352907fc980b868725387e98528952024ca938dcebc54dd9230c8b98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889f66edb4262383bf1591a0275e15e97", "guid": "bfdfe7dc352907fc980b868725387e98158202f4c9aa06e786a9dfb90228d20e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ad47eedfe3f68fe2f29a695cc48fc9c", "guid": "bfdfe7dc352907fc980b868725387e981d2da6abf0bf8f7eae128cd23ce74f85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830bab35e51655ddf108e8a34fce5478a", "guid": "bfdfe7dc352907fc980b868725387e98862c8dfa741af9dec54a4355607d417d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6fb2e5399b9627eebab89f477cc2f7b", "guid": "bfdfe7dc352907fc980b868725387e98c24d0acd988f3ae099a01dddd3ff0ac0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8312b4aa8e2bac6db08940abc0752cf", "guid": "bfdfe7dc352907fc980b868725387e9863366e5f3e0c28a1480f8a1cb4c73d74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e8be79d555652c8db65b62d6c72fdbb", "guid": "bfdfe7dc352907fc980b868725387e98b62fcb1831fe39e65cc66736855044d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984220135e53653a128af522bddf507d11", "guid": "bfdfe7dc352907fc980b868725387e98a76e2f0d9b7e486fa7d7212406cbae02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ce6fd4820689cafaf3ab857b8b291cb", "guid": "bfdfe7dc352907fc980b868725387e9862c4b8318386e87ccdf85e9601525d93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fad99ab1e7621c66b4141034f6d42ce", "guid": "bfdfe7dc352907fc980b868725387e9844b7e11d4aaef466362b41fa620ab22d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984797f321de4817321bb906b13f138d5a", "guid": "bfdfe7dc352907fc980b868725387e982bd12fb634f6785695af7f89c42b3b4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a9f93d2aeb6051d1a245d23053ece61", "guid": "bfdfe7dc352907fc980b868725387e98d6113b642c4d6a7717b059d7a002db1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98334bdf5eeeb98a9139b94d87425c71f2", "guid": "bfdfe7dc352907fc980b868725387e98b0182e28058e43e909e2900f55168e41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890aea628bde9d31c3272d2c46488bc91", "guid": "bfdfe7dc352907fc980b868725387e982b66edb63faac8045d2b9763d71ac562", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ad8fabebb0474360baf76ae90832df5", "guid": "bfdfe7dc352907fc980b868725387e98c4e24420091e959e1cad5d4aaf9b97e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e488eaa81d3d3e5f6178abcec36713e", "guid": "bfdfe7dc352907fc980b868725387e98e047449d53bfa024339ff776350c63bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa4f1f2be902a04274f09fc28ba113ea", "guid": "bfdfe7dc352907fc980b868725387e982cd075ecd65f23e608096b2d2df67d93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8289bc530c626e47d6ea1f47eaa9bbb", "guid": "bfdfe7dc352907fc980b868725387e985985e6007f197d61f56f9aab1a7caaac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f735c5c9cb26703b21e19e63ff6e3b55", "guid": "bfdfe7dc352907fc980b868725387e9810fb2816791b548bd5570d64a92a64d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832e2d69e6910e211ad85056bfc030085", "guid": "bfdfe7dc352907fc980b868725387e9806191a9fd1ee39789d5666b65c6ccfa1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f01c856627a4e2347d8afbc9f52a449b", "guid": "bfdfe7dc352907fc980b868725387e983d49f81e9f318b85f95bed6b9c674021"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98633201413c6b99567300e1cb99808868", "guid": "bfdfe7dc352907fc980b868725387e984761b6f9bc4083b49d067ac025fda95e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c8a0d5da5b516be8e28c019d5baf744", "guid": "bfdfe7dc352907fc980b868725387e9883dd0d6ddc5eeb8208aa715a6bc4bc15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869fd964a5308d9f592462c2892b2a530", "guid": "bfdfe7dc352907fc980b868725387e98d19f751a3db3b926ed6e8868bf3f598c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983136c923d66a6056f6b50ebcbec98b57", "guid": "bfdfe7dc352907fc980b868725387e98629588802bc1795f5255ca729fd75ad3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982de8dc8f49fedec33f1ef65058c22576", "guid": "bfdfe7dc352907fc980b868725387e98fac4682dd396301a93b61f337ef89946"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee7198891c6d34f2dc7b6d68865f32aa", "guid": "bfdfe7dc352907fc980b868725387e98988ff208e8cbbd935ff29edf5b7fe796"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ac24502345f5829a43a75c28bb19c9c", "guid": "bfdfe7dc352907fc980b868725387e98c7584ea1fbc59835c2cb788048f0dead"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98327147200d7bdbe7479f7ae34aee1cc4", "guid": "bfdfe7dc352907fc980b868725387e983cdc8fbae690d946102808081cd4b2f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d159c7402fda6e5a97b083e95d53a739", "guid": "bfdfe7dc352907fc980b868725387e98addbc157cdaa750659490d8784662e49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f7398564db583f782f7077149b14d05", "guid": "bfdfe7dc352907fc980b868725387e98eb151fd99cafd1c57100b8644e443d88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e351d08a4b5bbc5958925d29791779cc", "guid": "bfdfe7dc352907fc980b868725387e98b50d9eb8b85ca95ecea33c75cf17a4c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d288e66852137b107e859813fb3fc8e4", "guid": "bfdfe7dc352907fc980b868725387e98959f0fac09f2277c4280b0d7599b6237"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e47604015d6f8cbec6dccc38b370f3b", "guid": "bfdfe7dc352907fc980b868725387e98e39648e638ba5c9196de4f1b01a45e4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3684d5c398fb48cde62612686c03d4d", "guid": "bfdfe7dc352907fc980b868725387e98331515324dc89b284fc17c2897e41c89"}], "guid": "bfdfe7dc352907fc980b868725387e98dc22b00b2a03271812bcf80881230a12", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bd8442e0e55962a6faa62e6b315c51c7", "guid": "bfdfe7dc352907fc980b868725387e98062800eae4973c666cbe66396cdd333c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e8f83ecf5edf8a7e4789a4cb4e6128b", "guid": "bfdfe7dc352907fc980b868725387e9898e1e80643177d282aee6e91c7079220"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a02f4f6ab2e4a0208bb465d9c9ef9c4", "guid": "bfdfe7dc352907fc980b868725387e9807cc9224867d06e06a1810a0dae60e06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988310edf5e63a1ff02c87728c4f062eac", "guid": "bfdfe7dc352907fc980b868725387e98101e18f97e18cc8186ecac8a1cd70958"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d11547b15b5ce01f856bc9f98262981c", "guid": "bfdfe7dc352907fc980b868725387e98e3784a148edd264ef8fed722acfb124d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8a7961beda0a0527e49be00252f25e5", "guid": "bfdfe7dc352907fc980b868725387e982575cfc4256575f918e63b0b52f7c4c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e39bb847b1618c2732fbdb26bc0d79da", "guid": "bfdfe7dc352907fc980b868725387e9817946cd457e7724496672b0aaae08700"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a67a5686e46e63d996d5fda802489a30", "guid": "bfdfe7dc352907fc980b868725387e98bf834c293d6458744d1b8347b0ae9581"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804702ef249ddee16ea1256d31910dfbd", "guid": "bfdfe7dc352907fc980b868725387e98a0ef2170f00186ac1c97f9c0daa97dfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a363275caaf4f9aad1ae96fb78f8e368", "guid": "bfdfe7dc352907fc980b868725387e989e368967a41248a7a4faaa13f82f55b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9529cc927f7ba4b33090106e02095a7", "guid": "bfdfe7dc352907fc980b868725387e98fbb877a2e08dd92a6d0b9283713240ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9a578c44b26e3991563efcca5fe3558", "guid": "bfdfe7dc352907fc980b868725387e98d3fbeb274c059166aca110e7b83c0959"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f417bf9ed2e120ab9c716582d358959d", "guid": "bfdfe7dc352907fc980b868725387e98782178b5d4742068d46ac032e9bd380b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb6d0567a4be708cd4722e118775ddde", "guid": "bfdfe7dc352907fc980b868725387e98cda36c92eede7a54091065a2524278db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8163988ff3ced016b88341e3ab17a03", "guid": "bfdfe7dc352907fc980b868725387e984964e0f51895ac0f8c611c805ef29a1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c77eb36c9d686ea2584a586c29904dc", "guid": "bfdfe7dc352907fc980b868725387e9829ef67124e03672efa5032344091ff4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1bf116efdf5d7cb5a2fcceeecaba34f", "guid": "bfdfe7dc352907fc980b868725387e98fb7733c503603cfd360510aa8aa02435"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ce4f838a2b6e4906d500d253826ae93", "guid": "bfdfe7dc352907fc980b868725387e989cda2ac88c7a3ef90d148673e7b5e2a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987baa2b335f9105c38c85e4f8612723a7", "guid": "bfdfe7dc352907fc980b868725387e989ec87c44dac5b71c36430ba650b3bdcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858b9c43118b9fb06a466795a549181ab", "guid": "bfdfe7dc352907fc980b868725387e987666db90085e5b52c4ca30345540259f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f6a2a2f19b50d32242cecc265cc693e", "guid": "bfdfe7dc352907fc980b868725387e98e0511ee1d9852a4ad3c08c32f80fc83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c42b9a0db11bd69bb7b4d2a6ac45453", "guid": "bfdfe7dc352907fc980b868725387e98d57a1dd839a7946d801736e1d9258565"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d50c028d515f43269be1cf844bfd3844", "guid": "bfdfe7dc352907fc980b868725387e98d2adafd5bc2f572f68f3551d8f10d378"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac231eec1d1300ab8cc3832bda08564b", "guid": "bfdfe7dc352907fc980b868725387e98e59997b72270a375613a0711fb6e2221"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af3a280f74dfbf4d2aea88da78e27cae", "guid": "bfdfe7dc352907fc980b868725387e98dd6153bc4b5f404641235f4b79442e53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0d4dd74463a982da597654a1fc4442d", "guid": "bfdfe7dc352907fc980b868725387e98b6d381e62612df511fbc15cdcf104041"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba000765debcba60335762ff8b83d578", "guid": "bfdfe7dc352907fc980b868725387e98d6b8d785a1d6313e8e951aa3d865fc1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c65d3362cfd1a3813adcdcbfb76ea7e6", "guid": "bfdfe7dc352907fc980b868725387e983c03821182f13cd528ac01015127ad3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce4d430224bc638aec903255e7c9758d", "guid": "bfdfe7dc352907fc980b868725387e98c7836fc9246b5ee2bf2fd81abb0aa32e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988697696cdba124dbb48a81e08e247e05", "guid": "bfdfe7dc352907fc980b868725387e9847ac485d72329c7a8f2caa53ce424f31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae68c1a2a29ea53624bfb3fb38010562", "guid": "bfdfe7dc352907fc980b868725387e988c906f42682821f3389c93a7c2624fae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8c386b9e829c5e58bbd2af7d35eb06f", "guid": "bfdfe7dc352907fc980b868725387e98de776473494463dd47da74f0d1c9acf8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881220274ced499994db93e06bcc10060", "guid": "bfdfe7dc352907fc980b868725387e98fbac65793fcaf6845644b0be997759e7"}], "guid": "bfdfe7dc352907fc980b868725387e9836adde7d89174f1b0c45d11157928388", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e9847f520dd78a285305d277d58a33b810f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872cd8e1769ca68c3427177e351ede340", "guid": "bfdfe7dc352907fc980b868725387e984acc56a64ebcb316610eed332d4252db"}], "guid": "bfdfe7dc352907fc980b868725387e980c630b92bce3b37cfe3d9203b37349c9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985e981330f0a67446bd5ec81676eada68", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98d6c51726fb77c798c2339695db9a362f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}