{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986b54aca1f35807fc6af52368f7e77fb9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fff834845048d3e42b9938419c105eaa", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e5273c3cf59c6a7f1e4566ea02aef26e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ca6f1c93a31f07e9ea1216e2ca6c242", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e5273c3cf59c6a7f1e4566ea02aef26e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a2437fa4521b86f94818233cc728e9b3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98efc42910fb2132485fb30c92c721ccab", "guid": "bfdfe7dc352907fc980b868725387e983cf1523aa25fd78fa15446b0e0660d0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a49f163a72cd7ec9482b9243bdeaeda3", "guid": "bfdfe7dc352907fc980b868725387e987073023da4d6c7c12e8c90e9526be62d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98574f9cc776d3bbb30fed4cb8ea588d85", "guid": "bfdfe7dc352907fc980b868725387e98dea6b85cc5294a463ef6ef6f0b898eb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9c58994437c782c837eb7cfbb268487", "guid": "bfdfe7dc352907fc980b868725387e98aa690e6b3bc1d97f2c851036bada904d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2577e5652a7126a22de3725800c7525", "guid": "bfdfe7dc352907fc980b868725387e98af91634ae042807f8da41653e54de37b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fb441828bab5f97c69a3a84b192115d", "guid": "bfdfe7dc352907fc980b868725387e98cc183ab175896adc2ac1f607da0fc95c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e8e469ec70be2217b6aecf2a4960a26", "guid": "bfdfe7dc352907fc980b868725387e98de0b5ad7837d4c3bacaafd2057fa232d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b4cf9d3888e62c4b478804f01426edf", "guid": "bfdfe7dc352907fc980b868725387e98f4fda1a429f8b921e6299dc3b30dbef7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc8988d415f859335acdfb5f06dc2766", "guid": "bfdfe7dc352907fc980b868725387e98258aa72c58911275cdab55020d03e7ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff83a3ce386ffb931ad25446ef5d01a3", "guid": "bfdfe7dc352907fc980b868725387e986d8d88dbdc506b72873f580b5552eca9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c41d1b492a78f32d0771b1dd3e13cd6", "guid": "bfdfe7dc352907fc980b868725387e988d3145f154e4d0a9215d83f073d59489", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865141b22bbd029668181b848e1c2f211", "guid": "bfdfe7dc352907fc980b868725387e98b1910706e1dcab1363a19a0d82d20fca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e4ef2735e0577980460e0b593a944d0", "guid": "bfdfe7dc352907fc980b868725387e98bff3a76404f1c0fcb8d8f4f788a23f5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5c251858c8654536aff1e5ffafc61f9", "guid": "bfdfe7dc352907fc980b868725387e989f5b70d9ff1370d16cb1d19f7d555f0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896b287f843c12916f93e22093f8613bf", "guid": "bfdfe7dc352907fc980b868725387e9810a906f366191b7d799c4f2b5a26165f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e070ab41a052dbb2d155b870c2e98a82", "guid": "bfdfe7dc352907fc980b868725387e984c46f2636d78e1de6291468c77cf5840", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df90977c9ffcc7e197c3a00ba8641fba", "guid": "bfdfe7dc352907fc980b868725387e9898a5f146bb661870e9dc433f87af1112"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832b6b8d5db68b909a4c68f527e5b7918", "guid": "bfdfe7dc352907fc980b868725387e980c1eca927d67a2c831adfaceb15f77e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864e6024e7e5ae0fc81a89fde14690416", "guid": "bfdfe7dc352907fc980b868725387e985e45b09e57a0e0cd767c89181a08464a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6e6affed63830c02919083c6065ddc9", "guid": "bfdfe7dc352907fc980b868725387e985a36d518783c9291995ff7d923413f7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c295a5b5116e0524d5cebfd258ea8ed3", "guid": "bfdfe7dc352907fc980b868725387e989c0d18c1e80985a11fc6509b30868ac6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983629da7b78d6be9a0fe2cf888ba19c88", "guid": "bfdfe7dc352907fc980b868725387e989e3a7d8255d8cf81525f8b2476385af3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98040e515ea7388687efe406665ce0ae26", "guid": "bfdfe7dc352907fc980b868725387e9876f8f907fe74941081504494eae776c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eecea6012495c29b9fe0003e316ef387", "guid": "bfdfe7dc352907fc980b868725387e98ea68fc112fab26acf540e8d57a05dd9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fd4b5dfd3b1d8e13af2196f01b1ce22", "guid": "bfdfe7dc352907fc980b868725387e986880a4fc30c751b92e5e0d8691d638f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988184b5ee6ff08bda3a6e62b2b5223848", "guid": "bfdfe7dc352907fc980b868725387e98e7cb05b04b19ced8cbb54b6d0a685028"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca1a87d2a0809cdbdcfc6420327bbdf7", "guid": "bfdfe7dc352907fc980b868725387e980be2c5fef0b8a226673c80bb19fbf073"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982006f2d51e5604889de4c24269e4f6c8", "guid": "bfdfe7dc352907fc980b868725387e98542271be691597556ecc8dfa57f4ed2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98221b4dab9cb7f3c0529bd60d4109b66c", "guid": "bfdfe7dc352907fc980b868725387e98a8f7224de4a22bc46b2d095f7c0d8e59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987990c04185cc7969f603c210b616771f", "guid": "bfdfe7dc352907fc980b868725387e98e79c330a262eea9ac244cefda711fe9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd3ca4ee1955040c2d3d1b2a6dd9e0ad", "guid": "bfdfe7dc352907fc980b868725387e98670dd3c20558f86dba115d17b837d26d"}], "guid": "bfdfe7dc352907fc980b868725387e98d84cf0679f7903134538462e91014f72", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9829c5f8c7e2f07a401b19738ba8ee0a7d", "guid": "bfdfe7dc352907fc980b868725387e98893df59bfefe7c9094540a5bc9a0f44a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898ca990a0f604163324b39a22898d720", "guid": "bfdfe7dc352907fc980b868725387e98eb6732e2ea089fbb2151c39d5379e977"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805be403cf8dea2da0ca1f7f0f1de3f5f", "guid": "bfdfe7dc352907fc980b868725387e98db405a3097e102ee94bd860182e6b5bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd152cc395af9da798e9f0359933ca60", "guid": "bfdfe7dc352907fc980b868725387e98e95a8ab011ee4d11365440d65bada95a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98744274a45f0f41e330af5695ed46a514", "guid": "bfdfe7dc352907fc980b868725387e987964fcbe4e297a0885735f890913df8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983056c1d14566112867162750c1a00049", "guid": "bfdfe7dc352907fc980b868725387e989fde66b48e71bd19c691ebf62fc761ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98589aca99133bba91b32b72ebe8702f47", "guid": "bfdfe7dc352907fc980b868725387e98c75f10ce12cf8578a1ba8a8d2f9535ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853c1de4cd3167e24dc9a18b6b6327c7f", "guid": "bfdfe7dc352907fc980b868725387e98cfbbf1b27d835f05c5842abad8360068"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988def68ad5fe8cb3e7ab4a538b844f257", "guid": "bfdfe7dc352907fc980b868725387e98c1664e53ccf27849525b7ca714696c6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d06abfe5a9d860ab4a85cc00f984df4f", "guid": "bfdfe7dc352907fc980b868725387e9826dcdb19c45299433d6e2fe87ec00a6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb2790f51fdc6bc15c5e768523de3191", "guid": "bfdfe7dc352907fc980b868725387e981b0fb2938334738cf87f19bcf6582b50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0f78f40e4c9850bdb8915170fa1df49", "guid": "bfdfe7dc352907fc980b868725387e98e16baa37233f0a938395233335c03b88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888bf198672aa9eaba2efae7a53ea1041", "guid": "bfdfe7dc352907fc980b868725387e986f12bd607234aa25a7e2222eddf1c6a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec0da20128ac923f05f6fec522e03565", "guid": "bfdfe7dc352907fc980b868725387e980a3e70eac85698c0d84d2406131381cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987501473c2d36a3fc86753e78f90c426f", "guid": "bfdfe7dc352907fc980b868725387e9805a5b06b5c4d15c2b8ca94cf9efe4d4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98004f119ee492663b9188044af15484d3", "guid": "bfdfe7dc352907fc980b868725387e98a607d677df4c5496b4a4402fcfb6324e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b954fe8447fbbca738e4f8216030fb1", "guid": "bfdfe7dc352907fc980b868725387e98e58e93f0508ce356b2c1450aa2b5cae2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4a58e848249ee720340bffde3b9206f", "guid": "bfdfe7dc352907fc980b868725387e985f974fc54b4415a6ecffc51b3d50cc68"}], "guid": "bfdfe7dc352907fc980b868725387e98ad98abcfe7210c4a1b3782346245013d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e9883f7aac986e59c7f46a00d6a613b9628"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98756a5481f49628fbd0c3cc300230734d", "guid": "bfdfe7dc352907fc980b868725387e9894a3e657299e02e7769b942b3dc4023e"}], "guid": "bfdfe7dc352907fc980b868725387e98425a766dbb2473176b57a00019bf08bc", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987643f57e2d628683c1aa3f698f0be09d", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e984ffbb26b61786e20a7287169f56337bc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}