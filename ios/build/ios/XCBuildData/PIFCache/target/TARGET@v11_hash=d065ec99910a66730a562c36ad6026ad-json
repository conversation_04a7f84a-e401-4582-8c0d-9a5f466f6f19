{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9874d4192792151147b3920f3aeb0585e2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98713d751a40f12d1931de320d450b08d1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982970a16cacce5d4a30d831c5dc8dc7a5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9823f288a4301ee3639d30797386ae68d7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982970a16cacce5d4a30d831c5dc8dc7a5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988eae465932c0c8a6ea6ebac226466a6a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e3e6b1a221947d0ae296e74541c49ba", "guid": "bfdfe7dc352907fc980b868725387e98e0f89d2fd00755014d88ee19a4c3ea23", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdbceffe79e5e9202ff79185fd690f82", "guid": "bfdfe7dc352907fc980b868725387e9893949e233fa69fcf0e169fcec318e941", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e498230fd45353408061baaff0169e24", "guid": "bfdfe7dc352907fc980b868725387e98343b15df9b84c68c515884d6910e6677", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98505be3df4fa5406826199c344bb684a5", "guid": "bfdfe7dc352907fc980b868725387e981988ed06d7c5f874136c5f75a058d7ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837cae2ea19b59f27b28c09ca048f0cec", "guid": "bfdfe7dc352907fc980b868725387e980caa98b761dfc6e466291d272341c509", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1b5c74c810fca4e59faa1d1a854fbce", "guid": "bfdfe7dc352907fc980b868725387e98a4cd9ff3d25ee777b95a72fe7582bc46", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee1f8c0e333d15799e5be60ded3d48f0", "guid": "bfdfe7dc352907fc980b868725387e98141adc532a220b5971d23ff8a513ecec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983499e232db0305a9065318ecf0ff3ff7", "guid": "bfdfe7dc352907fc980b868725387e987b462be659b2d5871cd004e6ceb2fad7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98716a5ba5929d5bb23e9abd324195cae0", "guid": "bfdfe7dc352907fc980b868725387e983e18c25f5ee58f9817159ec9938d4ddb", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9863832629a4f338c9b436bcfbfa3013f3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fc123e2507e5725938d2f667e42bef70", "guid": "bfdfe7dc352907fc980b868725387e983ae83dc8f93c0988d587b243e4f8a544"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822ad655197c754cf3da01c4d2d633516", "guid": "bfdfe7dc352907fc980b868725387e98204de9cc4c77e1f4ac8fc7e36d58e3f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbd48f9fdd38a3a9b491935feabd8cf1", "guid": "bfdfe7dc352907fc980b868725387e98ee81587e4c0bbc971390b360396357f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811091962d2fb79a7522b2ade18331392", "guid": "bfdfe7dc352907fc980b868725387e9883de839fb9f438685ab1e7c6f6523db5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889a71371d66c7743a126d4a85a20676c", "guid": "bfdfe7dc352907fc980b868725387e98d919a0f07fa8bc183a88b47dad288c09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987153d5d6954ff9d51555a46701278cef", "guid": "bfdfe7dc352907fc980b868725387e98088866ec30097db610200f044d48e2a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be2ca063fa78cd1b536eed2fcc2b8824", "guid": "bfdfe7dc352907fc980b868725387e98beffe0e3a03c496e74584cbc8eb016e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854991ab8844f3d9ee7f0a83b597266ee", "guid": "bfdfe7dc352907fc980b868725387e9818b828ef2eaa9e99f8df18ad2523396b"}], "guid": "bfdfe7dc352907fc980b868725387e98876c01da1287555283f22be93628989c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e98f9010e260f08a5bed1ad1c01def67aa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98756a5481f49628fbd0c3cc300230734d", "guid": "bfdfe7dc352907fc980b868725387e983adf5e009abdaa35baaa859f074c2ca9"}], "guid": "bfdfe7dc352907fc980b868725387e98e608844d4db1f2b4928acd6f1622e798", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98025d7acaedea170aa105906b944f9b52", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e98db9b578fca2d2cca34a4c51f6fa8cc39", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e984673deb0fc4a61c11c90da4364ae0c01", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}