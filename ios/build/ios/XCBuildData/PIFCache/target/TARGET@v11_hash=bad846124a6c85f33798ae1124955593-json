{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9881a97f5cc6bf9f4c6eed0354ac18db32", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PurchasesHybridCommon", "PRODUCT_NAME": "PurchasesHybridCommon", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.7", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b1369827c10fd526d518716d2017c663", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9e298ac63c5693397d8c860b0b1c563", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon.modulemap", "PRODUCT_MODULE_NAME": "PurchasesHybridCommon", "PRODUCT_NAME": "PurchasesHybridCommon", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.7", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982d864be927436a4b227871bd99b0cdd3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9e298ac63c5693397d8c860b0b1c563", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon.modulemap", "PRODUCT_MODULE_NAME": "PurchasesHybridCommon", "PRODUCT_NAME": "PurchasesHybridCommon", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.7", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9847670147ad6af096c7dbcf4d6b6105c7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a4ecbbe3dc13f16bdc6ceafc8b29b9e7", "guid": "bfdfe7dc352907fc980b868725387e9876ca9d6ba308313926ef0a666c103992", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aee2d57a82e4c5ca30d8881c600c5201", "guid": "bfdfe7dc352907fc980b868725387e989e2195583db3b20c2eeb18eca1183411", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d2aafab155c19e08d5a292f37da26269", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cc167d7421693ccfb3ddba3217c863fa", "guid": "bfdfe7dc352907fc980b868725387e983f2074077da0f05d307eb6df98ba9d14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e329293f48f4e439ea45a6d05cbd879", "guid": "bfdfe7dc352907fc980b868725387e98d5b2f6b53c3242dbe686ba2d14331b72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98366734377b8d55ec728d6013c1606eb8", "guid": "bfdfe7dc352907fc980b868725387e98837aa49b5c2dea18fd5ffe1822658f41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a65c9ca26af1cb28f472d566b75d6f7", "guid": "bfdfe7dc352907fc980b868725387e985e85380df80037a1b93780f8a0750c42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8dcdfddf21eb427e8257eb86a1f8e0e", "guid": "bfdfe7dc352907fc980b868725387e98b5836d669f901fdc7ad039a3898fd65a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bae338a08dcefe4a02774e676449efc", "guid": "bfdfe7dc352907fc980b868725387e9856a9f86d33a7a16411926c91e2a8e94d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fde85e579bd9d3591f14708e0bde89e6", "guid": "bfdfe7dc352907fc980b868725387e989706c2f79a73440dca2a9f02adf8a2e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2068ff019b042e5bcf8f318a43ac786", "guid": "bfdfe7dc352907fc980b868725387e98cc120838c883d2a652c3f2f1d3e06d02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdc9a7060772f4d2694889ed43922f1e", "guid": "bfdfe7dc352907fc980b868725387e98b12a3a290930c595f6e51441420c6382"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fde75fae0547f41db3fc3afbcfe86e0", "guid": "bfdfe7dc352907fc980b868725387e98f301625e146db41b8d648f645e6617b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ea4eb0f5e7cc03113681d131f077bbd", "guid": "bfdfe7dc352907fc980b868725387e9895fb1b36790f753aaffd60a5043f366a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb78e354eb17c810fabed309e86cdd4a", "guid": "bfdfe7dc352907fc980b868725387e982202fd0dbd27f069581efbdf599ce98b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982096c31c1868af7be99d17394de74866", "guid": "bfdfe7dc352907fc980b868725387e98b9496dd9dc20176707d857e97d2220a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e16651ffda86aaa1a5c9c18043053284", "guid": "bfdfe7dc352907fc980b868725387e98d4c80c22c5b2b38768aa54cb7610d73b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860ba0c9fc190c8ad3e39902399f8e8e8", "guid": "bfdfe7dc352907fc980b868725387e98745d7e3885ad9aaf33d8b5a45747ab15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be998f0d26c40007cc1e4da993b8fac7", "guid": "bfdfe7dc352907fc980b868725387e98a03fc6c2cc68aef0ef0cc7ab70cfd6cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5da156c06088b6bdf2476e6dc6f5c28", "guid": "bfdfe7dc352907fc980b868725387e98e6f93469d3580d66425ac072afb7b56c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984904f82bb88000acad2529c47b404be0", "guid": "bfdfe7dc352907fc980b868725387e98ca24658fc974cfe508997ebffd4e142a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98632c2c40a3eb1327bfc9a7e875d07530", "guid": "bfdfe7dc352907fc980b868725387e98c1ec18d151b104f39358fa67d381d628"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da085929075b93c799508e03c1fcb283", "guid": "bfdfe7dc352907fc980b868725387e9875e7ca2ba9c09accb63ab19b9e2a0fbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98341592636d5da0b6ef49d727ebe1cae6", "guid": "bfdfe7dc352907fc980b868725387e98facdac075139ee1cae64c5e948aaad09"}], "guid": "bfdfe7dc352907fc980b868725387e988cb27503ffdc2f91e7de2b25e564cecc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1b96f727184f819fafd78f8ff8588be", "guid": "bfdfe7dc352907fc980b868725387e989899c225b1e95c80d37f5f6c7b13cdaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98711a3d90085d3f9951dbe59123db040e", "guid": "bfdfe7dc352907fc980b868725387e98ff4c1187475432c692652b2da8d8789f"}], "guid": "bfdfe7dc352907fc980b868725387e985104ef45d158483fad52a0c17dd9655e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e02ff847476726c8cd121a569059e889", "targetReference": "bfdfe7dc352907fc980b868725387e9821a12af8510f5a5a4842f587aedc172a"}], "guid": "bfdfe7dc352907fc980b868725387e98503e64b9a721add3dd51255616a953de", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9821a12af8510f5a5a4842f587aedc172a", "name": "PurchasesHybridCommon-PurchasesHybridCommon"}, {"guid": "bfdfe7dc352907fc980b868725387e9833260f06b6ffe5cdf6831b2907a4b8de", "name": "RevenueCat"}], "guid": "bfdfe7dc352907fc980b868725387e98b0f638c99ac593829a518d6a6a45d8d0", "name": "PurchasesHybridCommon", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988760891fc732fcd8db57a04e95d983d2", "name": "PurchasesHybridCommon.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}