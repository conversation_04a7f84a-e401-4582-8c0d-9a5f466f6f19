{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d90c0bd3eb64a582c555532c5e6475eb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bb371064bc34b1956bf7f316fe3b26b2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fe7612327bad0d70108767a23098eeeb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98890100b77a2b7d9c9b3bb122d4f61f34", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fe7612327bad0d70108767a23098eeeb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c1312a64dfbda86cb1736cecb86b3d8f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986ac8629bf4cc0befd3f3eb976a80962e", "guid": "bfdfe7dc352907fc980b868725387e9844415321cc8be8b1b69e9dc11db5cc74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef0c3952c96d03db703f832538d53e6a", "guid": "bfdfe7dc352907fc980b868725387e983198a6a43918f5e29477308f33ca6950", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b91a344f26003549d2b7542f5de9295e", "guid": "bfdfe7dc352907fc980b868725387e98f106d1aebbd4ccce7f0b7c276abd3cdb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878047612df41b9e27dfe82a73040a6a2", "guid": "bfdfe7dc352907fc980b868725387e98de7aa0715b37ab01cf001d98102b1311", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98717ea75635b95810f4cdda6d02a4a30d", "guid": "bfdfe7dc352907fc980b868725387e98b4f150c87bc60b3b6dc344e4a908a323", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98429f0351d29dbffe8110e2c60ccaebe3", "guid": "bfdfe7dc352907fc980b868725387e98c20e9b7b5756e0e1f8d24c37f2c9a1db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b75de99b8a14a55a826773e84fb6e1b", "guid": "bfdfe7dc352907fc980b868725387e9845e76efaf1a946e275e23ebfc0d3bb17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7774149981da9e909fede0624d8fb2c", "guid": "bfdfe7dc352907fc980b868725387e9893756495813181daca820e57e70cf635", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866696f07dcb1cd7d76dde2c7d7cc56c0", "guid": "bfdfe7dc352907fc980b868725387e9881d8bfa42df5a2fa547703271f584a29", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edf9912d7a215129064f712f2c25a692", "guid": "bfdfe7dc352907fc980b868725387e98bd023cb0ea7380621a9a3e72db12641a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1bd0069844151d189f240e27bc49807", "guid": "bfdfe7dc352907fc980b868725387e98e0cf415e1318b381fc5df51e9d6fb0d2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98419ca9159956c4d5416e8499140800b1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98633bb56e8829d13f68aed21f23a54c38", "guid": "bfdfe7dc352907fc980b868725387e98130ed8c1863aba1fa2c57329cdfd9315"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861c893068addfdcb3cd4c530b62257a4", "guid": "bfdfe7dc352907fc980b868725387e984c90b6347c9a1ae788c34b8df7de1adf"}], "guid": "bfdfe7dc352907fc980b868725387e98581b97c22e7f9018b13a596ab638e2f9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e98f0424d22a317954ffddbed1344627a0b"}], "guid": "bfdfe7dc352907fc980b868725387e986f71e219e8cb150b56f306c62fadb37b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98934fba7a9a7c1a6ed9b422a0d4ab5b18", "targetReference": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881"}], "guid": "bfdfe7dc352907fc980b868725387e98546def57ced7de15ab37ba67bfba6075", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881", "name": "FirebaseCoreExtension-FirebaseCoreExtension_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98311e6292af5af43c801705cd189cc184", "name": "FirebaseCoreExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}