{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9835a3f1759f9a1d33042744d4908f8f60", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98951842c1ce8d1bdf6b728bceb2275177", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988cf115aa67a0b2f500bd4eb1ce1559f1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983d8eee5861febd58aff3ea568c36720b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988cf115aa67a0b2f500bd4eb1ce1559f1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c7c77b9be51f2b2955f1c9f46ac4f328", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c841c57c11be001cfe36f7fdf093ce41", "guid": "bfdfe7dc352907fc980b868725387e98adb1207b490e85557538052656658870"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989984d76cf167eee36fbe1f57c08d2066", "guid": "bfdfe7dc352907fc980b868725387e98fbea1305c4b613a5de7c617ed9954eea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98001b83cd9e8e60624175a6994d197a84", "guid": "bfdfe7dc352907fc980b868725387e9843eccf9496f33835995ec2f3cce81173"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f66728ba8d3cf855b4de91078e039be2", "guid": "bfdfe7dc352907fc980b868725387e985a759726074f8d55d537fadc65d51949"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880281cea5ab7fb2ce5d91640700d088a", "guid": "bfdfe7dc352907fc980b868725387e98c07c16fbf9bab231a6be7576be462460"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc764fb27e8a26dc6ed0c7151fb753ce", "guid": "bfdfe7dc352907fc980b868725387e98912babf6854e4b6a633307cef95737e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987edeb4f93c668a58142aec56356e1b09", "guid": "bfdfe7dc352907fc980b868725387e98076f4eafb5ac5fb72bd2f15932fd9f80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b3953ea55e9d9b0ec1ea512295dda5d", "guid": "bfdfe7dc352907fc980b868725387e98d3cc6448891a816c9c02ad40bef4866f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad9f253763513d2fd89be80aefd36f61", "guid": "bfdfe7dc352907fc980b868725387e9863123b3ac8ec37a0595755d5029a1719"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fdf0509be5910866482c0369a19511b", "guid": "bfdfe7dc352907fc980b868725387e987c8a1fdc0b0990ec29cabde479c69f33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986624e7263bd24eb05cdb267a8266cce4", "guid": "bfdfe7dc352907fc980b868725387e98c5d228976510375f9a146bee77eed18f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d31769e324a0dc52d4b313637a94ce27", "guid": "bfdfe7dc352907fc980b868725387e985173db801afad78afafbd973d7915e5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bffa8117ab3f4bba1c13826d5dc602cb", "guid": "bfdfe7dc352907fc980b868725387e98b131eb873daf2aceec8ee6353f035978"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dab9d7b4c44fa4040cc6189925b18639", "guid": "bfdfe7dc352907fc980b868725387e98c4eedae789e326cec8c07af6345085c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820f5357f8c00e1cb72154048de57b829", "guid": "bfdfe7dc352907fc980b868725387e981cc9f9ab0e71d13f2c8b973f3bb5799b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e321259b1f70b212f0c599159bff148d", "guid": "bfdfe7dc352907fc980b868725387e98ba5d62f57539651a3439aaa04be82c0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6913d12cd7f1e36c8582af85f7dc6d2", "guid": "bfdfe7dc352907fc980b868725387e9809ac8fd2525f3f5f818e3ee1752a95a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854479b3abeeea42e432a8fa6617a0033", "guid": "bfdfe7dc352907fc980b868725387e985794388c6e4c73d662607e4f173d3f2d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855c3614d4c07b4e0613ad04ce7d16b02", "guid": "bfdfe7dc352907fc980b868725387e989da8360d51853b3f77b714ca0f0e6154"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e49e22296d5992b3f952c3cf46ace7a7", "guid": "bfdfe7dc352907fc980b868725387e987b4a3ac08a76d31fe8f98db12ec95673"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd856145ff53046b5aecfc54b3f96740", "guid": "bfdfe7dc352907fc980b868725387e989b32dc7c3835f841b9d7ce800bbda67c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da69bb0b32d3966c05079910e5e7127c", "guid": "bfdfe7dc352907fc980b868725387e98e05383d48227da4b86ecda39dd02118f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98152f2e1a214da4b7337688c86d982b6f", "guid": "bfdfe7dc352907fc980b868725387e98ea04311587af0f2e35bd8f934508b996"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a92826603cc46486912f0a6ec780db6", "guid": "bfdfe7dc352907fc980b868725387e98c5dc1c6967f28ce51240d48668ea5dcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3f2d086d48931bd47ffa18be04a4dcd", "guid": "bfdfe7dc352907fc980b868725387e98e9f764ac2c8e5354645330461cfea687"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988395138c3819baf64550b057521717a9", "guid": "bfdfe7dc352907fc980b868725387e98f36b0fd8d722b5960a1a64b2742411e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec248381cc5903cb7ce26ac0c1927732", "guid": "bfdfe7dc352907fc980b868725387e98020f727978f2dbeec111c9cc417ef2dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891af5dec457f5be9b727f7276446b151", "guid": "bfdfe7dc352907fc980b868725387e98ea1704c9d1af26730912c2e798a30fea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898692ec503aa72aac2947a7fc79c57f2", "guid": "bfdfe7dc352907fc980b868725387e986f1f3208c41d7af1a72728c3be0340dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e7519eb35c9919e2d209cc72faf7a44", "guid": "bfdfe7dc352907fc980b868725387e9824dd43dc675650b5065155a1db6ab960"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e4ad2211664cb5ca59f4cfd9bdd8489", "guid": "bfdfe7dc352907fc980b868725387e9866c1dde567aa6773be650b4ff15c212a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eeaee6bcf5b4efd719d9ad054293826c", "guid": "bfdfe7dc352907fc980b868725387e988b48a11710de7527bbb238161879a396", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e905e8d2e295658f3c34c3c2bd3a738", "guid": "bfdfe7dc352907fc980b868725387e982919fb7ab23f726a604ebca2be5201ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b200b7dbb213b9a5477cc0b25acef53f", "guid": "bfdfe7dc352907fc980b868725387e984b24e699258b48d62f66d3a699995333"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861d51b6c56fcc110aebb7cff036eb9cc", "guid": "bfdfe7dc352907fc980b868725387e98a98aa294f595d02d82565f54f2644934"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c56bfdb8ba98fa82c33dfa2a7835297", "guid": "bfdfe7dc352907fc980b868725387e98a054e0e2deb160bff4357e458072e3f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859e980672655511735f7a10f56e4e542", "guid": "bfdfe7dc352907fc980b868725387e98828d8f5f4e05f9fa032e890297792910"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0e494f5edadfe36c7450cfba794e9db", "guid": "bfdfe7dc352907fc980b868725387e9808e3ca72b6faf3719337340e31a2767e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986238ae4027b631c20d7e23978f749595", "guid": "bfdfe7dc352907fc980b868725387e98f946da2d63eba491d6af03852ea28eb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ce4a58fadf897907da0cd5bf5a53998", "guid": "bfdfe7dc352907fc980b868725387e98ec7c37b5c32316457be1f645e6aa4071"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851884ec3a20fb8760eb6286c6ad54ebd", "guid": "bfdfe7dc352907fc980b868725387e985bc8b8d4f7e6f0a99b8c2c7391e92ea0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b72abea210cc87561f5076bbe163ed31", "guid": "bfdfe7dc352907fc980b868725387e986c10769039f7d46626a34e993f527b88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1d8eda5ee646627a349a690297a7d5a", "guid": "bfdfe7dc352907fc980b868725387e985d417f662d0f488d255dbc002040ec5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e810defe0d90ba1b3c81e0684d7b6f1", "guid": "bfdfe7dc352907fc980b868725387e9826d8e513550ac00bde7c92252bc04902"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe17e94fea03aaea8fdb4eecdf8c54ce", "guid": "bfdfe7dc352907fc980b868725387e98bb8716d09e9582b204840b67839dadd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bd46c3644579e33c54a0e2758976217", "guid": "bfdfe7dc352907fc980b868725387e9806f37bbc12784f701a4c947f4e9b4c0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984959a12020612212f0dafef4a5dff171", "guid": "bfdfe7dc352907fc980b868725387e989df0fc32dac07c6091aebc420fca45d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb283c72277c94f8b9bb33426508ee1a", "guid": "bfdfe7dc352907fc980b868725387e98082ef71c5a23e54d60bfa7abf387e9f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98932c06237662af46e74b24edf2478534", "guid": "bfdfe7dc352907fc980b868725387e989f2a60a0103857059fb968db3fae2598"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98920391837ddd94640d0cdd66750781a4", "guid": "bfdfe7dc352907fc980b868725387e9864a9ba1d2ab6c718affcd5bf085574bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c0c678a59dcba9667f8f8c08f2f9cac", "guid": "bfdfe7dc352907fc980b868725387e981699f3ba24fee0a5c7bc3259817e4e5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb49db56efd366091d6c9d66b4be9d3e", "guid": "bfdfe7dc352907fc980b868725387e9864073c0dee9e2369350dbb80304599c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b05931d3c7994e6deb54868b28b7a70", "guid": "bfdfe7dc352907fc980b868725387e980a57158bd992e7527a1421727c71f030"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98048b87a9f7c0b842131df877cee2564b", "guid": "bfdfe7dc352907fc980b868725387e984d314a02535de58a841d7cb09e497c65"}], "guid": "bfdfe7dc352907fc980b868725387e9800271e4411352fc56f574d2d78d4d70a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98270f7840751facb0781cc373da0e1faa", "guid": "bfdfe7dc352907fc980b868725387e98e7eccb005b72c716614a0523c6fcc82b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98684122d998b23dc1d087581436e77b97", "guid": "bfdfe7dc352907fc980b868725387e98711f685a432d51899e5b8d7f6d638167"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f8f6ff361db63743967c1a539ba846d", "guid": "bfdfe7dc352907fc980b868725387e98403140d073a0eb73ecafadb6116c5f27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1f25afd5d51bbe52b01c58bd07c0f99", "guid": "bfdfe7dc352907fc980b868725387e98cdaab979d0280cf2c249016f110da148"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a287d0d207ae873ff4792c11fa2bbd1", "guid": "bfdfe7dc352907fc980b868725387e98cdd3213c4eadf6e340a21f613e91504b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98883a74a4b41bb1c49a471122cfaa091f", "guid": "bfdfe7dc352907fc980b868725387e98a30b74b20859d911579aff8dc45d50c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842f5cf4765780e1fa094cbc74c600d19", "guid": "bfdfe7dc352907fc980b868725387e98e1211ee47ec9cc12cb7442454a4c76cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf82da153c4d7f3609d0e5b3a991e5cb", "guid": "bfdfe7dc352907fc980b868725387e98cb2be68851540aa44bd8dfd88aecf7f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886d6de0696ae8d3ffd87749f16572e8b", "guid": "bfdfe7dc352907fc980b868725387e988a515e8ea275bc9476e76af81b2500da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989051a2883cb967ef5651b846ab3bab36", "guid": "bfdfe7dc352907fc980b868725387e9802c6030caf2a344f08f4fad6252243c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886202c8ce620dfc50496064082a4b411", "guid": "bfdfe7dc352907fc980b868725387e980bfcbac13bf66226a8d7e185dca9baf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876011062e193c963764901b4adaf5a87", "guid": "bfdfe7dc352907fc980b868725387e98876329c341355568579a93ea47d65dfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2002d10709db51476638ce87ae7dede", "guid": "bfdfe7dc352907fc980b868725387e983d6ea7d0197361aa9c062541f5da7f64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876ce47a5a523a08ac479827e8876b27e", "guid": "bfdfe7dc352907fc980b868725387e982b57bc41f3e2c8b453c3a1f262fca229"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0f0ce3b61060f61eff1ea15b2ef70a9", "guid": "bfdfe7dc352907fc980b868725387e9834d9c6d25bbd41a413a16fd3a1515d61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8c583f5ffd33d47ad9010845708eb62", "guid": "bfdfe7dc352907fc980b868725387e98de8144cb5d182a6a23ef1ad9b0f831ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f321bc81eee1fe1a4c30b2895985cc8a", "guid": "bfdfe7dc352907fc980b868725387e98291bd54aa24d0a393d75bba69fc1287d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810a4567ce53fa9dc0f3f7a68a496a98a", "guid": "bfdfe7dc352907fc980b868725387e98c329c595769526204a190d507999edaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b89ec7c84d0a276cbd246238b994691", "guid": "bfdfe7dc352907fc980b868725387e985b397b529b3b61e45719d0c5412429ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edec78f4f54ca4d0ef0ae8ccf5385b91", "guid": "bfdfe7dc352907fc980b868725387e98d898858457c777300a0ed4fcea630554"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c368c50f8727c2047617edddab970822", "guid": "bfdfe7dc352907fc980b868725387e9809a86017470bf01a662dd8b6c4c0f920"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e08e4d477f91801005e1a605087158b", "guid": "bfdfe7dc352907fc980b868725387e98310f66d14ad90a5ac46ff8701371720d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6208edd9b1afc21e73bb73d2e24ae82", "guid": "bfdfe7dc352907fc980b868725387e98cf5e89610f4dcc2a0b498071a6e82194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec60baa823602e4d5266fbd56ea4d8fb", "guid": "bfdfe7dc352907fc980b868725387e98c1d2c34638bd43aa6812a1bfbfa61d14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c6e3d6b07f8103d2c4b81a1ce7b1e31", "guid": "bfdfe7dc352907fc980b868725387e988b23f5fe8ba89d53c42107087daa571d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820e6d03b07bcffc7207b017b182cc90f", "guid": "bfdfe7dc352907fc980b868725387e98b47d272d09bb2e24d3b3101fd2b5e784"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9cd449165395acd5f512287d343a027", "guid": "bfdfe7dc352907fc980b868725387e98da0b18a1e9877ce3fbebe4110a7a088d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fed1b512d359856bfe777426292ae7b3", "guid": "bfdfe7dc352907fc980b868725387e983125ddcf04a782f468a73db894869e3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874548144e674af292ddfe7c81a429ffb", "guid": "bfdfe7dc352907fc980b868725387e98cf7fb18d5f216e26c868c4dc72979f6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e939cf9147e3d52833d85745dd550bd", "guid": "bfdfe7dc352907fc980b868725387e98bc967f9e6de821b70e7041a58132d4b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddd0914f01d2da13a3175421bcc1d27f", "guid": "bfdfe7dc352907fc980b868725387e9816a4f48b0820cffb3f33c6ed5dc4dd22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983097adf2ca85e0f23b7386fe39be6bcb", "guid": "bfdfe7dc352907fc980b868725387e981a446a128e1c38f4753560ae8133297e"}], "guid": "bfdfe7dc352907fc980b868725387e982314a7c004d4b7b7e97546ae9f1b05f2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e984d33bdaca5ca7a44c66e7be602d81a13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872cd8e1769ca68c3427177e351ede340", "guid": "bfdfe7dc352907fc980b868725387e9886704f91bf9b2572ffc68664e7ad49ef"}], "guid": "bfdfe7dc352907fc980b868725387e984dd46f9c032e29ff603b3b8aa82b4119", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e986e78a136b11faeb6d2a33addfcef8299", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e984dafc892cdcfe2fa3542dbd034b5553d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}