{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b03b70b83dc937b94794b792b3b506dd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dfc326a25d997390438ea02c8b3a7963", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98939122236fe3301198a9d34add06dd8b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98448ba9ffed0d6d5a42d55bce3953eebe", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98939122236fe3301198a9d34add06dd8b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98479d63c4de2a865229c7c51e5b651525", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9816ce434431cf796c43c7a19b6fcc519c", "guid": "bfdfe7dc352907fc980b868725387e982db313a53646d01bcaf552d2782cd9c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a92151db8f41302cefe6b46591de0505", "guid": "bfdfe7dc352907fc980b868725387e987e26a14fa5070f36539aac86b6fc0af3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9d105e67857e3246a664e7104a00873", "guid": "bfdfe7dc352907fc980b868725387e98d762aac6e3331fe118a252bb54d9ae1d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a7de97d918f78255e1b62ae490c699b", "guid": "bfdfe7dc352907fc980b868725387e9875477b85a80ac5f7af198d80349ff922", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987700a04210e083cec4efb0447d83aec6", "guid": "bfdfe7dc352907fc980b868725387e98c62ef4b7f1e0900d9f5e27450d4890a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbd4022c49a7cd9cae14eb96e6bbabfc", "guid": "bfdfe7dc352907fc980b868725387e986bed94f4594356bcea850ce1bc4d672d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98441ad002dce9d1359d7cf86d67b5234b", "guid": "bfdfe7dc352907fc980b868725387e986adeb8c44e0f0d42e0282acac1945671", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5cf7339d8401b71c0df7f9b42593c2b", "guid": "bfdfe7dc352907fc980b868725387e98e331be014372e9cbc259916349aad0ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fc001d39dbfd4843fea0dc47256b859", "guid": "bfdfe7dc352907fc980b868725387e98a4c659a8c89ad3ce536014e8cb9476c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98055536d877a3fff437a13c4f3a4409da", "guid": "bfdfe7dc352907fc980b868725387e986cdb91e1aee3ca2a71b9510d126a238b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf801d1c5b64e8bbc5501e6dcd0b4fdf", "guid": "bfdfe7dc352907fc980b868725387e985eae0c9d0e81b3738dd3e6dd812c8a45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98597d7b8f688340bc7e7e1235ac56eba4", "guid": "bfdfe7dc352907fc980b868725387e98aeb9c13405539f3b1ad5a9610b2eace0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98486766e8ffadebd27f4b0be299a0be96", "guid": "bfdfe7dc352907fc980b868725387e983bf07a384b17235759731053f3ccea4e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c71cb9685fdec3e4bb7b86296718392", "guid": "bfdfe7dc352907fc980b868725387e9816a171f8427cc1522e116f7142757aa7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bccdba4461828230cf134d8143d82282", "guid": "bfdfe7dc352907fc980b868725387e981e672b4985cd1858362083dcaa227155", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e9ad774bf98128323dbb8bd7343bc2e", "guid": "bfdfe7dc352907fc980b868725387e98e1a6f1d6cc917680af25abedab9e6429", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98deb8bcb51db7c658090a978edab4c7e6", "guid": "bfdfe7dc352907fc980b868725387e98313dece40f474dce8a0801b42e948730", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec33898e507fb1b52c33957d75b2119d", "guid": "bfdfe7dc352907fc980b868725387e98198754ffd3193b818bf508d5b7d0a187", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98023674ce7d099be9f8259df218f6aa1f", "guid": "bfdfe7dc352907fc980b868725387e982906960eef53bed651e6fdd8528d3f64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857cb6c30a030d819b3191c4681bf1da2", "guid": "bfdfe7dc352907fc980b868725387e98c1f49ed34d7ce8950cb5be01d76b2aaf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98452d5c8cd76eba557f9cd2c7f90dbd93", "guid": "bfdfe7dc352907fc980b868725387e9838accfe42c615ede66ef0f74f9d52ebe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8d94163496edca84f3edc57907adc05", "guid": "bfdfe7dc352907fc980b868725387e98a7a12e42baa9af3882a67445f09c0827", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b26564c95d59fe8f07ddaa1ccaff04c0", "guid": "bfdfe7dc352907fc980b868725387e98de9c66c1ae61bd102949af0e02146335", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c67df5548ac0279dd2e031adc6cbf5c", "guid": "bfdfe7dc352907fc980b868725387e98667ea95e04c3ea337fb76be60a3ea127", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c4e793fc45d48860f522f9964089edc", "guid": "bfdfe7dc352907fc980b868725387e9892e48d1d8274ff751b7ce80485dabf9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ea091586689e17526b9c987f4119404", "guid": "bfdfe7dc352907fc980b868725387e98036f86ddfb03c384a3b8476ff71a4bb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d3dbcd43cc591b539fb3a8506daebca", "guid": "bfdfe7dc352907fc980b868725387e987b00dceaa88d764a51ec2a6fd9668be9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fd8569f37f56e499ac40f29a64684b4", "guid": "bfdfe7dc352907fc980b868725387e98afa80e01fea00591cfaa5e8e9a9e381f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dc83dac5a1fec48b2c5cf8c627e1bd6", "guid": "bfdfe7dc352907fc980b868725387e98afc73ee5fb1e56caf122827eaba52b15", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981740971494360fe5b81dd8796ebe38f9", "guid": "bfdfe7dc352907fc980b868725387e98ec3185d3a0d692265e362adc8730d085", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98520e6cd4ad2b3e417389215c96ddaee9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986c658e8d109c2af00d99c5d922f9d740", "guid": "bfdfe7dc352907fc980b868725387e9874757888f8f2451e34ced03302925282"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872bc3641493d89d3658375a4c3f86a78", "guid": "bfdfe7dc352907fc980b868725387e98814312b631a986e2238f17b1073063ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e7361c3becf70d499abc11295e7174f", "guid": "bfdfe7dc352907fc980b868725387e98749fe4818eb25623f23ec2a3eeffe7a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f7536fc38aadf739e3b49c2ebc2615a", "guid": "bfdfe7dc352907fc980b868725387e982d05796d3adb37b613028bf30d346271"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fdc0765066ce9f378e10405d6e0ead8", "guid": "bfdfe7dc352907fc980b868725387e9833eff555e78fa32df3f7021eb9b6bdd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981522d21ebf40bc15b295571975ea75f0", "guid": "bfdfe7dc352907fc980b868725387e9869d3ca1c7ef59c110ed7f7065148b86a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a436766d87f614e85f2aa094acaff098", "guid": "bfdfe7dc352907fc980b868725387e98fb09b6b09bcd852760bc9d594dd88b84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee0dbcd11fb53d5e0a96bc3e6877950", "guid": "bfdfe7dc352907fc980b868725387e98281dd353d1d11185ba7e3d8fa234d367"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981771343034748ab918b7ac912b54f670", "guid": "bfdfe7dc352907fc980b868725387e98890e081dbe7c029aa301e72aa7747870"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9d54c9a409643dcf779a3660d35beb1", "guid": "bfdfe7dc352907fc980b868725387e98e3d06297fd117f6e4510134a66bb4071"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833747138b34981ada0d197cc5f588fa0", "guid": "bfdfe7dc352907fc980b868725387e988836dbc4521e84f04bf3e4d05f2bc4e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98550c12f96c1c38e0e348e221706230d0", "guid": "bfdfe7dc352907fc980b868725387e98eb8955b423a95fd316149b0e15a5ae90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98009e93f1ad54043a07dfd6ed0e739a8c", "guid": "bfdfe7dc352907fc980b868725387e9889f82941a978596f12fe4841d9cbeb8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e179f5d6531dbb0e5371fb799b2dddbb", "guid": "bfdfe7dc352907fc980b868725387e985d78d67e3f5862adeba3cf54640a3999"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802ee2e04cbc42e22e45aa3cc42c53b7b", "guid": "bfdfe7dc352907fc980b868725387e98e3f7493f32855947dd63249fda6333bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b58188c32adf9bd15d3fb2e99f929aa", "guid": "bfdfe7dc352907fc980b868725387e98b56b305948d6cc6555d6797afced13ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecc818a83903fba52b55e5682483ecb7", "guid": "bfdfe7dc352907fc980b868725387e98e2a433524d344e894049cfe0b47da0fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f58699d4e57df99f3ec60b1b93004e2b", "guid": "bfdfe7dc352907fc980b868725387e98c44e56d96c6864e3b4a747a1b030fe7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c1c917c307314038feed227c4bf702b", "guid": "bfdfe7dc352907fc980b868725387e98264e1fa4a0fe72dcc0ee2670a5baeb9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983769e68900a75853bd200a3902ba220b", "guid": "bfdfe7dc352907fc980b868725387e9829e59d2cdcf9663763d86f81f770de2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98211d2526c8cd7b88a3c32d9b5e187508", "guid": "bfdfe7dc352907fc980b868725387e980d3afd7108a2654878b7b7d6869f8dee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872a09f81434a3a320ebca161878ea3cc", "guid": "bfdfe7dc352907fc980b868725387e98959ef7e123b65b434b4c762f25eed1e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986395c68cc5a61c4e18002157914ca11e", "guid": "bfdfe7dc352907fc980b868725387e986a6eae9e092f95798e0f1f5b4ab6f7cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a71eca599ed980787cb1501eb35a5bb", "guid": "bfdfe7dc352907fc980b868725387e9840bb62465f322b264e4e5be13417c638"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98195da9b5ca49a30170e61068bade110f", "guid": "bfdfe7dc352907fc980b868725387e982ab52058bf9c9aaec16d4510c5a23dc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892242ab7ea44a4ad87149bc104574abf", "guid": "bfdfe7dc352907fc980b868725387e989e26fec7fa334c261957a9f5c761ad0d"}], "guid": "bfdfe7dc352907fc980b868725387e984300ad7ccb2d37091c2cb08f4cb407e7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e985ab50c77a6f2f2422af1628cd2008fa5"}], "guid": "bfdfe7dc352907fc980b868725387e98a07d12a09f4b8bfe4f875c66b2450a63", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e094b2caccfac2210b4f05c65c653f7f", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98943337216794387f92e3fc9a279362a5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}