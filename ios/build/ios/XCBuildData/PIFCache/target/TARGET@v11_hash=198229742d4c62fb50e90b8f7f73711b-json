{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986de3af93d6d1b41f66ad421f6396e74c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e8041e1d8805eb753770cfd964da5ce5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9826c2fdb1421882de331c7dcb7541702c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981404099c057d7e30d5a9ebe54c95e278", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9826c2fdb1421882de331c7dcb7541702c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989238db53389c73dd3312d298d91bb451", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9853e6e1de2807c24e2a756657d22f8e95", "guid": "bfdfe7dc352907fc980b868725387e9849fff220b284191070c33b01b61b106b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0e2c1ff59a221e41c8e7ae9fa4c85a4", "guid": "bfdfe7dc352907fc980b868725387e98e0dfe997b446cb3e1214cff0d13cd29b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bb417b1a97dc09872eb36e784031687", "guid": "bfdfe7dc352907fc980b868725387e98dc7fd31099966876e6c8c0b6b05f702c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863b04cb9a5003d29a04a15ed87fc045e", "guid": "bfdfe7dc352907fc980b868725387e9882a69edc8adc785989630df6ebc38ee9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2a193f61aa317854cd1e7964869e1ac", "guid": "bfdfe7dc352907fc980b868725387e9870ac8a8a77b6784888eff833b4ff1603", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb7e9e8b3c9b48114a903555e946c1b3", "guid": "bfdfe7dc352907fc980b868725387e98b1fad353c9da5bd1d2601c8ebeb23e7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4e2852f3222cbe88e07905adf1691cb", "guid": "bfdfe7dc352907fc980b868725387e98ea99c6fb66b13878f4e927745cf37a61", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850d67dfbad2945cc0689ef3fbc7e2fea", "guid": "bfdfe7dc352907fc980b868725387e980032b7845340dcbdcd150e86b2feedf4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859994e165e0f48f3356a5d12b8cc683e", "guid": "bfdfe7dc352907fc980b868725387e9869849a9c1285caadf533affcfc0b488a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984558feee19b13345fea22bd069c3cdd8", "guid": "bfdfe7dc352907fc980b868725387e988aba6bcb80c79f3909a69fc5468370e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7849c9f24968f261d2f881e756a1e3b", "guid": "bfdfe7dc352907fc980b868725387e9823c57df8ee69069b60a14375b40fa4ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da970c664ca0b91b28133a1027964a89", "guid": "bfdfe7dc352907fc980b868725387e98268cbdfe480c56e60ed44412f21251ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cccc644fb6b856e7557d9245627a33f2", "guid": "bfdfe7dc352907fc980b868725387e9835f8aa0d50bcac95e02878dd3c626e0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826d7b33011ec1e00f5a3fbd65fb51989", "guid": "bfdfe7dc352907fc980b868725387e98e0553d9650d1f3876dd00d67b1e46158", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e732dc0c1d4a98fda4114f82e624cd5", "guid": "bfdfe7dc352907fc980b868725387e98a260b784bb9939de7b30fb6987fb0526"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f3e561d659e0d8f5e2743eb5ed8cfde", "guid": "bfdfe7dc352907fc980b868725387e988474ee9535b9aafabaf979057292e218", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836aac6e6d88667e06a354ac60506ae33", "guid": "bfdfe7dc352907fc980b868725387e984af00ee3e9119ffda715f4c529a193d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b4df061b29dfc5151f206778acc5579", "guid": "bfdfe7dc352907fc980b868725387e98457622e4eb2227e76f8cdcb683c1ee8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba1a2f11f9aff6a61756f862b47cc205", "guid": "bfdfe7dc352907fc980b868725387e98d44475d08d296303e2f74e65f92758ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896696f8a6909acfa19c1ed270e64f72e", "guid": "bfdfe7dc352907fc980b868725387e987ea23385e041a7c4a94db57a642f37c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a509651f3a5794ac030048e76c5e2a5d", "guid": "bfdfe7dc352907fc980b868725387e9831c1447da7e13c880fdbd06095b1c9f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcca6b4f826c16d3fbb3e7b4b60a92ec", "guid": "bfdfe7dc352907fc980b868725387e98a72c8b1a9decd5ae92decd552115e783"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb351538b321a6f487e4019c830ffef7", "guid": "bfdfe7dc352907fc980b868725387e9842bf2e7e3362029ce36bca683440a92c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd2a1c80be052a0fdcca3feb01761a6a", "guid": "bfdfe7dc352907fc980b868725387e987ae83cce50c6c0ffa864e2d651ec0034", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8d03474f9a0725001bf0f7c702eb10f", "guid": "bfdfe7dc352907fc980b868725387e9822358c6e5468676688417715a4fafc68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cb96264dba11c3e08ad772faebe5d4d", "guid": "bfdfe7dc352907fc980b868725387e9879be1acecfa9ff17a926cf67b84be7b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e65e8ae8548c3d6ca24705cf737a5c6e", "guid": "bfdfe7dc352907fc980b868725387e9863b3154825fa7acfa93b79f65f26bcce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a29bd4d971984d5e39616e49f2d6187", "guid": "bfdfe7dc352907fc980b868725387e98f3b7e55455fe093febf19e8fe2fe6f37"}], "guid": "bfdfe7dc352907fc980b868725387e98f72bd838107b828d2bd52a5d564b16a3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98603468cffd2b5a76ff6d8c79bb3b0309", "guid": "bfdfe7dc352907fc980b868725387e9895445f8ee3240609f1651f4b8b23e9f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db12d4bc4dc4d06681b603b8336dab56", "guid": "bfdfe7dc352907fc980b868725387e98dc7b2ce5b16a5e09adcafa4ac4e9352a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e74ab73328f3527890eae2fa20310d9f", "guid": "bfdfe7dc352907fc980b868725387e98c4076d38f15364a0850bdc991e8bb3a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d4018474fe8e2deebf74efcecfc896d", "guid": "bfdfe7dc352907fc980b868725387e9810535acaaa8c60b417d0aa74875f259a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d15a2dc049399479bede27207ac3c4fe", "guid": "bfdfe7dc352907fc980b868725387e98d9d50003db5aacb36179e10a7204410a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98547517252cc2c1159ff29a63f3ef8d96", "guid": "bfdfe7dc352907fc980b868725387e986acb192710ad6c367ad970609e1c854a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d6aa70fd97388643a232435c1e671e5", "guid": "bfdfe7dc352907fc980b868725387e983575fe1880dbec30e6aaf9a8e9b71477"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987467dbd9e744ec5a3cc0d43f49633edd", "guid": "bfdfe7dc352907fc980b868725387e9837155a5625a5d8eb93a33af5710f3c2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6bdad9cfd24c9b25d19d007f01dba68", "guid": "bfdfe7dc352907fc980b868725387e98b0c08b24d47a3e6a24fe01d4d3102beb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989988f03b8423b1c6da9b3d9769948069", "guid": "bfdfe7dc352907fc980b868725387e98b8c7d711029986183752097a71a47f32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e1b6c0e69ea50c109ca69d43f736dc9", "guid": "bfdfe7dc352907fc980b868725387e98eb3703fe7c284a2c34eeb347723b861c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e793f7233d69d42bfef006c7fbe24a3a", "guid": "bfdfe7dc352907fc980b868725387e98c2d1e2bb65fce1c6de83a02514ad838c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3d5fe078504fad28deca1fbb7718b9f", "guid": "bfdfe7dc352907fc980b868725387e98b23b70c3b2b33ff8ce9819e8b9dd2d99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c61896659499a7168d51b69f7e7134fa", "guid": "bfdfe7dc352907fc980b868725387e98836a20673e11c62045baa674801033c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcb60ea03dbbb1dbf7599b022d41aa6d", "guid": "bfdfe7dc352907fc980b868725387e987b88b7f053b1a00f0ccb5947cc8e5d8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ea025564c321b87388b34e239c61282", "guid": "bfdfe7dc352907fc980b868725387e98ec9486e23ed475fbcb399f289168bcd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98322facd67e4d7578794d49b1e0623391", "guid": "bfdfe7dc352907fc980b868725387e9859f5461ccf87166741b9b8dcb221c712"}], "guid": "bfdfe7dc352907fc980b868725387e9899692e3289d93a24c6a7ec9134ba3e2c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e982200717cd3ded9ff937babb0e17d828e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98756a5481f49628fbd0c3cc300230734d", "guid": "bfdfe7dc352907fc980b868725387e9872bdf0b81aae0d24af42f47573f2d894"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872cd8e1769ca68c3427177e351ede340", "guid": "bfdfe7dc352907fc980b868725387e98d357ec1c2aba43796843c9570a806867"}], "guid": "bfdfe7dc352907fc980b868725387e98651949b3266aa718548e83c23bfc0b2e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98db28376ee5dd1ec8f65490748f632b25", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98ccc8ab59ddaf5e7d9b8634cbf76f37ab", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}