{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980187380ecee4ac1731f165a9b0555736", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986c9276f21418e6b508f9b908e9189fd0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ae8979d8e488c3cef4f8ff472b5cc6fb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c88a43ca539a0a79b512337594e8b9b7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ae8979d8e488c3cef4f8ff472b5cc6fb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9863c8302f981c0253fe6df4c241599e03", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fefe71c78c47a09a2f3d3e69234f47dc", "guid": "bfdfe7dc352907fc980b868725387e98f962b1602d2b7ddc48d723722c3b8907"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985603d49c1f8878084a6d431ff550bdf6", "guid": "bfdfe7dc352907fc980b868725387e98843d27941ce662bdf8a8b54cb2701eb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98782bfccd840aebb3334072efdc3877f2", "guid": "bfdfe7dc352907fc980b868725387e98c7015bca50c36922341a3aaa39e9752b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986afc1b67d574f0e50e65b96086cb4bb4", "guid": "bfdfe7dc352907fc980b868725387e98f78d5497da3b7d785b557fd5d85e4c29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9cd7482d9e0e407e2c426f6da41d7f9", "guid": "bfdfe7dc352907fc980b868725387e98c6c2e96a363a7b1d1ab6803473fb2571"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd3a2e751de9f9edd1b7870658f9df5d", "guid": "bfdfe7dc352907fc980b868725387e98103366dc03fc6ed9abc29faee72f079c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834ee5666483fc5ce2d44f5c4cb1da15c", "guid": "bfdfe7dc352907fc980b868725387e98dea8036aeec024a158d2ec12a5f27095"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981db7b45397561faf10a775868c1681f6", "guid": "bfdfe7dc352907fc980b868725387e98861c296e5cf9934b940996d095cd8b47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98428a1770ca88510a58ec6634cba47e3d", "guid": "bfdfe7dc352907fc980b868725387e98cb22db1eab4f31eb82d5fb9d9f01b41d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98562ce8929f8da132ad84542c5c115674", "guid": "bfdfe7dc352907fc980b868725387e98d63afe21107603e25ca1f25e5ca9418f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed7bc4df8d52c487a6dabff173a6d440", "guid": "bfdfe7dc352907fc980b868725387e98f2ebf96e509a6b797100e8ac5c6576a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98073cd45ba06bf0cbc767dd7d23b334b7", "guid": "bfdfe7dc352907fc980b868725387e9895e9702175a57dc8e565dcad6fae37bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a0ca83fdd9597afbee80f7ca6e159f7", "guid": "bfdfe7dc352907fc980b868725387e986ddc676d537d6d70504c3c890fe5fddd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4b68d5029fba7c3cb92e35b6bf57c67", "guid": "bfdfe7dc352907fc980b868725387e98fdb2043007e7c149502c0c1d3abce5d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3a27ee5c92c222c37e1322df63ea4c3", "guid": "bfdfe7dc352907fc980b868725387e985560b96d04869a226c01026d642d6089"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98788738f1c3cb90ed8f70e3c378bf4eeb", "guid": "bfdfe7dc352907fc980b868725387e98d47b6ce3fc8ee947ae10ba2ddbbdb627"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7d460b515514b71346c86e16cde577a", "guid": "bfdfe7dc352907fc980b868725387e988da065003969941ab661b76e318125aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3e25c15088be852c2a86deec1ffe58d", "guid": "bfdfe7dc352907fc980b868725387e98e3fb7ac87d486f5c10036deaabdaec24", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e98aca6dcbdde007f2bee091be4df424", "guid": "bfdfe7dc352907fc980b868725387e98093d64359ba7f970451adb4df087074b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e97d03efe004bb70fd44aae48f4573d", "guid": "bfdfe7dc352907fc980b868725387e98adf2768cc6334ed8816b73c93d92cf45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988264c18490714e6341a3e46cc5261a15", "guid": "bfdfe7dc352907fc980b868725387e98f10aeaa80ffa84fd1e421ef43b0b9494"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98625f16082c584086309433b7c26ef0cb", "guid": "bfdfe7dc352907fc980b868725387e986e61f50b04b7b38120b6518f9bcf6c18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f14d2bc3ebf59937314374be21f46c4", "guid": "bfdfe7dc352907fc980b868725387e98e042f4a03623c6a5069741a8de4c63e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afcf4f5569eb23364852651660db3cd9", "guid": "bfdfe7dc352907fc980b868725387e98db8a5277e520b009b63d21eaa985eed3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98914b7cda8ff9e31e6c9c6df15c5377c0", "guid": "bfdfe7dc352907fc980b868725387e9811ffb1acda0b11bf7032c5bab1deb206"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0f29f0d0ff588addedcdc9a1c2cdb9b", "guid": "bfdfe7dc352907fc980b868725387e988e190a775c96350b68a4d065fba7bd82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d122661bc9b3ec5abbba905c53119180", "guid": "bfdfe7dc352907fc980b868725387e986806f1bc0af99912554837d8ccf263b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fdc798b260aa56c211fd6fde8ed6821", "guid": "bfdfe7dc352907fc980b868725387e986731a7ab5802a7ff34afb0126a40d19c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839d323ad00959b0917eb5451970bb0cf", "guid": "bfdfe7dc352907fc980b868725387e980bf6762102fd69302023622a1a0fcf81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7c0ea9de8b1a407a10973f58819a752", "guid": "bfdfe7dc352907fc980b868725387e98becb3ad3faca940f0f9cd63d3c23e06d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c4f6a6921a7f91a33e7c8143c4844e2", "guid": "bfdfe7dc352907fc980b868725387e98750ebdce8e7f55c010d21b02545735b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d40cabcfd227c80d56102d1b2b9bf6cb", "guid": "bfdfe7dc352907fc980b868725387e98a5eaea17e36a93bb255e21b1534a1bc4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a54accf91b276ea6673efc00d756026", "guid": "bfdfe7dc352907fc980b868725387e98d2ef0fd380f1a1cdb318c08727017761"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ec9cc2514991f1ed5cde13a9f9e89f0", "guid": "bfdfe7dc352907fc980b868725387e98394754f9dcbf8fff0d0644562d059748"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825e985511a0e29f6d37a50ea2b7b7494", "guid": "bfdfe7dc352907fc980b868725387e9893f410e733a11f54f1e40e9ddba03696"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989869d830beacc6adb4cabc9718010010", "guid": "bfdfe7dc352907fc980b868725387e98dee708b4381241eb60eb94eec319729c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98714df34edfde53387a2761f8f138aa4d", "guid": "bfdfe7dc352907fc980b868725387e98e3f7b42c2877531b34a4651a531a55b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98315260a60d609af4d35bbd7102271c8d", "guid": "bfdfe7dc352907fc980b868725387e98aa0ff70de7247bea9e065388c082bfea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985eacb4a28b2d0f3341e205c54852c8a6", "guid": "bfdfe7dc352907fc980b868725387e9883b3ede9bb4dd9c5918042dc59cd6ae3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988186dac4d166cb2f5179f46ec0cd5a9e", "guid": "bfdfe7dc352907fc980b868725387e98b08025346ac2587dffe21d3262c46274"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981035a22c5a85d5f63cbe2e47b29987fd", "guid": "bfdfe7dc352907fc980b868725387e98fc1fb61cec47afc3b31974920298c9ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f3d148c152b634d5e16259d7c9d5841", "guid": "bfdfe7dc352907fc980b868725387e98aeb58e40c45c75b05d6fe3c905f62673"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98858d8c155312367031667498a74a4f9e", "guid": "bfdfe7dc352907fc980b868725387e98891f79611030b491989804329da0bdc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982342825c671955a2e2dc9ae6a8534118", "guid": "bfdfe7dc352907fc980b868725387e98a572acc4ce3dfa593a04dc0fa7bbe129"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989410cc94c4c207d56a0701c56bb87973", "guid": "bfdfe7dc352907fc980b868725387e98378f31f07edd0dda9812a3827973705a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986baaaa54e661e2c5056ac5f84e736842", "guid": "bfdfe7dc352907fc980b868725387e989de0d485b64c154e350ad4d51bfd111a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e536d0c1d4917a5742f358741ae4bf2", "guid": "bfdfe7dc352907fc980b868725387e984e19ac7066f62d39a97e118b5742bac7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830b03723d1fa158ed034b321ae0a41f8", "guid": "bfdfe7dc352907fc980b868725387e983c89b283450b7519229a434b16af227a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800602bb98769ae24bf51ad53a723e3d1", "guid": "bfdfe7dc352907fc980b868725387e98c39f0d9dc6eeb2602ccf2293c21ceb6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c98956b0251d3bccf5dec2abc5a9ef92", "guid": "bfdfe7dc352907fc980b868725387e98c9dc56f1fe3c61ff5f188531d74e85ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7edbebb61ea48b2949b56a0693459c4", "guid": "bfdfe7dc352907fc980b868725387e9826a51e20072e387700a09320108b6fe1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f18ce4abe6daefc3abfdeb5f4c405ea", "guid": "bfdfe7dc352907fc980b868725387e9858fc401d4013a9f3cc3694ccaa7c34fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861d8af7ad783e49bd3173e3912af26bd", "guid": "bfdfe7dc352907fc980b868725387e989b4731d212508b4d29aa96faec50f78e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a49a4794755659475b280d10790ae94", "guid": "bfdfe7dc352907fc980b868725387e9877182ea050a40bff351ba48a0e295157"}], "guid": "bfdfe7dc352907fc980b868725387e9809e1c684d42367c19341bea7269b8e33", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f44488274cb906fe949593b7f4480aad", "guid": "bfdfe7dc352907fc980b868725387e9837d0f57a7b31a9026098b14ea8d05b2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f292ca996e8f08e23c8a855c085293f", "guid": "bfdfe7dc352907fc980b868725387e982d0e36a632ec472aee65bff50d8ce0f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c164cc38187dc490612027493e2448a8", "guid": "bfdfe7dc352907fc980b868725387e9874fd1e9cfc035370e8efc900860ae7fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ff14dedf4fa94cd48e144431172cfde", "guid": "bfdfe7dc352907fc980b868725387e98d47948220d6f587fa320a0bfdbaf8842"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f0c8cd9f554465cf0fddc1a690f2083", "guid": "bfdfe7dc352907fc980b868725387e98fd06c688d496b4e3d977d317b2cc4127"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850fff1f67d31c09d5f6d740bd25c2e84", "guid": "bfdfe7dc352907fc980b868725387e989faf432344a9d295f3d4a176b33ec9e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f458e78780e10704eef002b5cc8fb97", "guid": "bfdfe7dc352907fc980b868725387e98c5cfd37ea220f5a3de677e95eca15f60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0c9affb451e200ea15ae130a26141fc", "guid": "bfdfe7dc352907fc980b868725387e987a00f1ae1a89d37d092b7fc542b596a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcbed338e6e6d88cd77d85e4ddb0b768", "guid": "bfdfe7dc352907fc980b868725387e98afe14c403c45d98a187495ec0919bb8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f59d612badb07219744ff9f70ccb61fd", "guid": "bfdfe7dc352907fc980b868725387e989efabc7e9c92a9d2f96601d52c93bd94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98690fc4af035af7b6bd2b4fc5af909711", "guid": "bfdfe7dc352907fc980b868725387e98a50a6f98132047e210956793eac9c88b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fac3c58b3f3a7d7c68ab230c3828d574", "guid": "bfdfe7dc352907fc980b868725387e9883a0300fc54922e37199f38e8574bcd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f971fe1037908a913787cfa708fcaac3", "guid": "bfdfe7dc352907fc980b868725387e98e54f912d3176c25a5062b54177cd2a56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98491f7bfa0363dbecb11ae75dc50ec074", "guid": "bfdfe7dc352907fc980b868725387e98fb21ae63a137c4f2c66aedcbac44a59e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980635d49e76bf3057a7ea2285b75059cd", "guid": "bfdfe7dc352907fc980b868725387e9857efb11767bc8f85ca3c3ae1b5458215"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca930358231fea3432b3046dfbfceb97", "guid": "bfdfe7dc352907fc980b868725387e98860ec399feeb4ba5a24001b623e153ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b6b5cf6765d98a29a993e825b7a938e", "guid": "bfdfe7dc352907fc980b868725387e98a835a02c81ffeb625ccf9ed2cd4f18fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cfc5a60dac7f17101369c25dfbcb001", "guid": "bfdfe7dc352907fc980b868725387e9882ae0c303ed04294010bc46ff7844b85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98242f154d1f97d480bdd3b67627a11da3", "guid": "bfdfe7dc352907fc980b868725387e9894c1f7a5c23fd4f4dd388ff5813b884a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae1324daea1fbfb26cc586be0ac087ea", "guid": "bfdfe7dc352907fc980b868725387e9878c928fe001450bb21206797da585d27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2ec8f9c096d7598a4a331d2972858fa", "guid": "bfdfe7dc352907fc980b868725387e985bc181eb1642204e9d91c768c208da2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8f0a20482c5d56decb198ebfdbca773", "guid": "bfdfe7dc352907fc980b868725387e98fb92536f1644c7fbd1f19bfadfddda3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983446dd0ba3848a53d000ae5d6e78c3ed", "guid": "bfdfe7dc352907fc980b868725387e988dd7189718cebaa49d28bed3a9767509"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b13a3ea15a33cadfaa23f678c4be7e5d", "guid": "bfdfe7dc352907fc980b868725387e982d59528c1dc48403feb1c60ef3254211"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2031db47285bc0c8f27f083362c72fa", "guid": "bfdfe7dc352907fc980b868725387e98ce5cd66f52c47557281ca2c3f26fd3f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cefe243dc58faa13a37d2f542276846", "guid": "bfdfe7dc352907fc980b868725387e988eadecce8e6c19d6943a8380d8ae179f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984355adcc6ca14c3fb00621c97e9fbb35", "guid": "bfdfe7dc352907fc980b868725387e9835620568c2c8ea6c09a4f4f7018c9fa3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98408887dd3260473e17db01468722e507", "guid": "bfdfe7dc352907fc980b868725387e989491b875e4e89ff6131222faf5a94fde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986171e0695753a18eb7c7eb0e4d01fbf4", "guid": "bfdfe7dc352907fc980b868725387e98dcb1cb303398ea8815bc245472aa6944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db6c3df94acd010614ab5b8f5c82b571", "guid": "bfdfe7dc352907fc980b868725387e9811fbe34d680869a84eeb1afb4e8f7398"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f5fbd8ca369c225abc1a29e3a10d56f", "guid": "bfdfe7dc352907fc980b868725387e98ee7f2d9c8091e53b2819521726e7ad39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9f6c1f12fbb37e13c1cdb1b93244e23", "guid": "bfdfe7dc352907fc980b868725387e987e960bb995cc3f8f75922235c3b00171"}], "guid": "bfdfe7dc352907fc980b868725387e98f7a1491cda520fe19d6fef5100499443", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1b96f727184f819fafd78f8ff8588be", "guid": "bfdfe7dc352907fc980b868725387e988bdec309eef84fad355ce934f1752620"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6e9503832ae66365cf6bea4fa1207a8", "guid": "bfdfe7dc352907fc980b868725387e98386d07a9854d24cae8602e5dfc92966b"}], "guid": "bfdfe7dc352907fc980b868725387e98119470c2d80041cb03b63f4021d1a7ba", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9882a77ddcd15063b9d69655af2c886bbf", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98541cad7d0f94cbd45f1c077878d0bfb9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}