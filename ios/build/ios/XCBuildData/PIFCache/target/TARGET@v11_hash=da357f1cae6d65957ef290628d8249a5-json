{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986ba91f33a14dbc3608be925e130dc3cf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c67ca22cbe11903ecfbd533acf11d37e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98192489a82271d4a1e0cf87bb3a5de998", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986f0be411b3b28794687d2726c9d574fb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98192489a82271d4a1e0cf87bb3a5de998", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989698e83e9bfc169888122fff67d93c6e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986ac0fde27ca64b55734574eeed97270c", "guid": "bfdfe7dc352907fc980b868725387e982d270337242c5fe2d87a174bdf223c9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a84561132f365683e4a2c1e1ff75c98", "guid": "bfdfe7dc352907fc980b868725387e983519a37638b0ddf80a65878f10c55ad2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e3ea68cad563f464444dea4a582e7bd", "guid": "bfdfe7dc352907fc980b868725387e985c733c51b3ddf2aedb4cb61e27cf6023", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981447772bbd4cbb17eace2cdcbae9d76c", "guid": "bfdfe7dc352907fc980b868725387e98ed8590e0cd6acccb62a69c901f54176f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d256551f90a07f9f414d37b368cb4fde", "guid": "bfdfe7dc352907fc980b868725387e98b1f5e0d10e55d66d1c0144e05bb08ded", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1a8a7b9b65af27d079bdb644d5d3b94", "guid": "bfdfe7dc352907fc980b868725387e98586ec907f5590f92158950890145f1dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fa228cd05939f56b0118d895d698f3b", "guid": "bfdfe7dc352907fc980b868725387e986392a82662cc21ae540b641e2263ab57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa063e4b9325b17fadb143304000606b", "guid": "bfdfe7dc352907fc980b868725387e98a60ec7fff7d2e4405fcd39bb17f296cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c82d1529c170e25c3c48319b13623c8f", "guid": "bfdfe7dc352907fc980b868725387e9805d8da40f92bf135d9c199fdaf4b93f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a8cb2b23996874fe55bb1fc061609e2", "guid": "bfdfe7dc352907fc980b868725387e98a9ab29a44360aba08b230fe513630fd8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98701169000d77f9e79fe69d519bdfa4e7", "guid": "bfdfe7dc352907fc980b868725387e98e85244b42ea137630d45f8610de52553", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982467020b9f186be48baf5b8199401fb5", "guid": "bfdfe7dc352907fc980b868725387e98c4d20434a984383d8ff35f0a917dfe23", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b4a8b26bf18adbedae8ce7927025f35", "guid": "bfdfe7dc352907fc980b868725387e98fa318994e32e72ed9add29d40721f549", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cbb95247083af3c186aaaaefa6041ff", "guid": "bfdfe7dc352907fc980b868725387e98abeb93846edfeebd71ac2428ab0f9233", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1be9168dbd9ccf5d26a9f3ba75ec5be", "guid": "bfdfe7dc352907fc980b868725387e9894c2fb5bce7fc600418cdba496ce8b0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff31496925d404d04fa8f2b31c9b00d4", "guid": "bfdfe7dc352907fc980b868725387e98f773474d015a3054bce2d48aea52188e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98100292bc127cab18187a6b6360a3e220", "guid": "bfdfe7dc352907fc980b868725387e987343ece7d01bd1ef3e8c148f132aa728", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0d2d9dcbd9f5c3fd38e68131802db3d", "guid": "bfdfe7dc352907fc980b868725387e98729864158b50e2920dd6774735250a86", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d42cfe1daeeaabdc336928ed5034863", "guid": "bfdfe7dc352907fc980b868725387e987d6997889a3efbb985732a6907794f3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a57df43a759bcd035bb0e1a1328e8998", "guid": "bfdfe7dc352907fc980b868725387e98d58d3125a8140e1abc785613daf67692", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1f1718c6165ddcf4277a94c50e188eb", "guid": "bfdfe7dc352907fc980b868725387e98c4b60fb8e68ae864e9634c5f2a0aa52e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c0d9477770e64f57e95cd7049ee8cf8", "guid": "bfdfe7dc352907fc980b868725387e987420e73e0cee7caa85a226e745ecbd47", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832ee11ad80ea29dd34cfb9db915b2e21", "guid": "bfdfe7dc352907fc980b868725387e98308059abf20a4785a763a0079b0e04df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a52c3eb8ddf48e2c92f251c1af83d200", "guid": "bfdfe7dc352907fc980b868725387e98dfc62e868259f986563bb4db1a1ed1ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9de10fd9a1d2e15d14614ce1dfd6b0f", "guid": "bfdfe7dc352907fc980b868725387e98cc995f327e93c51c25637cba0a6a1cbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804ab8c6fd93bd265f723369750a48561", "guid": "bfdfe7dc352907fc980b868725387e98261b2a43f42872edea24258c8f7017dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2a5d3d173900bef8a58683a6c8ce79d", "guid": "bfdfe7dc352907fc980b868725387e98857f47a26fba7662c206b76dd31d4b38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e8f2856c62f348b1230dd41cdd6b891", "guid": "bfdfe7dc352907fc980b868725387e986c9d4654d4e2325bc841f604dd6d715c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803e35aa0360e5751fe50593dbe690f65", "guid": "bfdfe7dc352907fc980b868725387e98061ca97d18a7c0c44d0ca05bc3b6ec74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e02a7739c37b283ce65a4ba39f797b8", "guid": "bfdfe7dc352907fc980b868725387e98c70ed942ecf5933f7aeb4be91419e29e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f1cb45652c96671311f4bc0bfc6a5c65", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9822d3baaf0353ec25814d78a4784e4a82", "guid": "bfdfe7dc352907fc980b868725387e9810c4bfb68b9ce33a618f24f7e5c252a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dafb6fe6d18e565c167a66fb4782e7fa", "guid": "bfdfe7dc352907fc980b868725387e987a5b582da8608ac604c056eb2fbeb053"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e461aefa032f23096f4359763ecd0fc3", "guid": "bfdfe7dc352907fc980b868725387e98f6ccee629d59ac3a4cc6c52ffc20f22e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98406bd38cbaa14c271bce85589fd7587a", "guid": "bfdfe7dc352907fc980b868725387e98a7ff7ae22961856fa094bf03bd1844e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98185913580d1b14859d9d3fae2baa5a0c", "guid": "bfdfe7dc352907fc980b868725387e98449cde2ca3d2d041a9e2727dccb63375"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0e3659c78d9dd788b1490a74ce784c6", "guid": "bfdfe7dc352907fc980b868725387e9896db91fdc6380cd420dfdef984daafcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984af03f367b019adeed82a326534b8dfb", "guid": "bfdfe7dc352907fc980b868725387e9896ef187c00889cc4aa2352757e75e482"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98511574799cb132cd10cd26c6409e0318", "guid": "bfdfe7dc352907fc980b868725387e98a15f6b8bd7ca6465949c145bd4c5197b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a955ef86261bd97ea57b285d6aa045f2", "guid": "bfdfe7dc352907fc980b868725387e98075b155c812003dd9cb3e796ae8ce8b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982efe357541d261ffebc23a9019cc6158", "guid": "bfdfe7dc352907fc980b868725387e9841e9e6a1606dc3b61f7eac5c84503636"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984432a97fe5407ce0f053bd22f014e7e1", "guid": "bfdfe7dc352907fc980b868725387e98bc19b45793db94031cf2907a10bee992"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b2c2465e282c46f2799f05cbb90f23a", "guid": "bfdfe7dc352907fc980b868725387e98088bebc2c9fbc14488e72c961f6672fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f590db414378ee6d000a4e39fe840f10", "guid": "bfdfe7dc352907fc980b868725387e98ef319b9e906d5facf4f15ea22f311a14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9ac0cc92c4749b2633861d16c0c0e84", "guid": "bfdfe7dc352907fc980b868725387e98a21cbbadb71a748daeaf4fa1ac9ec079"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869629230ad4754f8ccfabe9f34e72ca2", "guid": "bfdfe7dc352907fc980b868725387e98a0024d42fdc83a55e0a95f1c49855dcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98974943c1bb229c15b9283c9d0564db9d", "guid": "bfdfe7dc352907fc980b868725387e98a06dda6b6fac07e491ae160f78dc8ca5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98762a14547dc978442a4c519b58d5a1b1", "guid": "bfdfe7dc352907fc980b868725387e98592c743613fd896708f9e6ceb6d525e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985273a3260765b22b020eb054f3a62c75", "guid": "bfdfe7dc352907fc980b868725387e983cc0543d01494ba42adc79d5aea920a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f29a188bd3e8608c4316afd5c3e6c81f", "guid": "bfdfe7dc352907fc980b868725387e9822e130638166ac64156c77ff5e7ffedf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987951df3b38c4dcd37bb727f2517475aa", "guid": "bfdfe7dc352907fc980b868725387e98f8994c2ecafb3cb79c8ec56565dfdafc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bf5a10f4961d91b7000b8b96ba3bc14", "guid": "bfdfe7dc352907fc980b868725387e98931bb6bfffdf7c6e3c423f52270d574d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d78ef44aaa88165d09ff52e0f52bebcc", "guid": "bfdfe7dc352907fc980b868725387e98c2590aeb90cd0778e4fddc12d30e7e09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981715c1d33b4421e458285525c29c88d8", "guid": "bfdfe7dc352907fc980b868725387e9894e8bfa8fb58b389befe6283ffdced21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcf18074b54edd979d8aac7964e8d850", "guid": "bfdfe7dc352907fc980b868725387e98617c5a805566655fcdd5ee09802d7328"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eef9d337b7961e5dda3600aa5c073bd", "guid": "bfdfe7dc352907fc980b868725387e9866015a16648461ec627a83dda3a31d75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edca9d2fd31623f592521d8eff53c8d3", "guid": "bfdfe7dc352907fc980b868725387e98c222a7e2b710ee6778e47df4fe29afdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb824efbf97a7adc1aa8ea19160f6644", "guid": "bfdfe7dc352907fc980b868725387e98c617d87ca6ee760daa5a40e4e75129ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a20aaf372166e7444afaf315c35fc3a2", "guid": "bfdfe7dc352907fc980b868725387e98ade0bb3c46ae3b3fef9130d62913e37c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ef69f0dd1d5aa0ecb87874f8a251a56", "guid": "bfdfe7dc352907fc980b868725387e9813fc7da7d0a94e745244a5c22214fb10"}], "guid": "bfdfe7dc352907fc980b868725387e98f7ab74ea9edc2b386b695541a2992f3b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e98dad8efb0e37d60edd3f946b3d4993716"}], "guid": "bfdfe7dc352907fc980b868725387e98d8d524a2d147bf996fe69835206761c2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98034f9808c682a995dc09724932ef532e", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e982a82f73a9c80c78db8f263e49862c613", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}