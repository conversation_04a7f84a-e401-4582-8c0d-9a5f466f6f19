{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e67d860a1bd1d5865a1ba476f359d26b", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b976be5b156917388fa4476c03f102e1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f55b0919d6228d2b967688755712ee9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982dfed623f52a55346ecac344d046b5cd", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f55b0919d6228d2b967688755712ee9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984fb069f5b0ffb9ffec0fcf45a2c93284", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e7535a8a25c26f514e40fab8085d04ff", "guid": "bfdfe7dc352907fc980b868725387e98dfa4f55f8f519f787c16aaa989bd3821", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851a7ab7fa194229747d4e0ee96838d81", "guid": "bfdfe7dc352907fc980b868725387e9856828800dbeca6c2dd097a29d92dbc9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5acf30d8699bd8325c7782a981c8876", "guid": "bfdfe7dc352907fc980b868725387e98199d20167b51998d7196010f47a26e82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3e331a859015f9417a8c8527286ce17", "guid": "bfdfe7dc352907fc980b868725387e988e4272b744e19b4c4e394de37d58eb3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881c48726e547fe26387677a653acb0ea", "guid": "bfdfe7dc352907fc980b868725387e987f54253643a099fc38599b28d242988a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4fd940ef7b25ef323e96c24ffc7e0e1", "guid": "bfdfe7dc352907fc980b868725387e9805ad287379c785d60257cc5f354bc0ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fced37d69dd842398ef96988815de1e", "guid": "bfdfe7dc352907fc980b868725387e98b608957c1134b1bda472dccb604dda93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f79e4d08f81cc779918ba363b3292fd3", "guid": "bfdfe7dc352907fc980b868725387e9806bd1d95a435f67b45102d17679d4d0f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acaf64e3f58f2f59458b3e067e1a5db1", "guid": "bfdfe7dc352907fc980b868725387e989ca5e670d8f240881e8975d43fe30122", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834357eedc22046e0ba8d1e3cd57165d6", "guid": "bfdfe7dc352907fc980b868725387e98d6edd49827f58d2d509fc3dc3b0f92bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0e47d90e21d82e7f4c213b2da4feb0c", "guid": "bfdfe7dc352907fc980b868725387e98b46f85c17a02e6bf77cb5b52e0c2fb5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833c4b72fbf38c1003338bfd3f9f08fb4", "guid": "bfdfe7dc352907fc980b868725387e98e170cd6e846da0f81aa9f54531a84f57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f6c8f06c7671c6736f43d04613415ca", "guid": "bfdfe7dc352907fc980b868725387e98b892340c5de558681c46dcb2dc66ed8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d88297ba6353bdcc2ebcd1c1661143e6", "guid": "bfdfe7dc352907fc980b868725387e987ebf05eb807c38cf02d4c9ed6f62e8af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b83dd76f0561c65009c77bd13c1c6abd", "guid": "bfdfe7dc352907fc980b868725387e98b79d24c4222e817c3c49ede1822bef47", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856e5fb758bd0342c002b66f9f80ebbb1", "guid": "bfdfe7dc352907fc980b868725387e9848f07a2cc0e5f9c44757c4ce560d1322", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98717b282f15bd62978175e694997aa4d4", "guid": "bfdfe7dc352907fc980b868725387e980bb95ecb8f28d021630f4af79c28017a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db645b95fbaca4940ab741433486e4e6", "guid": "bfdfe7dc352907fc980b868725387e98ac645ea54d5c45b54d3d030116e1e4a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802bf70fea4e4990a519646fbb30bec99", "guid": "bfdfe7dc352907fc980b868725387e98cdeb61b76c1128a826be4cffefc36f87", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ee064c61526293d779fd1f3b1599148", "guid": "bfdfe7dc352907fc980b868725387e98677d0889e3468bfc2d1e98e73cab3098", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eee33b9afc1faa4d38235ba7ddc4a490", "guid": "bfdfe7dc352907fc980b868725387e98df713be7277506a4fd63a2825a207e69", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea861032ff5e1a4dc384c3b098e41a3a", "guid": "bfdfe7dc352907fc980b868725387e98f7128428d4b1b55ab92a0c69c4848242", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1a5b82db4b00299db89d1543404572c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988372a7405ef0852574a5d5ffa9a8d8f5", "guid": "bfdfe7dc352907fc980b868725387e9830b19a1e8777b678a3a39c15a1ba3a77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d2a0ac1ec3c4ad8a18b5249cdf2d411", "guid": "bfdfe7dc352907fc980b868725387e988e71b079028df1472efc93b7aecec869"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98901de475e0eac24c04cc1fbaddaee22b", "guid": "bfdfe7dc352907fc980b868725387e9831c0fe5051cd721a9965c460ceb1cd01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de1cd36270a309a7acb8ab0f5607e853", "guid": "bfdfe7dc352907fc980b868725387e985856708f73ae2de5376ae805e1beebd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846aa172b0060ff234e7923da959be409", "guid": "bfdfe7dc352907fc980b868725387e98a68aa8bcd58e965523aae155f893c997"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823332ea43cb38f1a90b3c062d65cb6a9", "guid": "bfdfe7dc352907fc980b868725387e9898404218307e783ecb5ce811c6a02d39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98321f05e7e0e4f1e54d33dae9730f23e2", "guid": "bfdfe7dc352907fc980b868725387e9864919b17ec5f6e75d9d3d827ceaca34b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e44b0345f914999f2ed9b2050ea981a0", "guid": "bfdfe7dc352907fc980b868725387e98b23bae12b6350a063d2ac3aec18921f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cf09e5123ff66101eaa54ba010ac26a", "guid": "bfdfe7dc352907fc980b868725387e98489eab99c4f6848ce507049f461dc472"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987902577afa709ab93cc04dc21bebc9b4", "guid": "bfdfe7dc352907fc980b868725387e98520921f49a7e947fa57e8d36bbc140ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984144ea529d4bae59e4b4e28187e5633c", "guid": "bfdfe7dc352907fc980b868725387e9879dcfc17ba43d99f1444f6dc9115c26c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a64bf3eb278797d1e0fbbb2cdc650957", "guid": "bfdfe7dc352907fc980b868725387e987880b178cccc92341d10c112afacf398"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823d8926a6d1833bdfb5d7fd42704957f", "guid": "bfdfe7dc352907fc980b868725387e98dd99fce8799acc31f2a0ab4337f2ca9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98014fac3ad00f5c55ef988f5cba51672c", "guid": "bfdfe7dc352907fc980b868725387e98ee514c9e19a46fe58b57968a5ed07c5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edcc7a45f51267c24096be93a2d7bb13", "guid": "bfdfe7dc352907fc980b868725387e9856edfeb578f2f58d1e8b99bbae517c69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab9e44d0b230e53b190145fd164ab08f", "guid": "bfdfe7dc352907fc980b868725387e9896b1727afb8fa7182fabfd476e6f3bf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984105301e0fc173de47c87541ae91011f", "guid": "bfdfe7dc352907fc980b868725387e98ccd8d98a4ff584e6955ed99777094a02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98690e343fac07667932c2768848b76a93", "guid": "bfdfe7dc352907fc980b868725387e981568dce7d027cc8d9282d2387110f0ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b107cf1a479c0d80c9f073197feead08", "guid": "bfdfe7dc352907fc980b868725387e989b3665bd6512371c4b04ac98cbb5589b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b109c369e3465b130d72d9c7da08b75", "guid": "bfdfe7dc352907fc980b868725387e98605ab5619a45fa94876646850caeb0fe"}], "guid": "bfdfe7dc352907fc980b868725387e98f49f561fc37fe60cfa7f0d4e8a50a29b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e9880d3c9427e9c1e457c363aaf56a4d08e"}], "guid": "bfdfe7dc352907fc980b868725387e9876bba6d78d9c11711feaf20e32f4792b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9865be8e6a7d9aa21c23fc49a25fd7e402", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e982f29a60ce5cd87d88c387b8ba02f67d6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}