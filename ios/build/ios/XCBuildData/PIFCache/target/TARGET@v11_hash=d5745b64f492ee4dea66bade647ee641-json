{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f8008a0f22fdd1c2f9efafd8f48639f0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988739fe12e2029ec33474d51e1c1de486", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9848f5875459a417054bb4215eedf3c10e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7e194b3c7a0dcadb37a08a8959714bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9848f5875459a417054bb4215eedf3c10e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad06093d4c8fed0ad01765d765863350", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980e7d2617a3c151c6f61f3d5d33e2622f", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2a1e83ce0b09ef4b5d9139d6d6559b8", "guid": "bfdfe7dc352907fc980b868725387e98293fb77e338b1bfb9685ad060d38ea5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8da4af103e5c77d88193bc80e732dde", "guid": "bfdfe7dc352907fc980b868725387e98f2ae16abaac0657eb68b17926113eb61", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98206ce04e8243f33f4b306abae42ef15e", "guid": "bfdfe7dc352907fc980b868725387e98dff3eda2446883d8db3490a596047fd9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a811ff21ed2905cc71653fa49d98951", "guid": "bfdfe7dc352907fc980b868725387e98e161b20ba3eaa64347d9260c2ee3d9f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cefcd57c1491230076c0ec0e497878af", "guid": "bfdfe7dc352907fc980b868725387e98b5f24e169dbd00bdbe76268d21b2370f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982eb67c59796ba5c7b718abfb118608f4", "guid": "bfdfe7dc352907fc980b868725387e98fb30f949234950f68161614e8c3ebd97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bc6d1263d07a0565561390968170f0d", "guid": "bfdfe7dc352907fc980b868725387e98d62d9e9ab0b8531f455ac3a25438bde1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861c57cffa76ad906d2d74ec19c12950c", "guid": "bfdfe7dc352907fc980b868725387e989dd4a4c3f3c16cc3fac7432e035d22a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b1ffa54af6930f2ab67dd0f24837fab", "guid": "bfdfe7dc352907fc980b868725387e98a886f0367f5e414b31771e5725923b6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cad4d44bd5344869ac8b4e9446678ce7", "guid": "bfdfe7dc352907fc980b868725387e98e8c7d62d48dd59728645a065cf905032", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846e5441510b488972b233c807502a10a", "guid": "bfdfe7dc352907fc980b868725387e98d2b6ab7b09e0f89d4f73c86d10bcfb79", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861a04ebb965191e874266de0f31a64e1", "guid": "bfdfe7dc352907fc980b868725387e980f5825604aa12d3ecce40857b2004333", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98713a262320742983ff6dc22156b767f3", "guid": "bfdfe7dc352907fc980b868725387e98e47f137b5b7be14d06032b4e32cda698", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981be2360ad6c7fca2616e67f6415bb859", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f139ce635bf3b4d8aaff261def91d9d", "guid": "bfdfe7dc352907fc980b868725387e98c4fe705aaf46222fa59b806281a02b21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d42d2bd7a8424b53cc4e92eb63c71b9", "guid": "bfdfe7dc352907fc980b868725387e98ccb88c548004ba09b396dad8250e82b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98487ac89e48dd405000ef47d77ea9a73a", "guid": "bfdfe7dc352907fc980b868725387e9839bb5c8d557ac0090a41308508f3a528"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fdc3d90ee0aac1c3717be7577e18b75", "guid": "bfdfe7dc352907fc980b868725387e98eb011eed3c6a99f9255f8d098d9e061c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a953bf71177a5445ae5105f444a8c0db", "guid": "bfdfe7dc352907fc980b868725387e98f5a80b78c8404f58aea1b8416f223e5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d12c916667bc8ed095773fef03aca8f2", "guid": "bfdfe7dc352907fc980b868725387e9887c4e516673fc3633c0430ba2de4f2fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b59acaee35321deeec6aae47cdeed80", "guid": "bfdfe7dc352907fc980b868725387e98c8172b423c3b96b30fa7b5f5e0604849"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863ff20d02d0dbd42563f63c55b59643f", "guid": "bfdfe7dc352907fc980b868725387e98d634fe97795e0f6c250e1bf3050a8f10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bd33c632885714ef324c693bb53aa37", "guid": "bfdfe7dc352907fc980b868725387e982159d14cc9089687f85566437a08e998"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d5cf549e2bb2025addf5ed42785e796", "guid": "bfdfe7dc352907fc980b868725387e98587c50bd9c5541e7fbf357e523aea44a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a4ac94b8c8c89d5f5a4b7afa2df1330", "guid": "bfdfe7dc352907fc980b868725387e980f6ac6eddbe53d979db536e6b338a53a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe4ff28ecdd793f77fde24f644175c50", "guid": "bfdfe7dc352907fc980b868725387e98cbb057da148a816f0e1db95928403745"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}