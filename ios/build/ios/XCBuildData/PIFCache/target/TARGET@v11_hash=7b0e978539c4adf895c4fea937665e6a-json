{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981552e64e3782bfdc5276d3ddff994678", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980eb165be605b10b61aa8061a3d915eb6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989dfd1e3188730a5b19a63ce21b82411d", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98061777b3ca2b957a7d6b1e36b48993d5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989dfd1e3188730a5b19a63ce21b82411d", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a26118e90bca14c8c649333ff0d7ebf3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c6e01b58d8ac77e830f49d5900d23213", "guid": "bfdfe7dc352907fc980b868725387e9841c73e0e549a97dabcd4477eb1bbcb4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3391b361a924b36d0c1746ab9e6d9f8", "guid": "bfdfe7dc352907fc980b868725387e981650fc48afcddf5a4c92a3d411f5482f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b555e562daf5ae467a705719b09ae64", "guid": "bfdfe7dc352907fc980b868725387e98d77fab90dce5634194332863b7e8c1ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843d48e6710f3c181dc6e5539574b1ea7", "guid": "bfdfe7dc352907fc980b868725387e98ffff54e639e9e90bf113efcb56bd7600", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988df21ac026ee54537235d76f25faa0bc", "guid": "bfdfe7dc352907fc980b868725387e983e1a0c7c17939f42c0bf79bbed64fd1f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807cfb53fe737699b8804c8cda29767ca", "guid": "bfdfe7dc352907fc980b868725387e98797fbca00c569ef4514e4eabdc4a6bbe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98768c4a8125de2aa944fc5b24dc496c3d", "guid": "bfdfe7dc352907fc980b868725387e988e17ddbb41ea8e66810711f16cb9334f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815d7f9299613c5979cc928f9394d8bfa", "guid": "bfdfe7dc352907fc980b868725387e98ad484c421c8c4ecae17caa8bd44968db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ae0ed21b4e7bf8da17532020c0ca850", "guid": "bfdfe7dc352907fc980b868725387e98d3ecf92e519c3a8251acf4dcbee77880", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823fff8f7f4863bfc2ac04f8a678bc4a1", "guid": "bfdfe7dc352907fc980b868725387e9818e494c90c7c54f4f3253c2054a53203", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846110ca812b5ae2ff798604a6e9ca02b", "guid": "bfdfe7dc352907fc980b868725387e981aab8bf7d9643c3d0b97d60b12d36c01", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4411dc1faa46d4ddb15ad5a5012ad7f", "guid": "bfdfe7dc352907fc980b868725387e98e53495320126ac928382e91889cc244d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98915694667d4f21bb1c449d26e8b26632", "guid": "bfdfe7dc352907fc980b868725387e98255590e980d017fed30ce6a73c199102", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98384c72e5853160885e9e1d21ec3410ce", "guid": "bfdfe7dc352907fc980b868725387e988ff274687a603c03e182408106f20c1e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824aca07a1beb4c848d0ea994d4753b71", "guid": "bfdfe7dc352907fc980b868725387e9822b772ed02802a491a7ab9575698ad0a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98750dba54e2c22adc86ba6f712ae624f3", "guid": "bfdfe7dc352907fc980b868725387e98a1eba4c1310aad1a1fad36664564c337", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc3b9a0ac6e82f9c207df8a272d5a09a", "guid": "bfdfe7dc352907fc980b868725387e98b2db854f7fc342ff36e138dca144dc0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880081c77c6856a9a5d23b7d8b7a7a9d6", "guid": "bfdfe7dc352907fc980b868725387e982cedfc171d5948c63cd0bdae58c1134b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f110fa9dc46ad22f69b952181a82424", "guid": "bfdfe7dc352907fc980b868725387e98c7a8e82e625fa82ddd206dea8c9fa84b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98999db61c12b21e40a1984b152870cadb", "guid": "bfdfe7dc352907fc980b868725387e98e032c085d6a9200ce7955805e55cbf0c", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efcc8f7f11ebb7b0456c2d69e5f816f7", "guid": "bfdfe7dc352907fc980b868725387e9881624234b9e1cc99cb55cb2531d51029", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eacde330a714056466eee49d07a25998", "guid": "bfdfe7dc352907fc980b868725387e98ebef074bd364f60cd28c07cb559f90d9", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983d09ec1073f90b0177b227b6eb84b42f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98448f1d584d161d91c2c1012480c2e098", "guid": "bfdfe7dc352907fc980b868725387e98e88f0f2c5bd7feaffacb3ec53c31eddf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98403b0e0e162fae03e5d7d97e89ecc44e", "guid": "bfdfe7dc352907fc980b868725387e98597aa627afdbbdd17f2249670cd5d89a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899705a24ab5e78b875e90945ee85f368", "guid": "bfdfe7dc352907fc980b868725387e986c3527c9e79e1c2899e648537<PERSON>ebeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980afa8886145d20cf2727a09e7473b1dc", "guid": "bfdfe7dc352907fc980b868725387e98e5631cf9ba213070fb0d8cb8bdb1031c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e30e294831b5e35c77d2f7753a026d6", "guid": "bfdfe7dc352907fc980b868725387e98b6e317772edb4ee8f507111b444e3a9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fdec71354e3c409d35ccc1aa38715db", "guid": "bfdfe7dc352907fc980b868725387e987df71096fb5025e1f6851126d9c2386d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826b2e52ace14f22b5f73eec1b100ea7d", "guid": "bfdfe7dc352907fc980b868725387e9886d6ddccc20572c42e366ee7a9273080"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845700da33dfa193683cd18ed10fc411a", "guid": "bfdfe7dc352907fc980b868725387e98d6a18f8b7f56d07e6a0227831d31e624"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883432123b52a53af4d1e0f550a4ef7d6", "guid": "bfdfe7dc352907fc980b868725387e98f73d417f8b972c40bc434713626eabd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985261320c1eb6a756e46303e2d24fbe3b", "guid": "bfdfe7dc352907fc980b868725387e98e30505b4795d8dcce00ca89015720311"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a85f39af772f53b184906f39e516581", "guid": "bfdfe7dc352907fc980b868725387e98ad09fc37ca8ead9a8995b05fc28d56ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f657915134a3606b6ac944f33b0189a", "guid": "bfdfe7dc352907fc980b868725387e986632586410a6a658e40a22a0e5d40e46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983830fc95c3c984954646a6d509f4705c", "guid": "bfdfe7dc352907fc980b868725387e9884f11b0a0698c2aae915b10120e98c7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98846082a7f8410c1327bd716b7226ec9a", "guid": "bfdfe7dc352907fc980b868725387e988960b728ff0ac011c356e02334d38a72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98506b1323971b5d65141a76de40809b37", "guid": "bfdfe7dc352907fc980b868725387e98f6f0560ab0577a8d321729b1d9aee34a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3ac7ac9bc19a345b8459dfacc488a4a", "guid": "bfdfe7dc352907fc980b868725387e98b03ccbf80c1a0d94c08c0dcb15bd2496"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843bfd4d4594a20cbfbd0b21105b7257d", "guid": "bfdfe7dc352907fc980b868725387e982fd4fd9b166b9ff915a42428eb44ef31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886c00b19f122e7defe48ed58a575212a", "guid": "bfdfe7dc352907fc980b868725387e9898dfd87e02fe97e58c10a207064877cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed04c8fb79fb30eb7a7576a7c03e7c98", "guid": "bfdfe7dc352907fc980b868725387e9874b2b33cc4695f601c70c01dd1ead05d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988686c571dcd0fad2d07b43c5bf4d83df", "guid": "bfdfe7dc352907fc980b868725387e982b076915d30f32455870fe45b319caaa"}], "guid": "bfdfe7dc352907fc980b868725387e9896109f55ac1b4a585a8506f87ecace3e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e980479c76e844b2f27f6d79c351cc55384"}], "guid": "bfdfe7dc352907fc980b868725387e98937503ea55a1055e7b0eef83ad39235b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9816893ce6ef628391b4b3284eccccde90", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e987e63407603082678492f6140b6e265ef", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}