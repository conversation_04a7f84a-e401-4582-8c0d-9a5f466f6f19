{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988cb31142331ac84eed064df935110398", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9818ed7f2b456f2ccc4e136bb140dfbf03", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cddd672fd967491610f79ec3e7b13673", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b6568adb68a7abbf52314eeaeed12261", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cddd672fd967491610f79ec3e7b13673", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9868e07506204bdeef275c0090b7228264", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9899a5f3e1b1dbcb31e5d2ad2d9230a502", "guid": "bfdfe7dc352907fc980b868725387e986e329901d7572d99eb5c32672214a4e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea261f2b86ba336c3122edfa11121ccb", "guid": "bfdfe7dc352907fc980b868725387e9844b50002d8083e54bedd1d84303d10c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886d6d5de44785d253645a5fc728d2e8c", "guid": "bfdfe7dc352907fc980b868725387e9820e4718ee978327de1c9736d2f4ac80c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e46aa1d1e23f9b309ccd166cdd69d76d", "guid": "bfdfe7dc352907fc980b868725387e9841be898d5c56b81e168cd48e8a18fc4e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a1f936682a650c87c923ef269fccf69", "guid": "bfdfe7dc352907fc980b868725387e98d5a9b02d736c0e169374f290e94d4834", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890b92f6affbfb0b262dd7b4f4d673f37", "guid": "bfdfe7dc352907fc980b868725387e9846ca26f0f0b3acc24d1d85801c3a9b83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984721494cf116241c07969a5b8309a789", "guid": "bfdfe7dc352907fc980b868725387e98eb2c8448cdc1aede03bc8addd01b6609", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b97b70bb093d794ed00564b959d42bb", "guid": "bfdfe7dc352907fc980b868725387e98842feae0cc9dcfabd333feae24d2d975", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861a27751544741d988b76bc6da33824e", "guid": "bfdfe7dc352907fc980b868725387e98e438b4bcd6f6ca23ec794819ba27bece", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f19edafb50bc5ddfd9e41bff6ba2f154", "guid": "bfdfe7dc352907fc980b868725387e98ad6abf2a3e3780494b8aae32d89f8351", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f44b19c9a78c0633a2a419507cd0bfc5", "guid": "bfdfe7dc352907fc980b868725387e986614aac21397660b28a787799b459703", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9821d8aae6048a21d9d5977d1a342a98e2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fa11e860b56b1f5bef56be0da60a9586", "guid": "bfdfe7dc352907fc980b868725387e98d596c43eb5a85f7b01aaa5005136cc00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a7431fa1081cb745627eec3cf41d314", "guid": "bfdfe7dc352907fc980b868725387e98b78a4f08d6c8e31b2bd03deb7aa71cdf"}], "guid": "bfdfe7dc352907fc980b868725387e98550ed7f93988a48481622465eb1eccb1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1b96f727184f819fafd78f8ff8588be", "guid": "bfdfe7dc352907fc980b868725387e98f1571a681dd67605bfa44d8d4ba1f7f1"}], "guid": "bfdfe7dc352907fc980b868725387e98ff7ecbed0962fff0bc34be9cf9b4d028", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98391024c1cea9ff234fd2fcfe1c05452c", "targetReference": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881"}], "guid": "bfdfe7dc352907fc980b868725387e98e667be9dc6751123c6bb58fb162428de", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881", "name": "FirebaseCoreExtension-FirebaseCoreExtension_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98311e6292af5af43c801705cd189cc184", "name": "FirebaseCoreExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}