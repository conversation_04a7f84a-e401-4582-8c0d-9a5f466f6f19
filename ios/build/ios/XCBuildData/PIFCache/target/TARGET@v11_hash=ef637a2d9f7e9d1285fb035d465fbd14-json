{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e7740f11e81c57902a348fa8da2ad968", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98da3e096e28841fb52cec9111d473ca7b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cdf8008efffc672fd011cea8097243c5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98531e9ad24cab0043344d1b898cbfe38e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cdf8008efffc672fd011cea8097243c5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98703d7da242af86ebeb2cf578be20d47c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98caa0bfafe6f14381c308d40145da4a9d", "guid": "bfdfe7dc352907fc980b868725387e9894c8b30fc2e4fc8a5a159ec58ec46876", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ef6bacb55a806656683623eb892b55e3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98add3248bbc78012e8186fab1c37a9497", "guid": "bfdfe7dc352907fc980b868725387e9818c6748c27193896aa82a2c829ecb7eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859888660f7e09d9a6749b151a472f6af", "guid": "bfdfe7dc352907fc980b868725387e986e24da571d7a7a2924c9b9d3b1396d23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867f6795a78c4e1a40242425792639d53", "guid": "bfdfe7dc352907fc980b868725387e9849378cfc3eee0c529fd9de00ce6d84b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981251ad2c8452286e8f496f2685a3bf8f", "guid": "bfdfe7dc352907fc980b868725387e987d9d134354ec07ac79df4c11f5360fd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb08387097906ed5c206b5fb07c9cdb9", "guid": "bfdfe7dc352907fc980b868725387e9839d128cd8f42baf59158267ea3a64697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a570ca8eded32d385208ec0d886a57c", "guid": "bfdfe7dc352907fc980b868725387e98c6c89e6bb7dbb99e7279b3837a437c4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98834cde0d8bb03444b8621aba65d369dc", "guid": "bfdfe7dc352907fc980b868725387e981107e6643abfc2d6078418271e43a821"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f616d037234ac3089d9c0b466854140", "guid": "bfdfe7dc352907fc980b868725387e98e12138ed0ef72934d9d770baaad90fd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9085b103306defd1a56c5701b97eb44", "guid": "bfdfe7dc352907fc980b868725387e98f167d809088b748fa30d94e2c27d0363"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98895d75fbaab28dd365a6e0951325ec44", "guid": "bfdfe7dc352907fc980b868725387e98af8d056e841ccc1f786ae811a2f24955"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851905da0e5e8282306c088bbafeeb10c", "guid": "bfdfe7dc352907fc980b868725387e98a94464b4b31ea05467519b42f2b9d3af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f67c773606bc83ca67f455866621027", "guid": "bfdfe7dc352907fc980b868725387e981e086d6b0f27756f70a452e14e6aac6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876d2e5bb2bbeeb67cd978b415a2ddc33", "guid": "bfdfe7dc352907fc980b868725387e989863c8e889224ccf871950a5299bb3f9"}], "guid": "bfdfe7dc352907fc980b868725387e98968e93ffb6c1a265d32cd1c209250a35", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1b96f727184f819fafd78f8ff8588be", "guid": "bfdfe7dc352907fc980b868725387e980d24236b2e181ebdaa9dd6f945e19914"}], "guid": "bfdfe7dc352907fc980b868725387e9822eb890f2fefb0c67d26c7633a1417bd", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9802d31051a0c60854d861636a61fa918b", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e984456683e9f21d9c4ba96e4d0b218bd96", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}