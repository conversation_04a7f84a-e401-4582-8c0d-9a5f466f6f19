{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987dff3564a8d776f0d61c8195c59586f6", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986718b9a8d90f9562a91596b161483a3c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9838fef977bd9869280b0e7d2533f37c3d", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b655040ce0b20833018c8f94473ee461", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9838fef977bd9869280b0e7d2533f37c3d", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9881d58e440b03fde90dd5e107d1089349", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986f3eb628293bfd9b730dc8aa517c0c6e", "guid": "bfdfe7dc352907fc980b868725387e9845bea5f16212173583976289c4814cbe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823d723da13a7348add3ddc6c0a9ed2bc", "guid": "bfdfe7dc352907fc980b868725387e983d9b180f8b4c995a43a76a6931692a29", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f8e40b922b4e1b769a02809b98dcf08", "guid": "bfdfe7dc352907fc980b868725387e98f698515992b435e913668669143174a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4c1c3d0139928cbed2d54e7b007ac6e", "guid": "bfdfe7dc352907fc980b868725387e98ccc925eb9ee4d3a53b3668128b3dfbc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c7c9b15de07fb4bd9a02d85cda3832b", "guid": "bfdfe7dc352907fc980b868725387e98d36c6480d6de3cdc61e05ae5aada445a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b016d06739724a403052f572b73525e1", "guid": "bfdfe7dc352907fc980b868725387e98c18c1c022b8f4d2e4829286d9ea9eb29", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f14bb8a72ab84fe33bb9ef1355d7a57", "guid": "bfdfe7dc352907fc980b868725387e987fdc0d9e4555fb8a52d786503e303aa0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878853bf558e7221da072b2982f1fe4b6", "guid": "bfdfe7dc352907fc980b868725387e9829b97914110fd0d5e64be01139445551", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dd33237889ba4bcd1adbdea81fe4d97", "guid": "bfdfe7dc352907fc980b868725387e980b0a17c4993f0c1e3c1035d9092de7cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880e06f2c0f39dbe5485c2f2a7928ebd4", "guid": "bfdfe7dc352907fc980b868725387e982d771ce21bcf4c12525e052bfb061fd3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880f2f2dd0b8767b88a1bb03bec85618d", "guid": "bfdfe7dc352907fc980b868725387e98dcc9d48e108a2e3bb7954cf2997ecf48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de6417747d0bb35173877151768a850e", "guid": "bfdfe7dc352907fc980b868725387e98bd30b9bead2d9ced186a53c0ddccef16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb1e2c754a9fd20b7ecf4fd18745ba14", "guid": "bfdfe7dc352907fc980b868725387e9876e73db5f8ff38589d5537213e5b3b61", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c762723444fb0cba2bfa834fa3c75719", "guid": "bfdfe7dc352907fc980b868725387e981a118f9de259e7fbc281146b4e6e5540", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aae4a88c3693a9805c888618c3b5e7d9", "guid": "bfdfe7dc352907fc980b868725387e9846998066b6e8b5af06d20c9b731ce2b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98263c4f80618dec367d951c37407acf19", "guid": "bfdfe7dc352907fc980b868725387e981c8908469af915b8161607f63a325c28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891e17ea664c2e861a5c1ee4bfddce380", "guid": "bfdfe7dc352907fc980b868725387e9841dc314c53d93f997994a3cc9ec05301", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b9992dea16e67c222bc37c0caae092d", "guid": "bfdfe7dc352907fc980b868725387e980c7fe0792c2dce87a8e028a81e415a6e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853a62a49b8fa7aa0eaa16c922353702e", "guid": "bfdfe7dc352907fc980b868725387e9840cd02f3db934e3dbd484ed8cbe3d75c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cf1e25bcfa89903b9073711d9b1443c", "guid": "bfdfe7dc352907fc980b868725387e985293ee29e6a89500da0c4262abc24a14", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849f5c6a2b58a17adf1106083c019a782", "guid": "bfdfe7dc352907fc980b868725387e98c2bc219fad02aec5042c5dcd2102f56a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a693d0a0d645ebddd2bdc1269bf7e3bf", "guid": "bfdfe7dc352907fc980b868725387e98f5630e220a2e4c24bcac3bb39365c978", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98640adb1f29ce0f726db8918114516bbd", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988de2802ed665e45b91241d5dbb0b4752", "guid": "bfdfe7dc352907fc980b868725387e987693ba6983d4fa320e2108623a9918fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce7158d611ba57e412430062e838b2d7", "guid": "bfdfe7dc352907fc980b868725387e98a58b75d494d4f41c15410a41fba01279"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c2739aa6906a2ce213952bec471e168", "guid": "bfdfe7dc352907fc980b868725387e983e3f91a38b4cf8f62eef9a9dbeb1e977"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5876f40aca8dc8e1de2972d11c6b5c9", "guid": "bfdfe7dc352907fc980b868725387e98d7c9fd600e17701d1e2a640788c6ae31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c818952357b3d184bc0be46efad7aa63", "guid": "bfdfe7dc352907fc980b868725387e98cb20c80a3e610884c782d8c0d379b61d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c7cf9ec57142020e4488fc2e8644cd2", "guid": "bfdfe7dc352907fc980b868725387e98383b97fbe9566c80180d1e8086092953"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869ea1806668234edb11038e5536aebd9", "guid": "bfdfe7dc352907fc980b868725387e981679e2c2322757e58fc564ed6a385cb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fff41d991940bde38f21abd28cc1859", "guid": "bfdfe7dc352907fc980b868725387e987a3cca29980509f17d03859b506cb94e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c724092cdb25d3b12b793fe869c7066", "guid": "bfdfe7dc352907fc980b868725387e98ddb057306440eb4b63abf5538552a8e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f83778c0ed120f7909a7a159592565f", "guid": "bfdfe7dc352907fc980b868725387e989a9777a3c56010290391e7426c522e40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edb6d5d62d3f1f3210ab10fea42ddc10", "guid": "bfdfe7dc352907fc980b868725387e98bf0e53628cfce2739e6aa3ebe1e9a498"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc7702e2ed5b31cee8067433f7513d90", "guid": "bfdfe7dc352907fc980b868725387e98a1a2db395200f729738539727010145c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880cf2767054257e56c24d58594be39f7", "guid": "bfdfe7dc352907fc980b868725387e9805942b3394434795cb8b37251256bf90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d4c718f4e64f716785d6b9a217cea2b", "guid": "bfdfe7dc352907fc980b868725387e984cd07d5056711781b0492d4eaf29ba9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885b1e020f7ddf8d8e552ff129d5f65c7", "guid": "bfdfe7dc352907fc980b868725387e986fd53d0f1e4946dcedc3e7cb612cf198"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd0e7232f9d8b83c7581e7791cc691aa", "guid": "bfdfe7dc352907fc980b868725387e98f83097eee3096a10ebaaa4c676d44969"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98671947feaf992c7315a00ca962bc2e22", "guid": "bfdfe7dc352907fc980b868725387e98317f849dbc6b8883002aec7f8d3556da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98480d496c35450de6eb35cbbefa7c52b9", "guid": "bfdfe7dc352907fc980b868725387e98551f10e9ea06aabd46d2a8a84e871aaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f6d6eb6095cc27b4f39f7c0f9a227de", "guid": "bfdfe7dc352907fc980b868725387e9887d72b52d9097152c4fdd2055758dc60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b61ccc12596ce7482a10a77f38042eb", "guid": "bfdfe7dc352907fc980b868725387e98f8efb034044ab0455dc6f92be285b726"}], "guid": "bfdfe7dc352907fc980b868725387e980b64c235856766bbdfd1723a7017a666", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1b96f727184f819fafd78f8ff8588be", "guid": "bfdfe7dc352907fc980b868725387e9807e263630dfdb0c5706c2e566b46c007"}], "guid": "bfdfe7dc352907fc980b868725387e984cd9f4492ae998f5c53fcfc817703101", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988c2b2b80708c23d9cc22a75fce09d4cc", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e9894215c687979837195deafedcb2f2240", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}