{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98289cb9209fec275fa93423b2d9a0efd5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98056edc2bf9640f18388060799b071ec1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98113de567f35259fdf7dc50ef1a16a813", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c3fb1a60de647ecb978608287133cb7e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98113de567f35259fdf7dc50ef1a16a813", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989912dad496fa48d07c5cdb368c75c65e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b5c523181a72f3221ef2f73e5c42dbaa", "guid": "bfdfe7dc352907fc980b868725387e98a11767dd59275bfcfd80d968e0bfcbfa", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d46f274682f694a92e98b05a8c25d84d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982500aac586ef26d24e6525062421aa6b", "guid": "bfdfe7dc352907fc980b868725387e9841044b21a9943ff658a768e61994ead8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bd6a3aa997031c7fd7f0ab3bdfbc1d3", "guid": "bfdfe7dc352907fc980b868725387e98feefbb459792a5e08cb00fab739ff244"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e98e0ea9ab0a0dda51000630c2d20cbd", "guid": "bfdfe7dc352907fc980b868725387e988ed1e9957304d27122bd0bb801cd52ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f85c0ec17359b73905287b4afbc54d48", "guid": "bfdfe7dc352907fc980b868725387e981c402ed7d5c0013ef5462bc58382f79f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98603190dd610dc7c5957dbd537a330798", "guid": "bfdfe7dc352907fc980b868725387e986656357c0e85c1e3570d1303aafe0d27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1b1db418ce1f49998b166625239467b", "guid": "bfdfe7dc352907fc980b868725387e9837c9945c1107ddcff8d9f02e5289be96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e764ff80fc9442ff67411f285a78aaf", "guid": "bfdfe7dc352907fc980b868725387e98c02299d5daecec932cc4db266152fbd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef2167fbb108401386251d5f8d9b91ff", "guid": "bfdfe7dc352907fc980b868725387e98076e03f54b30e8dc154fdb142abcf3c7"}], "guid": "bfdfe7dc352907fc980b868725387e98c2791cfbea7d0e3cb65df4223c49018f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e9888a74cb35ee7b63bf94b23d2a50157cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98756a5481f49628fbd0c3cc300230734d", "guid": "bfdfe7dc352907fc980b868725387e983015053e7957bb703ecbb0c18896217c"}], "guid": "bfdfe7dc352907fc980b868725387e984c4050d88bf87b87d59794c192cedd4c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f7dbd57ea570a9e39913041a1a6e4ca3", "targetReference": "bfdfe7dc352907fc980b868725387e9865af479ae97320e284a27cf831d212b3"}], "guid": "bfdfe7dc352907fc980b868725387e9845065227e7c233225e97bdf82e2b25cf", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e9865af479ae97320e284a27cf831d212b3", "name": "GTMAppAuth-GTMAppAuth_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}], "guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98855fb84830a2ff40ce73a17fc283f650", "name": "GTMAppAuth.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}