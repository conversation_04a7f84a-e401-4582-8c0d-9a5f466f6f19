{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98512f16c5267456b5fd34f1737ff747a0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984fc9bf04e8ac578ce181933ec592bc8a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985dd43c4be8aece2dc0eaa309c0b672b3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980c98c86fff114e6efada9cb43e93ab64", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985dd43c4be8aece2dc0eaa309c0b672b3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9810a621892696e4715518a64fd308fbe1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f02659c22590b49491ba00cfce3ecf30", "guid": "bfdfe7dc352907fc980b868725387e98b6862db3a601d4a65c5bd3a3e9a8e9e9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a7c02a5d1869ea355b30645ff825036", "guid": "bfdfe7dc352907fc980b868725387e98aae862745f21a517eac4a5934e3c0f42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98696455fe9c321eb27a45d26cdfbe1b55", "guid": "bfdfe7dc352907fc980b868725387e98068389d583aa1e43500f56d945367f5e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b126f9081336af647763eb0865627fd", "guid": "bfdfe7dc352907fc980b868725387e98e9334e2bccb4488a2d4f6241e2d3b62a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afcc2887267ed62fcb54928f2b720aee", "guid": "bfdfe7dc352907fc980b868725387e980c49feb9c91b17bc31ffbefb17a87a53", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865dd4c195d0b76772617da8e320351d2", "guid": "bfdfe7dc352907fc980b868725387e9810278c781f672ac3bb3c6f7aeba7e517", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c195ddebfa71452b28e957a22f75c985", "guid": "bfdfe7dc352907fc980b868725387e98aac4411e9559784f122d967b91c98118", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f37f90c26696a4743b64ae7b031927a7", "guid": "bfdfe7dc352907fc980b868725387e98f6c75ee3a1014b6535dbf97403820aab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981747c47a4dd96db07eed2aaccf1aa64f", "guid": "bfdfe7dc352907fc980b868725387e98c4b33df8b6eb1151fe40de4351ba7481", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844e8dd26ef7076a2774e7a51d9c2d52d", "guid": "bfdfe7dc352907fc980b868725387e983153b8d4beb78b9dc3f96def394b29de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b687540860cefa4baa4c2100b045f779", "guid": "bfdfe7dc352907fc980b868725387e98505d73daf369da52b8238a7cecf5cf25", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987932de0dac06d7e15fe6fd9c6398f220", "guid": "bfdfe7dc352907fc980b868725387e98fc0c429fc09f1e97eb95f8d879515b39", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bf0a59c943a1f212046cd8099eb566b", "guid": "bfdfe7dc352907fc980b868725387e98c63ba360f844f2b7c0bab42c402a6385", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d291a9ac60cdcf3d51c1069f1b9267e6", "guid": "bfdfe7dc352907fc980b868725387e98b7d8e43c80f9252f548b9777689e1cb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7aeb42c111aa6fc57670d5db4d016f0", "guid": "bfdfe7dc352907fc980b868725387e98e583b584dc2954590f7731ebc44dc31a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9832231dadde76b93a9fee534a0a45e824", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9861e75b689fbde267f207f1ddb7c53148", "guid": "bfdfe7dc352907fc980b868725387e983403c41d08d3e5a9a4aaf39a6efe601d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98474d58db9555c46475a3a3f76ef0f253", "guid": "bfdfe7dc352907fc980b868725387e988775e2717aa62669226b881e13e298ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98159e5c7d88a5a9738f6276a687d5eca9", "guid": "bfdfe7dc352907fc980b868725387e9839da7166dfb8ffc0e5095b7755124f11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f82bc7804f5b2361f604dc704fa0e15e", "guid": "bfdfe7dc352907fc980b868725387e98831d588aba1b1eb97e9f5b9c936ae10b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c95e7ce17b74aa031c291480d06faabc", "guid": "bfdfe7dc352907fc980b868725387e98d9e0512aff91551e5f6d1aa7cb4dbfa2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dcd6af562861ead60996fc7f3f1be59", "guid": "bfdfe7dc352907fc980b868725387e980d318e7584b3389442fa33f58dceb3e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c05290aea076ef1bf05aed44408d1d0", "guid": "bfdfe7dc352907fc980b868725387e98c8df4aa62380051afc7f7fccc57ae7dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bed7bc2b6b12005b4b5d65c1f0240b2", "guid": "bfdfe7dc352907fc980b868725387e98cb5f83c744ca63e09f2c45ef7f5fffcc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c76e28276850c98e92314849217bd25", "guid": "bfdfe7dc352907fc980b868725387e988e6409c22c35efde2ed4fb5fc49dfd6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb6dd83d1591cdb6ba5935d36385fb9f", "guid": "bfdfe7dc352907fc980b868725387e98442ee4ae7745dac5feefeb6208d97c31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e891744dbde5b70261cac1969d75ed48", "guid": "bfdfe7dc352907fc980b868725387e983be3d34b0720292967da6c578c3fc0ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ec29745d6a37fc65bfa5221d286811e", "guid": "bfdfe7dc352907fc980b868725387e980af1bc52e78bad8dcd38836c1cabfeb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad70f3e758f2c24f02805437000e484b", "guid": "bfdfe7dc352907fc980b868725387e987e9ea7fea0ccca4f7f483186b08ddd2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6b5ebde327241bd36ce74bf67d2354d", "guid": "bfdfe7dc352907fc980b868725387e98620e7cef03f1ff972ff02d4b40b38d23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873ec11ddaf99c835fccaec3602ab5c32", "guid": "bfdfe7dc352907fc980b868725387e98af985069b1070f9e8b09cee519ef5bd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98487e20565ae4756d433255ac946616cc", "guid": "bfdfe7dc352907fc980b868725387e98e63cde66ce46cbaa8527e8f961b400b5"}], "guid": "bfdfe7dc352907fc980b868725387e98eeb1bceea286262b32dc6101ba1072ea", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1b96f727184f819fafd78f8ff8588be", "guid": "bfdfe7dc352907fc980b868725387e98d055b93477d0d02a380e16cfb16b642c"}], "guid": "bfdfe7dc352907fc980b868725387e98d23b672e476b4492b02d1bf4bcb08451", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e5d21c55d001cf1bd451cd248d29e82d", "targetReference": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9"}], "guid": "bfdfe7dc352907fc980b868725387e989b0437b4d4c4589dab7213538ea96ff9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9", "name": "in_app_purchase_storekit-in_app_purchase_storekit_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982a930221dc4925ae3ad26ac05af9179d", "name": "in_app_purchase_storekit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b48307e2bc58dc7155fc2e80bc197afb", "name": "in_app_purchase_storekit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}