{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98105bf3117aead0f15eac9a0fba40765e", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_local_notifications", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_local_notifications", "INFOPLIST_FILE": "Target Support Files/flutter_local_notifications/ResourceBundle-flutter_local_notifications_privacy-flutter_local_notifications-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "flutter_local_notifications_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9887847b40faa2fa4749d80cc74c19b7c5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9891d06d42157fece39ab654361e6a0cfa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_local_notifications", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_local_notifications", "INFOPLIST_FILE": "Target Support Files/flutter_local_notifications/ResourceBundle-flutter_local_notifications_privacy-flutter_local_notifications-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "flutter_local_notifications_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e981e71a3c50951f417171a5091a33cd110", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9891d06d42157fece39ab654361e6a0cfa", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_local_notifications", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_local_notifications", "INFOPLIST_FILE": "Target Support Files/flutter_local_notifications/ResourceBundle-flutter_local_notifications_privacy-flutter_local_notifications-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "flutter_local_notifications_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9874a6c1932f7ac7ad15ada013e25ff3ca", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c975cd193806a3df2227e79fbfb27935", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98f3aaad79bfaa4646b5567c78d9b2d633", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c62de42cd71ec199255d00a08b8be55", "guid": "bfdfe7dc352907fc980b868725387e98dfb4ee759b0a6ba4d54787281f0d9552"}], "guid": "bfdfe7dc352907fc980b868725387e9828dc2c015ccd28733b353ddb4878a8df", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e987434bc9491ab71790f372f1bf966d056", "name": "flutter_local_notifications-flutter_local_notifications_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9850c87ce847b276bf243d2856d441d422", "name": "flutter_local_notifications_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}