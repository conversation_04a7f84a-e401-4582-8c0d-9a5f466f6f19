{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c0bb791fe63953c0108eab482258dc2e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PurchasesHybridCommon", "PRODUCT_NAME": "PurchasesHybridCommon", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.7", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985e8661efdaa32d9b156ba9fa66f460fc", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b28d42ace960fcdbebb15a600ed2c950", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon.modulemap", "PRODUCT_MODULE_NAME": "PurchasesHybridCommon", "PRODUCT_NAME": "PurchasesHybridCommon", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.7", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9820ea10d2c51bf878375302e005674de7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b28d42ace960fcdbebb15a600ed2c950", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon.modulemap", "PRODUCT_MODULE_NAME": "PurchasesHybridCommon", "PRODUCT_NAME": "PurchasesHybridCommon", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.7", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980acf308e9229a13db1d8fed8d9f63e28", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a93004e47c4b28dc7d55151ff366231d", "guid": "bfdfe7dc352907fc980b868725387e98b00dd61da9d8d4787c8653158d3678a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98732d78739399937ed3bf632ea0b2f7c2", "guid": "bfdfe7dc352907fc980b868725387e982c7327bef25e214de5fecd0df9e6dcab", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e56df57e2c5e3c422098deb06337bec2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834fa979b4dd2415cb31ed7eeee0bb717", "guid": "bfdfe7dc352907fc980b868725387e983cdf7c2c69e7a19d1b2246fc4130b0e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d93cfe071ef9eec25e1aa8441392cd0", "guid": "bfdfe7dc352907fc980b868725387e9891ffe45c63a16c176dc89f53ae7add65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e40231cb0141aa61df398270dde4a0b", "guid": "bfdfe7dc352907fc980b868725387e9872888fdcf3c7384a3cf0fb7e91b83234"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c9236eb7c5fccc0642680ab606ffff9", "guid": "bfdfe7dc352907fc980b868725387e98020a5666c47c39cb9c6a1fb9c1e29d28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f17b6969c3856a1bcc2c1e523c9bddc", "guid": "bfdfe7dc352907fc980b868725387e98c53053162319be654da8c4f687b0babb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d3f24456e9fa602f70db88c943a527c", "guid": "bfdfe7dc352907fc980b868725387e98862ce2d3f0e4053d75a0d664a4be98f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8fcf9e26daf27aba997326749af261e", "guid": "bfdfe7dc352907fc980b868725387e98ce30c0a335302065e1549f275d1aed67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854fcaac4bc0aed5ca0920ad544864db2", "guid": "bfdfe7dc352907fc980b868725387e98101f1950d4cf3088c786c67928355ae4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f660ccbb05310b48559b47dc3262a72", "guid": "bfdfe7dc352907fc980b868725387e98ad88b6bfe49393339fcbade690d85505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b2cf740316a9e3018fd6e6d190b41bb", "guid": "bfdfe7dc352907fc980b868725387e9893ef25efd772f778ac9a2929aa16dd78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988644b0f9687d18ab2fc2ec53ec1ddb2d", "guid": "bfdfe7dc352907fc980b868725387e9883898cade4822647b157704ef32b1cf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a564ff82a6f4b229c74e7b7cfbe45d1c", "guid": "bfdfe7dc352907fc980b868725387e989ee294b34806b9e74456b4b49436c9b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b43eafed45fd45593bcaddcaf811d26", "guid": "bfdfe7dc352907fc980b868725387e981e3ce2af486caa758455b551cce4fdb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989632ebefc199638f24553452c402840d", "guid": "bfdfe7dc352907fc980b868725387e98e1bd6ea8da1dc8eb260837c35dc3793a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac40a6178aa02f68803619b4e23d3c92", "guid": "bfdfe7dc352907fc980b868725387e9895635782aa0e52de5afbd51eff550415"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895ad6273736776cb9b1f388a5bfccf74", "guid": "bfdfe7dc352907fc980b868725387e987bf6f2e459bd8369d9e68d7ae7386dc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98771b54e25f47ab8f2b0ba6bacd90fead", "guid": "bfdfe7dc352907fc980b868725387e989089c8e980495f18c1b6283049212777"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f927f9c5257a4554345f77d5cbcadab", "guid": "bfdfe7dc352907fc980b868725387e98319b58e00384e88a4f32ad23bf089e84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98691e82f403d526e0902969283cfff774", "guid": "bfdfe7dc352907fc980b868725387e98d402b612933d5ae1ae07ced907ae84b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d5a6bd426bdf2c379ad24916bb497ba", "guid": "bfdfe7dc352907fc980b868725387e987dd982ec7edd27bfaf8c8aa6c902ded8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd9cbf1a197775e10085f923fd4bb487", "guid": "bfdfe7dc352907fc980b868725387e98f0746c23e6849298744c4bc0ce35cd78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ca71479a49b2ec95558c93308e7a51a", "guid": "bfdfe7dc352907fc980b868725387e98a0d6f91829e80b67b4fbff4d733d7a4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836b3462aa7bba760ef76f01ef942acf5", "guid": "bfdfe7dc352907fc980b868725387e98d51d296cdd2a2919062ce58110989260"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbf67cbbf15af5a2923124af521bf7eb", "guid": "bfdfe7dc352907fc980b868725387e98e5d222c2d465035ada71239c7f60b409"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869da4513a650eba52ebdac6c2636228f", "guid": "bfdfe7dc352907fc980b868725387e986922c505385f89d2001de3420dcf7f01"}], "guid": "bfdfe7dc352907fc980b868725387e98d50cee646a38f981b5c4e693072ea318", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e98e9e6f4a59adbef7033b31c770fc9da7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878e02aad0e180e7e2dde2434f68cd45e", "guid": "bfdfe7dc352907fc980b868725387e98f71b77afd93321ca1ec68ad96e7fc6ae"}], "guid": "bfdfe7dc352907fc980b868725387e98be22a9b1db1912817ff1fc1c685c1faf", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9873486046930641d6624b49718bcc3f3b", "targetReference": "bfdfe7dc352907fc980b868725387e9821a12af8510f5a5a4842f587aedc172a"}], "guid": "bfdfe7dc352907fc980b868725387e98caf130638f14f388ff181d43a0e41a8b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9821a12af8510f5a5a4842f587aedc172a", "name": "PurchasesHybridCommon-PurchasesHybridCommon"}, {"guid": "bfdfe7dc352907fc980b868725387e9833260f06b6ffe5cdf6831b2907a4b8de", "name": "RevenueCat"}], "guid": "bfdfe7dc352907fc980b868725387e98b0f638c99ac593829a518d6a6a45d8d0", "name": "PurchasesHybridCommon", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988760891fc732fcd8db57a04e95d983d2", "name": "PurchasesHybridCommon.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}