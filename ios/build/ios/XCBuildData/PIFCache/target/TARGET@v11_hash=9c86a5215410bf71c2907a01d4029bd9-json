{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b420c58d008be93d72e719ad718ec652", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980008bb8836d6f1100d6834607dca9a91", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ed0eea39aecd3be235c0f26a5db5d92", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9822f8193124400b77c5d4f5fb568825f0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ed0eea39aecd3be235c0f26a5db5d92", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98652c325c7f6a52d647e27824a8ef9555", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987f00af9907d599301b27476cae9db215", "guid": "bfdfe7dc352907fc980b868725387e98d9b7b9542e8e9ca1f9450423fe021eb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c97641ceaf08f9d6749636a04e533bc", "guid": "bfdfe7dc352907fc980b868725387e989e5b4bec4f8d879bac9173ba8d1f0989", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c31bf2f1e0fd0c1211a3996736814779", "guid": "bfdfe7dc352907fc980b868725387e98ba83682a4ab6c6caa03b25668aacff16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868d2bf316ae614703f4a921db8e4d615", "guid": "bfdfe7dc352907fc980b868725387e98829241350accfeda2027dd74d33e98e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98026ae370baa29b20c7059ea468b6e0d6", "guid": "bfdfe7dc352907fc980b868725387e987ef91636acd49151fa326237694ef7c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98115f63d55eb7b05038670379b683546f", "guid": "bfdfe7dc352907fc980b868725387e98cc7d6cf93c3d6b6a05722e8b345ed9fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f44b75f277f9fe5e1de5583d5d5f9a50", "guid": "bfdfe7dc352907fc980b868725387e987859ac929f90675f2448c89113abfa57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2e9839ce04b514a410d19b6c49c433f", "guid": "bfdfe7dc352907fc980b868725387e98cb596ffd42e49c4b6f7af4429b927053"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7cf6bbb2c2c1e1fc4b4a57ef9729ad3", "guid": "bfdfe7dc352907fc980b868725387e98433e6ea3700c5238c72554284608048c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836cdeb304e0aeec1fbb3b0408ac53ae5", "guid": "bfdfe7dc352907fc980b868725387e98323e6cba772a4e99c5a89462c1f23714"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b67a9a33b7712365f386a8928bb250e", "guid": "bfdfe7dc352907fc980b868725387e98a9e1ac3891db7e7206124853c6cea1a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4c3b4fef1fd6e5786097e66ad16a098", "guid": "bfdfe7dc352907fc980b868725387e986767737dea35c492ee23029223ffd5fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98375e9b883c4f69af076879bb367667f9", "guid": "bfdfe7dc352907fc980b868725387e983fcbbe8af041079e7fb2e8a1edb9c573", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98264b3320f2276eabefd4829e88609c8a", "guid": "bfdfe7dc352907fc980b868725387e98910681d6deb162440338f3b15200a31a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822af27fbd07761d1d5f5a5aa7d053296", "guid": "bfdfe7dc352907fc980b868725387e9833db2cc72c5e8f230340f6cc2096f836"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f4b3b50dd4ece098cc62f7ee0e08819", "guid": "bfdfe7dc352907fc980b868725387e980ce97c7e55b3028d72ea7eea65feaa99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c5cb3fd4fa3f5ccfeb171743dc2b945", "guid": "bfdfe7dc352907fc980b868725387e98548ab8b0003a23aa0d9b0d5df512f262"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4c5dd81f43fc8232b215e2f757f8bae", "guid": "bfdfe7dc352907fc980b868725387e98e1c3bd68f1cfdd00882dfe34f0b9dafc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bb730e12100c57ec4195f3e20df0477", "guid": "bfdfe7dc352907fc980b868725387e98d79700e56d50502df68671ac79cd32c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ca3aa0b8a62864cea33973c8a6a0ed0", "guid": "bfdfe7dc352907fc980b868725387e98efee7089932d9475867549142b100232", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804453137a497b672b5a77d16a045e444", "guid": "bfdfe7dc352907fc980b868725387e98521dd2cd2a52a2fa5be1ccb7b9a8948e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cac77a4e4d801f4bcea494f17e6574ec", "guid": "bfdfe7dc352907fc980b868725387e98a988a61749e9c55115d2bc923b9f98ad", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981b0e76f90e4c96001200fc7ce668d295", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a8c25375bdd7e2b7146ef69a2a66c618", "guid": "bfdfe7dc352907fc980b868725387e9830f3885953b8c5a66271c7c4e4bb85d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988338013c7f9fca9582285c6ff8dcedea", "guid": "bfdfe7dc352907fc980b868725387e98631112a6ab032cb1981d1e930cef1253"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bea9ed96edb2437c79f079d1f4a89f6", "guid": "bfdfe7dc352907fc980b868725387e9868da64f4077c8a1daa7fb6f2d403e881"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bd4ab9ac3c6f3b7713ad4829f5a280c", "guid": "bfdfe7dc352907fc980b868725387e98a96d2b199fc0065fb216d1c00828b30d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877ef0c1ec456cf699515c052ff333da9", "guid": "bfdfe7dc352907fc980b868725387e98d5246c2a6677d1ba1ea59051617edcba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a06ea618357760c3356de8e27d19bed", "guid": "bfdfe7dc352907fc980b868725387e988561a49bf99f1d6e1fea06a316a288c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808f11c25ed4c450f93d1dbb3a210faed", "guid": "bfdfe7dc352907fc980b868725387e98f9a86412ef9ae4150310d2c19451859c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b568ff0943e5bf3ec1c0ba04c2afcdb", "guid": "bfdfe7dc352907fc980b868725387e985b07b008cf2b15a57878a5bf42276fc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc14f5ffd23988cefcee46935cf21f69", "guid": "bfdfe7dc352907fc980b868725387e9888fa139efc67270a0cb8d9dd844616c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809a85b6fa6c0d1794be92939ccbb78a4", "guid": "bfdfe7dc352907fc980b868725387e984a15b9ec2b5386d189951fdd00f0577d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835797fb0aee9841af027e816f84d8c49", "guid": "bfdfe7dc352907fc980b868725387e989a305620bfec3af0ee569c0dade26273"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886cb4ec25cd317c35b566387624262cf", "guid": "bfdfe7dc352907fc980b868725387e981fa48c943561aaaa94416c79dd67b3b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eedbdff9fcdee5d4fac1637e0e059719", "guid": "bfdfe7dc352907fc980b868725387e983ecc1f23cbba79c1510ad1dcefc18d2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f467c8e0b7d1c80a4abc5e5b60744bbe", "guid": "bfdfe7dc352907fc980b868725387e98d0059e2a169d38329261bf48b0691588"}], "guid": "bfdfe7dc352907fc980b868725387e9833637e94e907149f6335d245e75acce6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e9830853802fa3ef46814734671d1bed801"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa77430775ffe39299fd399f367efbaa", "guid": "bfdfe7dc352907fc980b868725387e982f78c6e9daab8916f99985b3862b5b42"}], "guid": "bfdfe7dc352907fc980b868725387e982c259079906feed5a9ea718a8532ea6d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e629f0456cf2efd9d95b7a2e3f28814d", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e985c579a7838e1eff96d59caf1b6e573fe", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}