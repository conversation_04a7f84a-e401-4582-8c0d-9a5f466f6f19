{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9825167623f1ac24f7ee0198e6d68f8e9b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980d3926596ad3cd9c175f9e28b372b54b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98968b0a0b86d427c1755055a46111a409", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98288f36904c064e8ffee3307684f0adf4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98968b0a0b86d427c1755055a46111a409", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ad21424d04da79e5b6b09fe13bb9d64", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985fffe03f2dbe1ac40b97443420ae6f6c", "guid": "bfdfe7dc352907fc980b868725387e983f9cfae9278a1267b3b1e81d8773b3ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983be225b3a7410d41f22e68191f2af1ac", "guid": "bfdfe7dc352907fc980b868725387e9847eb73ccf6d35ff4812559d7655564f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989aedadfad7daec0fd36b6bdb95783147", "guid": "bfdfe7dc352907fc980b868725387e98684ed70bb627954a4bff4eb3553d67b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0e1936c386d12a65d8f39c3c4096e0e", "guid": "bfdfe7dc352907fc980b868725387e982169df0abba8dcb77e8e0e51af7d1573", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9b583f649ee0e528bec50f9bc13969a", "guid": "bfdfe7dc352907fc980b868725387e98b303ae70b8cb4d9154ce9976f9210919", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b6f61fafc4b08c4b02e0796cbc24c2c", "guid": "bfdfe7dc352907fc980b868725387e985dbf62c9b595732f5fb638624b06fac9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb15f0d6bbdd8ee8f7930b58c54a3fbd", "guid": "bfdfe7dc352907fc980b868725387e98937ae5b50614e2c720817d1a42235f62", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe7d87f06ff20163031a00cc32963da6", "guid": "bfdfe7dc352907fc980b868725387e98347abf05dfe40c07f5eb4abb391cab09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cca64042f3c237069eedbfa335718c52", "guid": "bfdfe7dc352907fc980b868725387e98381782072813e654590b7ea2e762b192", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e4c24adee5d6f630074af935a460998", "guid": "bfdfe7dc352907fc980b868725387e983e65211358740e9aa2f13b1186d7693f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a559b5357b666e621c7b7eee872d676", "guid": "bfdfe7dc352907fc980b868725387e985107316db7cad031c533039dd8658b56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849176498cf3bf0bb065da3ad256ddf5b", "guid": "bfdfe7dc352907fc980b868725387e986d11fe075c114a7358bcdb5cfa405a24", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980532efbd6285f0766f258c943a14c6ea", "guid": "bfdfe7dc352907fc980b868725387e98fc64bb9737fa0e9fab7e3795744b38b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98481fb977b25e9f67e1219ab2f14a1cd0", "guid": "bfdfe7dc352907fc980b868725387e98943340e67dc12fbf6333224e62297ca4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98209e0ef82bdea2aa9235929e0e695dc6", "guid": "bfdfe7dc352907fc980b868725387e982fcd6a02bc42050388e66e88f341a671", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8010bfea944bc6c253a5f89c9e63233", "guid": "bfdfe7dc352907fc980b868725387e98bd04bdf5e7d6806a3ffc496a3b58e06e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8f3d3bec0fe31fdcbd37abe4e6969e8", "guid": "bfdfe7dc352907fc980b868725387e9859b6d4601e285d37000de9daba45fbdc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981758e47e95d2be7c89fe22483ec8e41f", "guid": "bfdfe7dc352907fc980b868725387e989c15b361d54dac77d3be904b741b5f1a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842a150986016ae8eb6ea1630fd9cf0f7", "guid": "bfdfe7dc352907fc980b868725387e98bf1e0f939d55dbb53f3319b55b1ddcb3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cdd700a8000e8925675cdd2862ca822", "guid": "bfdfe7dc352907fc980b868725387e98d4047caa189df51701e66abf5681034a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854d425ab16cc9c1b6ef30f79bf800dc9", "guid": "bfdfe7dc352907fc980b868725387e989a41d2151f165be69506a53e2d359ab9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981331206ece0f13026aa3b40045aa6863", "guid": "bfdfe7dc352907fc980b868725387e983432d52ce1f3833204b49c6a10966c07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b4f128d9c867ac18bedd886cfdebd82", "guid": "bfdfe7dc352907fc980b868725387e980ebb949f9c67eade059986d7a4fa5cfc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb497eb76209d45c1faed60609b33fac", "guid": "bfdfe7dc352907fc980b868725387e98211a38ee2747d81e006cfc86b4d95802", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981664dfc7329363764fdacd5d07a0c8e8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981103a7d746271da778b7c2e2693301ca", "guid": "bfdfe7dc352907fc980b868725387e9852f3ba8313a0ce364e13f7b79c431803"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bae345830e0b3ad3bcd537faf3e08ead", "guid": "bfdfe7dc352907fc980b868725387e98ce3eac326a3205d67eb6c04a6877e2a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb64c78ee64b57bccbafa46acff1a4f2", "guid": "bfdfe7dc352907fc980b868725387e98b24ea75e3e6e02110bdc1b625505e481"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fd9cd398f02283f105551296400c777", "guid": "bfdfe7dc352907fc980b868725387e987ccc0c652e8d4ca4838661aeec578222"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fbba035e01dc8678523cd79d50e9117", "guid": "bfdfe7dc352907fc980b868725387e984ea03780c1019990bd0dfa2ceaf538d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be7c1ead9c0f864de8e6213c1cdff658", "guid": "bfdfe7dc352907fc980b868725387e985c17e8259587497e574357b99a517760"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895e22e6bfd6bf7a259903749f610502f", "guid": "bfdfe7dc352907fc980b868725387e98712da6ac4359aee19fd0fef1d5ef9f8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fc3a07e5eda880751610c33184201c8", "guid": "bfdfe7dc352907fc980b868725387e9810139febdbf18170878d75dd8f905a2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f510e1724a2c0074e9169096066e233e", "guid": "bfdfe7dc352907fc980b868725387e986cd7b939ea9a5e7c7e6d205d66a0f50d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d792fabe62dc3d020c42e6c4fdf9a04", "guid": "bfdfe7dc352907fc980b868725387e9826aac65f89360fbc74110e7856619647"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9e2011eab9dd68e6565ef5040f5d1f1", "guid": "bfdfe7dc352907fc980b868725387e9817fb1b87326caa044d92ed729656166c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868ec70d57963b0dc9285b1a186e7dfe9", "guid": "bfdfe7dc352907fc980b868725387e984fb1ef32eac0d2fbce432190ba562039"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c72b7c60b70f649a257cc2c303fc16fe", "guid": "bfdfe7dc352907fc980b868725387e98de6526fb690218a3e60974ddba24c333"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810caf2e961d5de987d4145b69725a818", "guid": "bfdfe7dc352907fc980b868725387e98f2b1028c53c10e68b60ef242f758056b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c098445af4a739ddd0ab2b042d089a5", "guid": "bfdfe7dc352907fc980b868725387e98f58548e1666b6b11176a8e8253b4ae37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c36a09322c5ed04ec153cc1fc23a2ee2", "guid": "bfdfe7dc352907fc980b868725387e98e81380654c4085a121524798e33b6600"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986121dbc7dee5ec0c51fdaa0420071483", "guid": "bfdfe7dc352907fc980b868725387e98e96c09b8e4863398e6ba1c0653c662f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d46ffa43d1e84aad7a01e113907d0a6", "guid": "bfdfe7dc352907fc980b868725387e989b66ac94263b2fafb7e6fd1f432fd54b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98616e8f2c745bdb899d5b83ef5e6aa597", "guid": "bfdfe7dc352907fc980b868725387e9830589d81b035e81fe4d2686c3f9c9e55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98742e1a1f9f5b2a2d0061ec7c43fb189a", "guid": "bfdfe7dc352907fc980b868725387e98b9250e7aadee98b6bab9e44833957ffe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea282b19689abdc49a397685159ae025", "guid": "bfdfe7dc352907fc980b868725387e9810cdb8f866961feb964f372426cc7f7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4c852585f5f8635d6919fa5d6290cbe", "guid": "bfdfe7dc352907fc980b868725387e9891174e2602ad1b23b7a15814aa898375"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc6a76153f35e2ab9eb8c83b7ec86374", "guid": "bfdfe7dc352907fc980b868725387e9830d50d3b8dada408fbd5ac3ca1c576d1"}], "guid": "bfdfe7dc352907fc980b868725387e98339f3c5c18cf261c37731f1ff9cd3c6b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b207e1baac2e5ab8ff3e74eac5f257", "guid": "bfdfe7dc352907fc980b868725387e98f76f428393514513ee220ee3cd45b4b7"}], "guid": "bfdfe7dc352907fc980b868725387e98cf9e1d75d6fd474380329cbf47d24d45", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98911b19d9ae38fc82e6a73bfd4cf29ba7", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98f5ee52bb1ad32275d40a0a37ff2d7e9c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}