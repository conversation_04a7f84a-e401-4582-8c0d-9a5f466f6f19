<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Langda</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>langda</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>GIDClientID</key>
	<!-- TODO Replace this value: -->
	<!-- Copied from GoogleService-Info.plist key CLIENT_ID -->
	<string>648592789808-9cpngpr087392v4jsn41ppt1qt2bs3ct.apps.googleusercontent.com</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>sign-up-magic-link</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.oibori.langda</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>sign-up-magic-link</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.oibori.langda</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>sign-in-magic-link</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.oibori.langda</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>sign-in-social</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.oibori.langda</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.648592789808-9cpngpr087392v4jsn41ppt1qt2bs3ct</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>kakao</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>kakaoe7d5fa47e2b1b454ca7148a588bf7d0c</string>
			</array>
		</dict>
	</array>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>kakaokompassauth</string>
		<string>kakaolink</string>
		<string>kakaoplus</string>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<!-- <key>com.posthog.posthog.AUTO_INIT</key>
    <false/> -->
	<key>com.posthog.posthog.API_KEY</key>
    <string>phc_knowrDOmJdbECaeGRggB1BJMKwQdjU0amJD1ORcPB3r</string>
    <key>com.posthog.posthog.POSTHOG_HOST</key>
    <string>https://eu.i.posthog.com</string>
    <key>com.posthog.posthog.CAPTURE_APPLICATION_LIFECYCLE_EVENTS</key>
    <true/>
    <key>com.posthog.posthog.DEBUG</key>
    <true/>
</dict>
</plist>
