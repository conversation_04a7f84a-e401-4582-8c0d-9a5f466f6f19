{"openapi": "3.0.0", "info": {"title": "Langda API", "version": "1.0.0", "description": "API for the Langda application"}, "servers": [{"url": "/", "description": "Current environment"}], "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "description": "API Key Authentication"}, "SupabaseAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "scheme": "bearer", "name": "Authorization-Supabase", "bearerFormat": "JWT", "description": "Supabase JWT Authentication, format: Bearer <access_token>"}}, "schemas": {"CurrencyTransactionType": {"type": "string", "enum": ["achievement", "feature_usage", "feature_usage_refund", "periodic_reward", "referral_reward", "streak_milestone"]}, "UpgradeCheckResponse": {"type": "object", "properties": {"upgradeRequired": {"type": "boolean", "description": "Whether an upgrade is required for this build number", "example": true}}, "required": ["upgradeRequired"]}, "Error": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message", "example": "Internal Server Error"}, "traceId": {"type": "string", "description": "Trace ID for error tracking", "example": "1234567890"}}, "required": ["error", "traceId"]}, "StreakGrantType": {"type": "string", "enum": ["streak_milestone"]}, "StreakMilestone": {"type": "string", "enum": ["5_day", "10_day", "30_day"]}, "BaseCurrencyGrant": {"type": "object", "properties": {"amount": {"type": "integer", "minimum": 0, "description": "The amount of currency granted"}}, "required": ["amount"]}, "StreakGrant": {"allOf": [{"$ref": "#/components/schemas/BaseCurrencyGrant"}, {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/StreakGrantType"}, "milestone": {"$ref": "#/components/schemas/StreakMilestone"}}, "required": ["type", "milestone"]}]}, "AchievementGrantType": {"type": "string", "enum": ["achievement"]}, "AchievementId": {"type": "string", "enum": ["diary_complete", "perfect_score"], "description": "Identifier for an achievement"}, "AchievementType": {"type": "string", "enum": ["one_time", "repeatable"]}, "AchievementGrant": {"allOf": [{"$ref": "#/components/schemas/BaseCurrencyGrant"}, {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/AchievementGrantType"}, "achievementId": {"$ref": "#/components/schemas/AchievementId"}, "achievementType": {"$ref": "#/components/schemas/AchievementType"}}, "required": ["type", "achievementId", "achievementType"]}]}, "PeriodicGrantType": {"type": "string", "enum": ["periodic_reward"]}, "SubscriptionType": {"type": "string", "enum": ["trial", "basic"], "description": "Subscription type"}, "PeriodicGrant": {"allOf": [{"$ref": "#/components/schemas/BaseCurrencyGrant"}, {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/PeriodicGrantType"}, "period": {"type": "string", "enum": ["initial", "daily", "monthly"]}, "subscriptionType": {"$ref": "#/components/schemas/SubscriptionType"}}, "required": ["type", "period", "subscriptionType"]}]}, "CurrencyGrant": {"oneOf": [{"$ref": "#/components/schemas/StreakGrant"}, {"$ref": "#/components/schemas/AchievementGrant"}, {"$ref": "#/components/schemas/PeriodicGrant"}], "discriminator": {"propertyName": "type", "mapping": {"streak_milestone": "#/components/schemas/StreakGrant", "achievement": "#/components/schemas/AchievementGrant", "periodic_reward": "#/components/schemas/PeriodicGrant"}}, "description": "Any single currency grant"}, "DiaryEntryResponse": {"type": "object", "properties": {"updatedCurrencyBalance": {"type": "number", "description": "User's updated currency balance after the operation", "example": 100}, "data": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the diary entry", "example": "123e4567-e89b-12d3-a456-************"}}, "required": ["id"]}, "currencyGrants": {"type": "array", "items": {"$ref": "#/components/schemas/CurrencyGrant"}, "description": "Currency grants awarded by this action, if any"}}, "required": ["updatedCurrencyBalance", "data", "currencyGrants"]}, "DiaryInsertionRequestBody": {"type": "object", "properties": {"user_id": {"type": "string", "description": "Unique identifier for the user", "example": "user-123"}, "content": {"type": "string", "description": "The diary entry content", "example": "Today I learned..."}, "weather": {"type": "string", "description": "Weather condition during the entry", "example": "sunny"}, "date": {"type": "string", "description": "Date of the diary entry (YYYY-MM-DD)", "example": "2024-02-24"}}, "required": ["user_id", "content", "date"]}, "UserQuestionRequestBody": {"type": "object", "properties": {"title": {"type": "string", "description": "Title of the user question", "example": "<PERSON><PERSON>"}, "body": {"type": "string", "description": "Content of the user question", "example": "How do I use this feature?"}, "userId": {"type": "string", "format": "uuid", "description": "UUID of the user asking the question", "example": "123e4567-e89b-12d3-a456-************"}, "deviceInfo": {"type": "string", "description": "Information about the user's device", "example": "iPhone 12, iOS 15.0"}}, "required": ["title", "body", "userId", "deviceInfo"]}, "GetEntryDateResponse": {"type": "string", "description": "The date for which the user can make an entry", "example": "2024-02-24"}, "LearningMotivation": {"type": "string", "enum": ["career", "emigration", "fun", "other", "social", "study_abroad"]}, "ExplanationLanguagePreference": {"type": "string", "enum": ["diary_language", "native_language"], "description": "Preference for correction language"}, "CreateUserProfileResponse": {"type": "object", "properties": {"user_id": {"type": "string", "description": "The ID of the created user profile", "example": "user-123"}, "nickname": {"type": "string", "description": "User's chosen nickname", "example": "언어학습자123"}, "learning_motivation": {"$ref": "#/components/schemas/LearningMotivation"}, "referral_code": {"type": "string", "description": "The newly generated referral code for this user", "example": "NEW456"}, "native_language": {"type": "string", "nullable": true, "description": "User's native language (ISO 639-1 code), null if not set", "examples": ["ko", "ja", "en", null]}, "explanation_language": {"$ref": "#/components/schemas/ExplanationLanguagePreference"}}, "required": ["user_id", "nickname", "learning_motivation", "referral_code", "native_language", "explanation_language"]}, "CreateUserProfileRequest": {"type": "object", "properties": {"learning_motivation": {"$ref": "#/components/schemas/LearningMotivation"}, "nickname": {"type": "string", "minLength": 1, "maxLength": 30, "pattern": "^[\\p{L}\\p{N}]+$", "description": "User's chosen nickname", "example": "언어학습자123"}, "referral_code": {"type": "string", "nullable": true, "pattern": "^[A-Z0-9]{6}$", "description": "Optional referral code of the person who referred this user", "example": "ABC123"}, "native_language": {"type": "string", "nullable": true, "minLength": 2, "maxLength": 2, "description": "Optional native language of the user (ISO 639-1 code)", "examples": ["ko", "ja", "en"]}, "explanation_language": {"allOf": [{"$ref": "#/components/schemas/ExplanationLanguagePreference"}, {"nullable": true, "description": "Optional preference for correction language"}]}}, "required": ["learning_motivation", "nickname"]}, "DeleteUserResponse": {"type": "object", "properties": {"id": {"type": "string", "description": "ID of the deleted user", "example": "user-123"}}, "required": ["id"]}, "CorrectionFeedbackResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "The ID of the correction that received feedback", "example": "123e4567-e89b-12d3-a456-************"}, "likes": {"type": "integer", "description": "The updated likes count for the correction", "example": 1}}, "required": ["id", "likes"]}, "FeedbackType": {"type": "string", "enum": ["upvote", "downvote"]}, "CorrectionFeedbackRequestBody": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/FeedbackType"}, "user_id": {"type": "string"}}, "required": ["type", "user_id"], "example": {"type": "upvote", "user_id": "123e4567-e89b-12d3-a456-************"}}, "DeepExplanationData": {"type": "object", "properties": {"correctionId": {"type": "string", "format": "uuid", "description": "Unique identifier for the correction", "example": "123e4567-e89b-12d3-a456-************"}, "explanation": {"type": "string", "description": "Deep explanation in diary language (English)", "example": "The modification improves clarity by restructuring the sentence..."}, "explanationUserLanguage": {"type": "string", "description": "Deep explanation in user language (Korean)", "example": "문장의 구조를 재구성하여 명확성을 향상시켰습니다..."}}, "required": ["correctionId", "explanation", "explanationUserLanguage"]}, "BaseCurrencyResponse": {"type": "object", "properties": {"updatedCurrencyBalance": {"type": "number", "description": "User's updated currency balance after the operation", "example": 100}}, "required": ["updatedCurrencyBalance"]}, "DeepExplanationResponse": {"allOf": [{"$ref": "#/components/schemas/BaseCurrencyResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/DeepExplanationData"}}, "required": ["data"]}]}, "FeatureCostType": {"type": "string", "enum": ["per_use"], "description": "Type of cost for a feature"}, "FeatureCost": {"type": "object", "properties": {"cost": {"type": "number", "description": "Cost of the feature", "example": 5}, "type": {"$ref": "#/components/schemas/FeatureCostType"}, "freeFor": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionType"}, "description": "List of subscriptions that get the feature for free"}}, "required": ["cost", "type", "freeFor"], "description": "Mapping of feature IDs to their cost configuration", "example": {"cost": 5, "type": "per_use", "freeFor": ["basic"]}}, "FeatureCostsData": {"type": "object", "properties": {"deep_explanation": {"$ref": "#/components/schemas/FeatureCost"}, "correction_chat": {"$ref": "#/components/schemas/FeatureCost"}}}, "FeatureCostsResponse": {"type": "object", "properties": {"featureCosts": {"$ref": "#/components/schemas/FeatureCostsData"}}, "required": ["featureCosts"]}, "MonthlyGrantsResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Whether the monthly grants were processed successfully", "example": true}, "processedAt": {"type": "string", "format": "date-time", "description": "When the monthly grants were processed", "example": "2025-03-12T11:30:00.000Z"}}, "required": ["success", "processedAt"]}, "RevenueCatWebhookResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Whether the webhook was processed successfully", "example": true}}, "required": ["success"]}, "RevenueCatWebhookRequest": {"type": "object", "properties": {"api_version": {"type": "string", "description": "RevenueCat API version", "example": "1.0"}, "event": {"type": "object", "additionalProperties": {"nullable": true}, "description": "RevenueCat event payload", "example": {"type": "RENEWAL", "id": "evt_123", "app_user_id": "user_123"}}}, "required": ["api_version", "event"]}, "RecoveredTaskData": {"type": "object", "properties": {"taskId": {"type": "string", "format": "uuid"}, "type": {"type": "string"}, "userId": {"type": "string", "format": "uuid"}}, "required": ["taskId", "type", "userId"]}, "RecoverStuckTasksResponse": {"type": "object", "properties": {"recovered": {"type": "number"}, "tasks": {"type": "array", "items": {"$ref": "#/components/schemas/RecoveredTaskData"}}}, "required": ["recovered", "tasks"]}, "StreakCalendarEntry": {"type": "object", "properties": {"date": {"type": "string", "description": "Date in YYYY-MM-DD format", "example": "2024-07-28"}, "reward_amount": {"type": "integer", "minimum": 0, "description": "Amount of currency to be rewarded", "example": 50}, "is_bonus": {"type": "boolean", "description": "Whether this is a bonus reward (every 5th day)", "example": false}, "is_claimed": {"type": "boolean", "description": "Whether the user has already claimed this day's reward", "example": false}, "day_in_streak": {"type": "integer", "minimum": 1, "description": "Which day in the streak this represents", "example": 1}}, "required": ["date", "reward_amount", "is_bonus", "is_claimed", "day_in_streak"]}, "StreakCalendarResponse": {"type": "object", "properties": {"start_date": {"type": "string", "description": "Start date of the calendar range (YYYY-MM-DD)", "example": "2024-07-28"}, "end_date": {"type": "string", "description": "End date of the calendar range (YYYY-MM-DD)", "example": "2024-08-27"}, "rewards": {"type": "array", "items": {"$ref": "#/components/schemas/StreakCalendarEntry"}, "description": "List of daily rewards in the calendar range"}}, "required": ["start_date", "end_date", "rewards"]}, "ReferralRewardsResponse": {"type": "object", "properties": {"per_signup": {"type": "integer", "minimum": 0, "description": "Amount of currency rewarded to the referrer when someone signs up using their code", "example": 500}, "invitee_reward": {"type": "integer", "minimum": 0, "description": "Amount of currency rewarded to the person who signs up using a referral code", "example": 100}}, "required": ["per_signup", "invitee_reward"]}, "PhraseHelpResponseBody": {"type": "object", "properties": {"response": {"type": "string", "description": "The response to the request for help with English phrasing.", "example": "The banner fluttered in the wind"}}, "required": ["response"]}, "PhraseHelpRequestBody": {"type": "object", "properties": {"query": {"type": "string", "maxLength": 200, "description": "User query asking for translation help (max 200 chars)", "example": "How do I say 눈치?"}}, "required": ["query"]}}, "parameters": {}}, "paths": {"/upgrade-required/{buildNumber}": {"get": {"tags": ["System"], "description": "Check if the application needs to be upgraded based on build number", "parameters": [{"schema": {"type": "string", "description": "Application build number to check for upgrade requirement", "example": "123"}, "required": true, "name": "buildNumber", "in": "path"}], "responses": {"200": {"description": "Successfully checked upgrade requirement", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpgradeCheckResponse"}}}}, "400": {"description": "Invalid build number format", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server configuration error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/insert": {"post": {"security": [{"BearerAuth": []}], "tags": ["Diary"], "description": "Create a new diary entry", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DiaryInsertionRequestBody"}}}}, "responses": {"200": {"description": "Successfully created diary entry", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DiaryEntryResponse"}}}}, "400": {"description": "Invalid request body", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/user-questions/notification": {"post": {"security": [{"BearerAuth": []}], "tags": ["Notifications"], "description": "Send a notification for a user question", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserQuestionRequestBody"}}}}, "responses": {"200": {"description": "Successfully sent user question notification", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "description": "Notification ID", "example": "ntfy-123"}}, "required": ["id"]}}}}, "400": {"description": "Invalid request body", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/entry-date": {"get": {"security": [{"BearerAuth": []}], "tags": ["Diary"], "description": "Get the date for which the user can make an entry", "parameters": [{"schema": {"type": "string", "description": "User ID to get entry date for", "example": "user-123"}, "required": true, "name": "userId", "in": "query"}], "responses": {"200": {"description": "Successfully retrieved entry date", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetEntryDateResponse"}}}}, "400": {"description": "Invalid query parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users/profile": {"post": {"security": [{"BearerAuth": [], "SupabaseAuth": []}], "tags": ["User Management"], "description": "Create a new user profile with optional referral", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserProfileRequest"}}}}, "responses": {"200": {"description": "Successfully created user profile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserProfileResponse"}}}}, "400": {"description": "Invalid request body or referral code", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Missing or invalid JWT token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users/me": {"delete": {"security": [{"BearerAuth": [], "SupabaseAuth": []}], "tags": ["User Management"], "description": "Delete the authenticated user and all associated data", "responses": {"200": {"description": "Successfully deleted user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteUserResponse"}}}}, "401": {"description": "Missing or invalid JWT token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/corrections/{id}/feedback": {"post": {"tags": ["Corrections"], "description": "Provide feedback (upvote/downvote) for a correction", "parameters": [{"schema": {"type": "string", "format": "uuid", "description": "The ID of the correction to provide feedback for", "example": "123e4567-e89b-12d3-a456-************"}, "required": true, "name": "id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CorrectionFeedbackRequestBody"}}}}, "responses": {"200": {"description": "Successfully recorded feedback for the correction", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CorrectionFeedbackResponse"}}}}, "400": {"description": "Invalid request parameters or body", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Correction not found or not accessible by the user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "409": {"description": "Correction has already been voted on", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/corrections/{id}/deep-explain": {"post": {"security": [{"BearerAuth": []}], "tags": ["Diary"], "description": "Generate a deep explanation for a correction", "parameters": [{"schema": {"type": "string", "format": "uuid", "description": "Unique identifier for the correction", "example": "123e4567-e89b-12d3-a456-************"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "Successfully generated deep explanation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeepExplanationResponse"}}}}, "400": {"description": "Invalid request parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/currency/feature-costs": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "description": "Expose the cost configuration for feature usage", "responses": {"200": {"description": "Returns the cost configuration for features", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeatureCostsResponse"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/cron/monthly-subscription-grants": {"post": {"security": [{"BearerAuth": []}], "tags": ["<PERSON><PERSON>"], "description": "Process monthly gem grants for annual subscriptions", "responses": {"200": {"description": "Monthly grants processed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MonthlyGrantsResponse"}}}}, "401": {"description": "Unauthorized - Invalid or missing cron auth key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/revenuecat/webhook": {"post": {"security": [{"BearerAuth": []}], "tags": ["Subscription"], "description": "Handle RevenueCat subscription webhook events", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RevenueCatWebhookRequest"}}}}, "responses": {"200": {"description": "Webhook processed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RevenueCatWebhookResponse"}}}}, "401": {"description": "Unauthorized - Invalid webhook auth key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/cron/recover-stuck-tasks": {"post": {"security": [{"BearerAuth": []}], "tags": ["<PERSON><PERSON>"], "description": "Recover stuck tasks and issue refunds", "responses": {"200": {"description": "Successfully recovered stuck tasks", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecoverStuckTasksResponse"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/cron/reset-broken-streaks": {"post": {"security": [{"BearerAuth": []}], "description": "Reset streaks for users who have not written a diary in the last day", "tags": ["<PERSON><PERSON>"], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "Authorization", "in": "header"}], "responses": {"200": {"description": "Successfully reset broken streaks", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}}, "required": ["success"]}}}}}}}, "/currency/rewards/streak-calendar": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "description": "Get the streak calendar showing upcoming rewards", "parameters": [{"schema": {"type": "integer", "default": 14, "minimum": 1}, "required": false, "description": "Number of days to include in the forecast, starting from today", "name": "days", "in": "query"}], "responses": {"200": {"description": "Returns the streak calendar with future rewards", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StreakCalendarResponse"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/currency/rewards/referral": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "description": "Get the referral rewards configuration including per-signup and invitee rewards", "responses": {"200": {"description": "Returns the referral rewards configuration", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReferralRewardsResponse"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/diary/phrase-help": {"post": {"security": [{"BearerAuth": []}], "tags": ["Diary"], "description": "Provides translation help for specific words/phrases asked by the user.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PhraseHelpRequestBody"}}}}, "responses": {"200": {"description": "Successfully generated phrase help", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PhraseHelpResponseBody"}}}}, "400": {"description": "Invalid request body or query format", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error during LLM generation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}}