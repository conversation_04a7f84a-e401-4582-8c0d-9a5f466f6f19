                        -H/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-<PERSON><PERSON><PERSON>O<PERSON>_PLATFORM=android-21
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Developer/projects/langda/build/app/intermediates/cxx/Debug/3j1v1116/obj/arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Developer/projects/langda/build/app/intermediates/cxx/Debug/3j1v1116/obj/arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/Developer/projects/langda/android/app/.cxx/Debug/3j1v1116/arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2