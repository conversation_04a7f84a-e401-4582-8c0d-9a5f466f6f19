{"project_info": {"project_number": "648592789808", "project_id": "langda-25669", "storage_bucket": "langda-25669.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:648592789808:android:834eb26132c7e8a074f8f5", "android_client_info": {"package_name": "com.oibori.langda"}}, "oauth_client": [{"client_id": "648592789808-duq7jklvh9l8sh5vit7fdhhl30hqh9g1.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.oibori.langda", "certificate_hash": "b68ec480658ba7deac069529fa0a53c35830237b"}}, {"client_id": "648592789808-v5mg92ebffnn432nsimf5c7mmvgsnv47.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.oibori.langda", "certificate_hash": "607bc2fb606816dd7707ac655d38b7a33744def2"}}, {"client_id": "648592789808-vb2ng7epgpotc867cg6420mah6j18lvd.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyC8zVtUSe7eahErID6sdOs-EyhkILJXTUU"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "648592789808-vb2ng7epgpotc867cg6420mah6j18lvd.apps.googleusercontent.com", "client_type": 3}, {"client_id": "648592789808-9cpngpr087392v4jsn41ppt1qt2bs3ct.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.oibori.langda", "app_store_id": "6695738311"}}]}}}], "configuration_version": "1"}