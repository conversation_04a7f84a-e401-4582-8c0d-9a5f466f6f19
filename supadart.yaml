# Don't want to expose your supabase credentials? you have two options
# 1. Use a .env file to specify SUPABASE_URL and SUPABASE_ANON_KEY
# 2. Specify --url and --key in the CLI (ex. supadart -u <url> -k <key>)
SUPABASE_URL:
SUPABASE_ANON_KEY:

# Enums in your database? map them here
# Please take a look at the documentation to see how to work with enums
enums:
  diary_version_type: [original, corrected]
  learning_motivation: [social, study_abroad, emigration, career, fun, other]
  currency_transaction_type:
    [streak_milestone, achievement, trial_bonus, periodic_reward, feature_usage]
  generation_task_status: [a]
  explanation_language_preference: [native_language, diary_language]

# Optional, where to place the generated classes files default: ./lib/models/
output: lib/models/
# Set to true, if you want to generate separated files for each classes
separated: false
# Set to true, if you are not using Flutter, just normal Dart project
dart: false

# Optional, used to map table names to class names(case-sensitive)
mappings:
  # books: book
  # categories: category
  # children: child
  # people: person

# Optional, used to exclude methods from generated classes, comment out to include them
exclude:
  # - toJson
  - copyWith
  - New
