{"v": "5.12.1", "fr": 60, "ip": 0, "op": 120, "w": 430, "h": 430, "nm": "wired-gradient-35-edit", "ddd": 0, "assets": [{"id": "comp_1", "nm": "mask-in-d", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Shape Layer 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-84, 158], [-165, 95]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-88, 158], [-191, 158]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 47, "s": [0]}, {"t": 67, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 42, "s": [45]}, {"t": 62, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 42, "op": 174, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Pen", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.172], "y": [0.144]}, "t": -1, "s": [317]}, {"i": {"x": [0.241], "y": [1]}, "o": {"x": [0.173], "y": [0.407]}, "t": 42, "s": [25.007]}, {"i": {"x": [0.517], "y": [1]}, "o": {"x": [0.534], "y": [0]}, "t": 60, "s": [-11.153]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": -1, "s": [177, 543, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 29, "s": [216, 134, 0], "to": [0, 0, 0], "ti": [3.905, -10.062, 0]}, {"i": {"x": 0.241, "y": 1}, "o": {"x": 0.167, "y": 0.29}, "t": 42, "s": [200.494, 230.423, 0], "to": [7.095, -17.438, 0], "ti": [-13.569, -4.441, 0]}, {"i": {"x": 0.517, "y": 1}, "o": {"x": 0.534, "y": 0}, "t": 60, "s": [244.571, 221.374, 0], "to": [1.414, 0.463, 0], "ti": [-0.482, 0.481, 0]}, {"t": 90, "s": [215.95, 215, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [249.95, 250, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 2, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-0.931, -18.434], [0, -48.75]], "c": false}]}, {"i": {"x": 0.211, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.23, 48.818], [0, -48.75]], "c": false}]}, {"t": 34, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.23, 48.818], [0, -48.75]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 210.35], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 2, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[33.628, -39.924], [-31.272, -39.924], [-32.45, -108.7], [32.45, -108.7]], "c": true}]}, {"i": {"x": 0.087, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.403, 67.908], [-32.497, 67.908], [-32.45, -108.7], [32.45, -108.7]], "c": true}]}, {"t": 34, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.45, 108.7], [-32.45, 108.7], [-32.45, -108.7], [32.45, -108.7]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [249.95, 243.3], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 2, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[4.225, -176.524], [33.628, -176.724], [-31.272, -176.724]], "c": true}]}, {"i": {"x": 0.087, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[0.003, -12.692], [32.403, -68.892], [-32.497, -68.892]], "c": true}]}, {"t": 34, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[0.05, 28.1], [32.45, -28.1], [-32.45, -28.1]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [249.95, 380.1], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 2, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.45, 21.4], [-32.45, 21.4], [-32.017, 21.391], [32.883, 21.391]], "c": true}]}, {"i": {"x": 0.087, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.45, 21.4], [-32.45, 21.4], [-32.45, -21.4], [32.45, -21.4]], "c": true}]}, {"t": 34, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.45, 21.4], [-32.45, 21.4], [-32.45, -21.4], [32.45, -21.4]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [249.95, 113.2], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 2, "op": 61, "st": -2, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Vector", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [33.588, -33.588, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-0.937, 14.969], [0.154, 112.218]], "c": false}]}, {"t": 90, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -47.5], [0, 47.5]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 45, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 60, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Pencil", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.38], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 60, "s": [-56]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.38, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [275.539, 377.466, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [68.539, 361.466, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-152.067, 152.073, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-32.191, 158.231], [31.782, 158.231], [31.728, -58.708], [-32.491, -58.755]], "c": true}]}, {"t": 90, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40, 135], [40, 135], [40, -135], [-40, -135]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-32.237, 158.279], [0.004, 215.06], [31.736, 158.279]], "c": false}]}, {"t": 90, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-40.058, 135.06], [0.004, 215.06], [39.942, 135.06]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 45, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-32.518, 101.289], [-32.491, 57.237], [31.727, 57.284], [31.7, 101.337]], "c": false}]}, {"t": 90, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40, 25], [-40, -25], [40, -25], [40, 25]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [113.393, -113.198], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 45, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 60, "op": 1800, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_3", "nm": "mask-in-r", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Pencil", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.42], "y": [1]}, "o": {"x": [0.58], "y": [0]}, "t": 8, "s": [-45]}, {"t": 59, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.42, "y": 1}, "o": {"x": 0.58, "y": 0}, "t": 8, "s": [215.539, 361.466, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 59, "s": [68.539, 361.466, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-152.067, 152.073, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": -1, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.002, 215.056], [0.006, 215.056], [0.006, 215.056], [0.002, 215.056]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40, 135], [40, 135], [39.792, 135], [-40.208, 135]], "c": true}]}, {"i": {"x": 0.36, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40, 135], [40, 135], [39.792, 135], [-40.208, 135]], "c": true}]}, {"t": 59, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40, 135], [40, 135], [40, -135], [-40, -135]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": -1, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[0.002, 215.056], [0.004, 215.06], [0.006, 215.056]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-40.058, 135.06], [0.004, 215.06], [39.942, 135.06]], "c": false}]}, {"i": {"x": 0.36, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-40.058, 135.06], [0.004, 215.06], [39.942, 135.06]], "c": false}]}, {"t": 59, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-40.058, 135.06], [0.004, 215.06], [39.942, 135.06]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 45, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": -1, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.135, 375.28], [-0.135, 375.28], [-0.132, 375.28], [-0.132, 375.28]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40.208, 295], [-40.363, 295], [39.637, 295], [39.792, 295]], "c": false}]}, {"i": {"x": 0.36, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40.208, 295], [-40.208, 245], [39.792, 245], [39.792, 295]], "c": false}]}, {"t": 59, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40, 25], [-40, -25], [40, -25], [40, 25]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [113.393, -113.198], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 45, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1799, "st": -1, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Vector", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [33.588, -33.588, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-0.052, 47.5], [0, 47.5]], "c": false}]}, {"t": 38, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -47.5], [0, 47.5]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.39, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [-93.984, 94.913], "to": [0, 0], "ti": [0, 0]}, {"t": 59, "s": [0, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 45, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 23, "op": 1799, "st": -1, "ct": 1, "bm": 0}]}, {"id": "comp_4", "nm": "hover-circle", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "mask-h-c", "td": 1, "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "gradient", "tt": 1, "tp": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.4], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [-94]}, {"t": 120, "s": [266]}], "ix": 10}, "p": {"a": 0, "k": [271.941, 219.46, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [240, 240, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 29, "nm": "Gaussian Blur", "np": 5, "mn": "ADBE Gaussian Blur 2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Blurriness", "mn": "ADBE Gaussian Blur 2-0001", "ix": 1, "v": {"a": 0, "k": 175, "ix": 1}}, {"ty": 7, "nm": "Blur Dimensions", "mn": "ADBE Gaussian Blur 2-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Repeat Edge Pixels", "mn": "ADBE Gaussian Blur 2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-97.478, 0], [0, -97.478], [97.478, 0], [30.894, 26.574], [0, 53.531]], "o": [[97.478, 0], [0, 97.478], [-43.948, 0], [-37.631, -32.369], [0, -97.478]], "v": [[0, -176.5], [176.5, 0], [0, 176.5], [-105.29, 115.869], [-176.5, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.808, 0.808, 0.808, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-gradient-35-edit').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [113.242, -118.884], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "primary.design", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "design"}, {"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [500, 500], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.839, 0.929, 0.09, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-gradient-35-edit').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "secondary.design", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false, "cl": "design"}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_5", "nm": "mask-h-c", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [33.588, -33.588, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.55, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -47.5], [0, 47.5]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.45, "y": 0}, "t": 30, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.849, 74.997], [0.783, 113.998]], "c": false}]}, {"i": {"x": 0.55, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -47.5], [0, 47.5]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.45, "y": 0}, "t": 90, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.904, 31.497], [1.124, 82.498]], "c": false}]}, {"t": 120, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -47.5], [0, 47.5]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 45, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Pencil 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 60, "s": [-90]}, {"t": 120, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 0, "s": [68.539, 361.466, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 60, "s": [372.539, 361.466, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [68.539, 361.466, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-152.067, 152.073, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 2, "s": [{"i": [[0, 0], [0.009, 0.491], [-0.096, 0.275], [-34.756, -0.107], [-0.658, -1.409], [0.065, -2.727], [0, 0]], "o": [[0, 0], [-0.043, -2.454], [0.799, -1.401], [31.879, 0.099], [0.084, 0.285], [-0.012, 0.493], [0, 0]], "v": [[-38.394, 28.487], [-39.639, 0.136], [-40.218, -21.579], [1.834, -26.696], [39.514, -21.592], [39.842, -4.167], [39.39, 31.566]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5, "s": [{"i": [[0, 0], [0.013, 0.723], [-0.13, 0.403], [-34.373, -0.043], [-0.777, -2.08], [-0.097, -4.008], [0, 0]], "o": [[0, 0], [-0.064, -3.611], [0.932, -2.048], [31.504, 0.041], [0.114, 0.42], [0.271, 4.028], [0, 0]], "v": [[-31.844, 33.553], [-39.347, 34.919], [-40.904, -16.14], [1.079, -23.631], [38.735, -16.161], [38.55, 33.563], [22.265, 33.75]], "c": false}]}, {"i": {"x": 0.55, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0, 0], [0.027, 1.495], [-0.242, 0.833], [-33.097, 0.168], [-1.175, -4.317], [-0.636, -8.279], [0, 0]], "o": [[0, 0], [-0.135, -7.469], [1.374, -4.203], [30.254, -0.152], [0.213, 0.869], [1.214, 15.812], [0, 0]], "v": [[-2.797, 97.594], [-39.233, 88.778], [-39.52, 41.919], [2.233, 26.518], [39.809, 41.873], [39.19, 78.225], [8.123, 98.645]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.45, "y": 0}, "t": 30, "s": [{"i": [[0, 0], [0.045, 2.464], [-0.383, 1.372], [-31.493, 0.433], [-1.675, -7.127], [0.329, -13.687], [0, 0]], "o": [[0, 0], [-0.224, -12.314], [1.929, -6.91], [28.684, -0.395], [0.337, 1.434], [-0.059, 2.474], [0, 0]], "v": [[-5.306, 157.5], [-38.754, 136.062], [-39.233, 85.336], [2.23, 59.999], [39.706, 85.257], [38.665, 145.194], [3.069, 157.125]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [{"i": [[0, 0], [0.029, 1.594], [-0.257, 0.888], [-32.932, 0.195], [-1.227, -4.606], [0.212, -8.855], [0, 0]], "o": [[0, 0], [-0.144, -7.967], [1.431, -4.481], [30.092, -0.177], [0.226, 0.928], [-0.038, 1.601], [0, 0]], "v": [[-19.641, 110.907], [-37.754, 104.729], [-39.49, 46.386], [2.233, 29.963], [39.798, 46.337], [39.572, 106.039], [14.408, 112.481]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [{"i": [[0, 0], [0.016, 0.868], [-0.151, 0.485], [-34.132, -0.003], [-0.852, -2.503], [0.115, -4.824], [0, 0]], "o": [[0, 0], [-0.078, -4.34], [1.016, -2.455], [31.268, 0.004], [0.133, 0.505], [-0.021, 0.872], [0, 0]], "v": [[-29.478, 66.382], [-39.81, 62.218], [-39.705, 13.884], [2.235, 4.899], [39.875, 13.859], [38.583, 67.118], [24.623, 68.282]], "c": false}]}, {"i": {"x": 0.55, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [0, 0], [-0.025, 0.001], [-35.569, -0.241], [-0.405, 0.013], [-0.001, 0], [0, 0]], "o": [[0, 0], [0.002, 0], [0.518, -0.03], [32.674, 0.221], [0.021, -0.001], [0, 0], [0, 0]], "v": [[-40, -25], [-40, -25], [-39.961, -25.002], [2.237, -25.088], [39.968, -24.999], [40, -25], [40, -25]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.45, "y": 0}, "t": 90, "s": [{"i": [[0, 0], [0, 0.001], [-0.013, 0.104], [-29.543, 0.407], [-0.279, -2.37], [-0.001, -0.008], [0, 0]], "o": [[0, 0], [0.001, -0.008], [0.277, -2.139], [26.777, -0.368], [0.015, 0.126], [0, 0.001], [0, 0]], "v": [[-31.63, 88.008], [-31.63, 88.007], [-31.61, 87.838], [1.739, 63.698], [32.112, 87.803], [32.134, 88.007], [32.134, 88.008]], "c": false}]}, {"t": 120, "s": [{"i": [[0, 0], [0, 0], [-0.025, 0.001], [-35.569, -0.241], [-0.405, 0.013], [-0.001, 0], [0, 0]], "o": [[0, 0], [0.002, 0], [0.518, -0.03], [32.674, 0.221], [0.021, -0.001], [0, 0], [0, 0]], "v": [[-40, -25], [-40, -25], [-39.961, -25.002], [2.237, -25.088], [39.968, -24.999], [40, -25], [40, -25]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [113.393, -113.198], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 45, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 1, "op": 59, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Pencil 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 60, "s": [-90]}, {"t": 120, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 0, "s": [68.539, 361.466, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 60, "s": [372.539, 361.466, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [68.539, 361.466, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-152.067, 152.073, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.55, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [-30.179, 0.176], [0, 0], [0, 0], [35.289, 0.215], [0, 0]], "o": [[0, 0], [34.522, -0.201], [0, 0], [0, 0], [-37.012, -0.225], [0, 0]], "v": [[-40, 135], [1.316, 135.177], [40, 135], [40, -135], [2.317, -135], [-40, -135]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.45, "y": 0}, "t": 30, "s": [{"i": [[0, 0], [-24.619, -0.217], [0, 0], [0, 0], [31.876, -0.043], [0, 0]], "o": [[0, 0], [25.408, 0.224], [0, 0], [0, 0], [-36.289, 0.049], [0, 0]], "v": [[-30.317, 154.378], [1.724, 170.295], [30.319, 154.378], [39.423, -21.846], [1.987, -0.76], [-39.04, -21.846]], "c": true}]}, {"i": {"x": 0.55, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [-30.179, 0.176], [0, 0], [0, 0], [35.289, 0.215], [0, 0]], "o": [[0, 0], [34.522, -0.201], [0, 0], [0, 0], [-37.012, -0.225], [0, 0]], "v": [[-40, 135], [1.316, 135.177], [40, 135], [40, -135], [2.317, -135], [-40, -135]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.45, "y": 0}, "t": 90, "s": [{"i": [[0, 0], [-34.384, -0.303], [0, 0], [0, 0], [30.616, -0.036], [0, 0]], "o": [[0, 0], [35.487, 0.313], [0, 0], [0, 0], [-34.855, 0.041], [0, 0]], "v": [[-42.26, 130.459], [2.396, 101.347], [42.43, 130.46], [36.682, -25.376], [1.831, -54.115], [-35.681, -25.388]], "c": true}]}, {"t": 120, "s": [{"i": [[0, 0], [-30.179, 0.176], [0, 0], [0, 0], [35.289, 0.215], [0, 0]], "o": [[0, 0], [34.522, -0.201], [0, 0], [0, 0], [-37.012, -0.225], [0, 0]], "v": [[-40, 135], [1.316, 135.177], [40, 135], [40, -135], [2.317, -135], [-40, -135]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.55, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-39.907, 135.685], [0.004, 215.06], [39.821, 135.685]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.45, "y": 0}, "t": 30, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-30.361, 154.423], [0.004, 215.06], [30.275, 154.423]], "c": false}]}, {"i": {"x": 0.55, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-39.907, 135.685], [0.004, 215.06], [39.821, 135.685]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.45, "y": 0}, "t": 90, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-42.161, 131.184], [0.089, 215.212], [42.24, 131.185]], "c": false}]}, {"t": 120, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-39.907, 135.685], [0.004, 215.06], [39.821, 135.685]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 45, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 60, "s": [-90]}, {"t": 120, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 0, "s": [68.539, 361.466, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 60, "s": [372.539, 361.466, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [68.539, 361.466, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-152.067, 152.073, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.55, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [-30.179, 0.176], [0, 0], [0, 0], [35.289, 0.215], [0, 0]], "o": [[0, 0], [34.522, -0.201], [0, 0], [0, 0], [-37.012, -0.225], [0, 0]], "v": [[-40, 135], [1.316, 135.177], [40, 135], [40, -135], [2.317, -135], [-40, -135]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.45, "y": 0}, "t": 30, "s": [{"i": [[0, 0], [-24.619, -0.217], [0, 0], [0, 0], [31.876, -0.043], [0, 0]], "o": [[0, 0], [25.408, 0.224], [0, 0], [0, 0], [-36.289, 0.049], [0, 0]], "v": [[-30.317, 154.378], [1.724, 170.295], [30.319, 154.378], [39.423, -21.846], [1.987, -0.76], [-39.04, -21.846]], "c": true}]}, {"i": {"x": 0.55, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [-30.179, 0.176], [0, 0], [0, 0], [35.289, 0.215], [0, 0]], "o": [[0, 0], [34.522, -0.201], [0, 0], [0, 0], [-37.012, -0.225], [0, 0]], "v": [[-40, 135], [1.316, 135.177], [40, 135], [40, -135], [2.317, -135], [-40, -135]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.45, "y": 0}, "t": 90, "s": [{"i": [[0, 0], [-34.384, -0.303], [0, 0], [0, 0], [30.616, -0.036], [0, 0]], "o": [[0, 0], [35.487, 0.313], [0, 0], [0, 0], [-34.855, 0.041], [0, 0]], "v": [[-42.26, 130.459], [2.396, 101.347], [42.43, 130.46], [36.682, -25.376], [1.831, -54.115], [-35.681, -25.388]], "c": true}]}, {"t": 120, "s": [{"i": [[0, 0], [-30.179, 0.176], [0, 0], [0, 0], [35.289, 0.215], [0, 0]], "o": [[0, 0], [34.522, -0.201], [0, 0], [0, 0], [-37.012, -0.225], [0, 0]], "v": [[-40, 135], [1.316, 135.177], [40, 135], [40, -135], [2.317, -135], [-40, -135]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.55, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-39.907, 135.685], [0.004, 215.06], [39.821, 135.685]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.45, "y": 0}, "t": 30, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-30.361, 154.423], [0.004, 215.06], [30.275, 154.423]], "c": false}]}, {"i": {"x": 0.55, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-39.907, 135.685], [0.004, 215.06], [39.821, 135.685]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.45, "y": 0}, "t": 90, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-42.161, 131.184], [0.089, 215.212], [42.24, 131.185]], "c": false}]}, {"t": 120, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-39.907, 135.685], [0.004, 215.06], [39.821, 135.685]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 45, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Pencil", "tt": 2, "tp": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 60, "s": [-90]}, {"t": 120, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 0, "s": [68.539, 361.466, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 60, "s": [372.539, 361.466, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [68.539, 361.466, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-152.067, 152.073, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.55, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [-37.423, -0.254], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [34.524, 0.234], [0, 0], [0, 0]], "v": [[-40, 25], [-40, -25], [2.237, -25.088], [40, -25], [40, 25]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.45, "y": 0}, "t": 30, "s": [{"i": [[0, 0], [0, 0], [-38.998, 0.537], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [35.498, -0.488], [0, 0], [0, 0]], "v": [[-39.043, 138.158], [-39.806, 88], [2.208, 109.499], [40.194, 88], [39.42, 138.158]], "c": false}]}, {"i": {"x": 0.55, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [0, 0], [-37.423, -0.254], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [34.524, 0.234], [0, 0], [0, 0]], "v": [[-40, 25], [-40, -25], [2.237, -25.088], [40, -25], [40, 25]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.45, "y": 0}, "t": 90, "s": [{"i": [[0, 0], [0, 0], [-31.084, 0.428], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [28.294, -0.389], [0, 0], [0, 0]], "v": [[-35.689, 134.625], [-31.63, 88.008], [1.739, 63.698], [32.134, 88.008], [36.674, 134.637]], "c": false}]}, {"t": 120, "s": [{"i": [[0, 0], [0, 0], [-37.423, -0.254], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [34.524, 0.234], [0, 0], [0, 0]], "v": [[-40, 25], [-40, -25], [2.237, -25.088], [40, -25], [40, 25]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [113.393, -113.198], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 45, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_7", "nm": "mask-h-l", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [33.588, -33.588, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.51, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -47.5], [0, 47.5]], "c": false}]}, {"i": {"x": 0.38, "y": 1}, "o": {"x": 0.49, "y": 0}, "t": 16, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.334, 1.512], [0.875, 79.519]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.62, "y": 0}, "t": 30, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -47.5], [0, 47.5]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -47.5], [0, 47.5]], "c": false}]}, {"i": {"x": 0.38, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 47, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.334, 1.512], [0.875, 79.519]], "c": false}]}, {"t": 61, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -47.5], [0, 47.5]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 45, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Pencil", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.524], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.703], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [-90]}, {"i": {"x": [0.268], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 38, "s": [-90]}, {"i": {"x": [0.477], "y": [1]}, "o": {"x": [0.417], "y": [0]}, "t": 66, "s": [5]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.524, "y": 0}, "t": 0, "s": [68.539, 361.466, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.703, "y": 0.703}, "o": {"x": 0.333, "y": 0.333}, "t": 30, "s": [359.539, 363.466, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.268, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [359.539, 363.466, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.477, "y": 1}, "o": {"x": 0.417, "y": 0}, "t": 66, "s": [47.539, 361.466, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [68.539, 361.466, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-152.067, 152.073, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.51, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40, 135], [40, 135], [40, -135], [-40, -135]], "c": true}]}, {"i": {"x": 0.38, "y": 1}, "o": {"x": 0.49, "y": 0}, "t": 16, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40, 135], [40, 135], [40.094, -79.491], [-39.907, -79.491]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.62, "y": 0}, "t": 30, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40, 135], [40, 135], [40, -135], [-40, -135]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40, 135], [40, 135], [40, -135], [-40, -135]], "c": true}]}, {"i": {"x": 0.38, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 47, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40, 135], [40, 135], [40.094, -79.491], [-39.907, -79.491]], "c": true}]}, {"t": 61, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40, 135], [40, 135], [40, -135], [-40, -135]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.51, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-40.058, 135.06], [0.004, 215.06], [39.942, 135.06]], "c": false}]}, {"i": {"x": 0.38, "y": 1}, "o": {"x": 0.49, "y": 0}, "t": 16, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-40.058, 135.06], [0.004, 215.06], [39.942, 135.06]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.62, "y": 0}, "t": 30, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-40.058, 135.06], [0.004, 215.06], [39.942, 135.06]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-40.058, 135.06], [0.004, 215.06], [39.942, 135.06]], "c": false}]}, {"i": {"x": 0.38, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 47, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-40.058, 135.06], [0.004, 215.06], [39.942, 135.06]], "c": false}]}, {"t": 61, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-40.058, 135.06], [0.004, 215.06], [39.942, 135.06]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 45, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.51, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40, 25], [-40, -25], [40, -25], [40, 25]], "c": false}]}, {"i": {"x": 0.38, "y": 1}, "o": {"x": 0.49, "y": 0}, "t": 16, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-39.907, 80.509], [-40.029, 38.008], [39.971, 38.008], [40.094, 80.509]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.62, "y": 0}, "t": 30, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40, 25], [-40, -25], [40, -25], [40, 25]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40, 25], [-40, -25], [40, -25], [40, 25]], "c": false}]}, {"i": {"x": 0.38, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 47, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-39.907, 80.509], [-40.029, 38.008], [39.971, 38.008], [40.094, 80.509]], "c": false}]}, {"t": 61, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-40, 25], [-40, -25], [40, -25], [40, 25]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [113.393, -113.198], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 45, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Shape Layer 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[146, 149], [-168, 149]], "c": false}]}, {"t": 65, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[146, 149], [-168, 149]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.697], "y": [0.121]}, "o": {"x": [0.406], "y": [0]}, "t": 38, "s": [0]}, {"i": {"x": [0.585], "y": [0.617]}, "o": {"x": [0.288], "y": [0.358]}, "t": 44, "s": [19.268]}, {"i": {"x": [0.615], "y": [0.819]}, "o": {"x": [0.287], "y": [0.553]}, "t": 50, "s": [64.215]}, {"i": {"x": [0.631], "y": [1]}, "o": {"x": [0.3], "y": [0.504]}, "t": 58, "s": [92.96]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 65, "s": [100]}, {"t": 76, "s": [98]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.41], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 45, "s": [0]}, {"t": 81, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-35-edit').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 38, "op": 77, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "stroke", "np": 3, "mn": "Pseudo/@@bJ/MfWAWT8GP3HAe3C1BcQ", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "<PERSON><PERSON>", "mn": "Pseudo/@@bJ/MfWAWT8GP3HAe3C1BcQ-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}, {"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.839, 0.929, 0.09], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.808, 0.808, 0.808], "ix": 1}}]}], "ip": 0, "op": 391, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "hover-circle", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 130, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-circle", "dr": 120}], "props": {}}