#!/bin/bash
set -e  # Exit on any error

rm -rf lib/api

# Step 1: Run swagger_parser first to generate all the enum types and basic models
echo "Running swagger_parser..."
dart run swagger_parser

# Step 2: Run build_runner to generate .g.dart files for all API models BEFORE replacing anything
echo "Generating JSON serialization for API models..."
dart run build_runner build --delete-conflicting-outputs --build-filter="lib/api/**.dart"

# Step 3: Build our Freezed files that depend on the generated enums
echo "Building Freezed files..."
dart run build_runner build --delete-conflicting-outputs --build-filter="lib/freezed/**.dart"

# Step 4: Remove the generated currency grants files
echo "Removing generated currency grants files..."
rm -f lib/api/models/currency_grants.dart
rm -f lib/api/models/currency_grant.dart
rm -f lib/api/models/currency_grant.g.dart

# Step 5: Create symlinks to our Freezed implementations
# Why are they "../../" ? Because they're RELATIVE to the destination.
echo "Creating symlinks to Freezed implementations..."
ln -sf ../../freezed/currency_grants.dart lib/api/models/currency_grants.dart
ln -sf ../../freezed/currency_grant.dart lib/api/models/currency_grant.dart
ln -sf ../../freezed/currency_grant.g.dart lib/api/models/currency_grant.g.dart
ln -sf ../../freezed/currency_grant.freezed.dart lib/api/models/currency_grant.freezed.dart

# Step 6: Verify the changes
echo "Verifying changes..."
if [ -L "lib/api/models/currency_grants.dart" ] && \
   [ -L "lib/api/models/currency_grant.dart" ] && \
   [ -L "lib/api/models/currency_grant.g.dart" ]; then
  echo "✅ Symlinks created successfully"
else
  echo "❌ Error: Symlinks not created properly"
  exit 1
fi

# Step 7: Find and modify files containing updatedCurrencyBalance
echo "Finding and modifying currency response files..."
FILES_TO_MODIFY=$(find lib/api -type f -name "*.dart" ! -name "*.g.dart" -exec grep -l "required this.updatedCurrencyBalance" {} \;)

if [ -n "$FILES_TO_MODIFY" ]; then
  for file in $FILES_TO_MODIFY; do
    echo "Modifying $file to implement BaseCurrencyResponse..."
    # Add import if not exists
    if ! grep -q "import.*base_currency_response.dart" "$file"; then
      sed -i '' '1i\
import "./base_currency_response.dart";
' "$file"
    fi
    # Add implements clause if not exists
    sed -i '' 's/class \([^{]*\){/class \1 implements BaseCurrencyResponse {/' "$file"
  done
fi

# Step 8: Handle base_currency_response files
echo "Setting up base_currency_response symlinks..."
rm -f lib/api/models/base_currency_response.dart
rm -f lib/api/models/base_currency_response.g.dart
ln -sf ../../freezed/base_currency_response.dart lib/api/models/base_currency_response.dart

echo "Done! API client generated with custom Freezed implementation and currency response modifications."
