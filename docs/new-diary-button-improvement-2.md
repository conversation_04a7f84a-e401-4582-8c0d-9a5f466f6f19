At the bottom of our HomePageWidget (home_page_widget.dart) is our note writing button (note_write_button.dart)
There's an improvement we need to make.
It needs to take two more callbacks, "onTapNeedSubscription" and "onTapLoggedOut".
In the noteWriteButtonModelQuery, we also need to call fetchUserInfoQuery.
If the SubscriptionStatus is "Expired", instead of calling onTapEnabled, onTapNeedSubscription should be called.
If the user is not logged in, 1. The submit button should always be shown (regardless of current entry date) 2. The onTapLoggedOut callback should be used.
The NoteWriteButtonModel should be modified accordingly.