At the bottom of our HomePageWidget (home_page_widget.dart) is our note writing button.
We're going to separate this out and improve it.

Goals:

- Move the button related code into a new file, a single class NoteWriteButtonWidget.
- This should be wrapped in a QueryBuilder that uses the key of the canWriteNewDiaryQuery (note_query.dart). This string can be used for the "query" argument. 
Example snippet from a different app:
```dart
return QueryBuilder<SubscribeButtonModelData>(
      // Can use key if the query already exists.
      queryKey: subscribeButtonModelKey(id, 1),
      builder: (context, state) {
        final data = state.data;    
        if (state.error != null) return Text(state.error.toString());
        if (data == null) return const SizedBox();
        final val1 = data.entryDate;
        final val2 = data.subscriptionStatus;
        return Container(
          margin: const EdgeInsets.all(10),
          child: Column(
            children: [
              const Text(
                "Title",
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 20),
              ),
              Text(
                '$val1',
                textAlign: TextAlign.center,
              ),
              Text(
                '$val2',
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );

Query<SubscribeButtonModelData> getSubscribeButtonModelData(
      int val1, int val2) {
    return Query<SubscribeButtonModelData>(
      key: subscribeButtonModelKey(val1, val2),
      queryFn: () async {
        final q1 = getNoteDetail(val1);
        final q2 = getEntryDate(val2);
        final results = await Future.wait([q1.result, q2.result]);
        final res1 = results[0].data!;
        final res2 = results[1].data!;
        return SubscribeButtonModelData(
            entryDate: res1, subscriptionStatus: res2);
      },
    );
  }
}

String noteDetailKey(int id) => "get-value-$id";
String entryDateKey(int id) => "get-another-value-$id";
String subscribeButtonModelKey(int val1, int val2) =>
    "$noteDetailKey($val1)-$entryDateKey($val2)";

class SubscribeButtonModelData {
  final int entryDate;
  final String subscriptionStatus;

  SubscribeButtonModelData({
    required this.entryDate,
    required this.subscriptionStatus,
  });
}
```
- The query should not be checked during tap but already before returning the widget. This is because if its return value is true, the design of the LDButton of
what is currently called "_submitButtonBuilder" should be used; otherwise the one of the "_enableSubmitButtonBuilder" should be used.
- Instead of just an "onTap", receive "onTapEnabled". 
- Do not yet use this new widget in the _HomePageWidgetState, the focus is on creating a good widget.