At the bottom of our HomePageWidget (home_page_widget.dart) is our note writing button.
There's an improvement we need to make.
The day for which a user can potentially write a note is the one returned by ApiNoteRepository.getCurrentEntryDate.
Currently if the user has selected a day in the past or future (i.e. it does not equal the date returned by the ApiNoteRepository.getCurrentEntryDate), the "done" button is shown.
Instead, in this case, no button should be shown at all.

This means we need separate queries for both getCurrentEntryDate and checkIfNoteExists and then one more query that brings those together. 
Then each result is used as the data model for the button widget. This data model can be used to derive the same result as "NoteService.canWriteNewDiary". 
Then we can use the following logic:
- If the user has selected he "current entry date", and has not yet written a diary, the appropriate button is shown
- If the user has selected he "current entry date", and has already written a diary, the done button is shown
- If the user has selected a different date from the "current entry date", no widget is shown

Example snippet from a different app of combining multiple queries and using it in data model for a widget:
```dart
return QueryBuilder<SubscribeButtonModelData>(
      // Can use key if the query already exists.
      queryKey: subscribeButtonModelKey(id, 1),
      builder: (context, state) {
        final data = state.data;    
        if (state.error != null) return Text(state.error.toString());
        if (data == null) return const SizedBox();
        final val1 = data.entryDate;
        final val2 = data.subscriptionStatus;
        return Container(
          margin: const EdgeInsets.all(10),
          child: Column(
            children: [
              const Text(
                "Title",
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 20),
              ),
              Text(
                '$val1',
                textAlign: TextAlign.center,
              ),
              Text(
                '$val2',
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );

Query<SubscribeButtonModelData> getSubscribeButtonModelData(
      int val1, int val2) {
    return Query<SubscribeButtonModelData>(
      key: subscribeButtonModelKey(val1, val2),
      queryFn: () async {
        final q1 = getNoteDetail(val1);
        final q2 = getEntryDate(val2);
        final results = await Future.wait([q1.result, q2.result]);
        final res1 = results[0].data!;
        final res2 = results[1].data!;
        return SubscribeButtonModelData(
            entryDate: res1, subscriptionStatus: res2);
      },
    );
  }
}

String noteDetailKey(int id) => "get-value-$id";
String entryDateKey(int id) => "get-another-value-$id";
String subscribeButtonModelKey(int val1, int val2) =>
    "$noteDetailKey($val1)-$entryDateKey($val2)";

class SubscribeButtonModelData {
  final int entryDate;
  final String subscriptionStatus;

  SubscribeButtonModelData({
    required this.entryDate,
    required this.subscriptionStatus,
  });
}
```